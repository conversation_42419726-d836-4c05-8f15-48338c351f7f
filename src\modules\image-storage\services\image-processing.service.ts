import { Injectable, Logger } from '@nestjs/common';
import { ImageStorageType } from '../dto/upload-image.dto';
// import { ImageProcessingResult } from '../interfaces/image-storage.interface';

@Injectable()
export class ImageProcessingService {
  private readonly logger = new Logger(ImageProcessingService.name);

  processImage(imageBuffer: Buffer, type: ImageStorageType) {
    try {
      this.logger.log(
        `Processing image of type: ${type}, size: ${imageBuffer.length} bytes`,
      );

      // Simply return the original buffer without any processing
      // No optimization, no thumbnail generation, no resizing
      return {
        originalBuffer: imageBuffer,
        thumbnailBuffer: imageBuffer, // Use same buffer as thumbnail
        optimizedBuffer: imageBuffer,
        sizes: undefined, // No additional sizes
      };
    } catch (error) {
      this.logger.error('Image processing failed:', error);
      throw new Error(`Image processing failed: ${error.message}`);
    }
  }

  /**
   * Validate image format (keep this for basic validation)
   */
  isValidImageFormat(buffer: Buffer): Promise<boolean> {
    try {
      // Basic validation by checking file signatures (magic numbers)
      const validSignatures = [
        [0xff, 0xd8, 0xff], // JPEG
        [0x89, 0x50, 0x4e, 0x47], // PNG
        [0x47, 0x49, 0x46], // GIF
        [0x52, 0x49, 0x46, 0x46], // WebP (starts with RIFF)
      ];

      const fileHeader = Array.from(buffer.subarray(0, 4));

      const isValid = validSignatures.some((signature) =>
        signature.every((byte, index) => fileHeader[index] === byte),
      );

      return Promise.resolve(isValid);
    } catch (error) {
      this.logger.error('Image format validation failed:', error);
      return Promise.resolve(false);
    }
  }

  /**
   * Get basic image info without using Sharp
   */
  getImageDimensions() {
    // Return default dimensions since we're not processing
    // In a real scenario, you might want to read EXIF data or use a lightweight image parser
    return {
      width: 0,
      height: 0,
    };
  }
}
