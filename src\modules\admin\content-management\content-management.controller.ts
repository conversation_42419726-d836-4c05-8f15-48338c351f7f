import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  Query,
  Param,
  Put,
} from '@nestjs/common';
import { ContentManagementService } from './content-management.service';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { CreateAuthorDto } from './dtos/create-author.dto';
import { Roles } from '../../auth/decorators/role.decorator';
import { AdminRole } from '../../../common/role.enum';
import { CreateGenreDto } from './dtos/create-genre.dto';
import { DeleteGenreDto } from './dtos/delete-genre.dto';
import { CreateContentDto } from './dtos/create-content.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { CreateEpisodeDto } from './dtos/create-episode.dto';
import { FilterContentDto } from './dtos/filter-content.dto';
import { UpdateContentDto } from './dtos/update-content.dto';
import { UpdateEpisodeDto } from './dtos/update-episode.dto';
import { FilterAuthorDto } from './dtos/filter-author.dto';
import { UpdateAuthorDto } from './dtos/update-author.dto';
import { JwtAdminAuthGuard } from '../auth-admin/guards/jwt-admin-auth.guard';
import { GetEpisodeDto } from './dtos/get-episode.dto';

@Controller('content-management')
@ApiTags('Content Management')
@ApiBearerAuth()
@UseGuards(JwtAdminAuthGuard)
@Roles(AdminRole.ADMIN, AdminRole.SUPER_ADMIN)
export class ContentManagementController {
  constructor(
    private readonly contentManagementService: ContentManagementService,
  ) {}
  @Get('contents')
  @ApiOperation({ summary: 'Filter content' })
  async filterContent(@Query() filterDto: FilterContentDto) {
    return this.contentManagementService.filterContent(filterDto);
  }

  @Put('update-content/:id')
  @ApiOperation({ summary: 'Update content' })
  async updateContent(
    @Param('id') id: number,
    @Body() updateContentDto: UpdateContentDto,
  ) {
    return await this.contentManagementService.updateContent(
      id,
      updateContentDto,
    );
  }

  @Post('create-author')
  @ApiOperation({ summary: 'Create author' })
  async createAuthor(@Body() createAuthorDto: CreateAuthorDto) {
    return this.contentManagementService.createAuthor(createAuthorDto);
  }

  @Post('create-genre')
  @ApiOperation({ summary: 'Create genre' })
  async createGenre(@Body() createGenreDto: CreateGenreDto) {
    return this.contentManagementService.createGenre(createGenreDto);
  }

  @Delete('delete-genre')
  @ApiOperation({ summary: 'Delete genre' })
  deleteGenre(@Body() deleteGenreDto: DeleteGenreDto) {
    return this.contentManagementService.deleteGenre(deleteGenreDto);
  }

  @Get('authors')
  @ApiOperation({ summary: 'Get all authors' })
  async getAuthors() {
    return this.contentManagementService.getAuthors();
  }

  @Get('author/:id')
  @ApiOperation({ summary: 'Get author by id' })
  async getAuthor(@Param('id') id: number) {
    return await this.contentManagementService.getAuthor(id);
  }

  @Get('authors-filter')
  @ApiOperation({ summary: 'Get all authors with filter' })
  async getAuthorsFilter(@Query() filterDto: FilterAuthorDto) {
    return await this.contentManagementService.getAuthorsFilter(filterDto);
  }

  @Get('genres')
  @ApiOperation({ summary: 'Get all genres' })
  async getGenres() {
    return this.contentManagementService.getGenres();
  }

  @Post('create-content')
  @ApiOperation({ summary: 'Create content' })
  async createContent(@Body() createContentDto: CreateContentDto) {
    return await this.contentManagementService.createContent(createContentDto);
  }

  @Post('upload-image')
  @ApiOperation({ summary: 'Upload image' })
  @UseInterceptors(
    FileInterceptor('image', { limits: { fileSize: 50 * 1024 * 1024 } }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        image: { type: 'string', format: 'binary' },
      },
    },
  })
  uploadImage(@UploadedFile() file: any) {
    return this.contentManagementService.uploadImage(file);
  }

  @Post('create-episode')
  @ApiOperation({ summary: 'Create episode' })
  async createEpisode(@Body() createEpisodeDto: CreateEpisodeDto) {
    return await this.contentManagementService.createEpisode(createEpisodeDto);
  }

  @Get('next-episode-number')
  @ApiOperation({ summary: 'Get next episode number' })
  @ApiQuery({
    name: 'contentId',
    type: Number,
    required: true,
  })
  async getNextEpisodeNumber(@Query('contentId') contentId: number) {
    return await this.contentManagementService.getNextEpisodeNumber(contentId);
  }

  @Put('update-episode/:id')
  @ApiOperation({ summary: 'Update episode' })
  async updateEpisode(
    @Param('id') id: number,
    @Body() updateEpisodeDto: UpdateEpisodeDto,
  ) {
    return await this.contentManagementService.updateEpisode(
      id,
      updateEpisodeDto,
    );
  }

  @Get('content/:id')
  @ApiOperation({ summary: 'Get content' })
  async getContent(@Param('id') id: number) {
    return await this.contentManagementService.getContent(id);
  }

  @Get('episode/:id')
  @ApiOperation({ summary: 'Get episode' })
  async getEpisode(@Param('id') id: number) {
    return await this.contentManagementService.getEpisode(id);
  }

  @Get('episode-by-content-id')
  @ApiOperation({ summary: 'Get episode by content id' })
  async getEpisodeByContentId(@Query() getEpisodeDto: GetEpisodeDto) {
    return await this.contentManagementService.getEpisodeByContentId(
      getEpisodeDto.contentId,
      getEpisodeDto.page,
      getEpisodeDto.limit,
    );
  }

  @Post('check-author-exist')
  @ApiOperation({ summary: 'Check author exist' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
      },
    },
  })
  async checkAuthorExist(@Body() checkAuthorExistDto: { name: string }) {
    return await this.contentManagementService.checkAuthorExist(
      checkAuthorExistDto.name,
    );
  }

  @Put('update-author/:id')
  @ApiOperation({ summary: 'Update author' })
  async updateAuthor(
    @Param('id') id: number,
    @Body() updateAuthorDto: UpdateAuthorDto,
  ) {
    return await this.contentManagementService.updateAuthor(
      id,
      updateAuthorDto,
    );
  }
}
