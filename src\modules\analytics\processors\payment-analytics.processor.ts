import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job } from 'bullmq';
import {
  PaymentAnalyticsEntity,
  PaymentType,
} from '../../../database/entities/payment-analytics.entity';
import { UserPlatform } from '../../../database/entities/user-analytics.entity';
import { PaymentEventDto } from '../dtos/analytics-events.dto';
import { ANALYTICS_QUEUES } from '../constants/analytics-queues.constant';
import { PurchaseRateService } from '../services/purchase-rate.service';
import { ExchangeRateDbService } from '../services/exchange-rate-db.service';

@Injectable()
@Processor(ANALYTICS_QUEUES.PAYMENT_ANALYTICS)
export class PaymentAnalyticsProcessor extends WorkerHost {
  private readonly logger = new Logger(PaymentAnalyticsProcessor.name);

  constructor(
    @InjectRepository(PaymentAnalyticsEntity)
    private paymentAnalyticsRepository: Repository<PaymentAnalyticsEntity>,
    private purchaseRateService: PurchaseRateService,
    private exchangeRateDbService: ExchangeRateDbService,
  ) {
    super();
  }

  async process(job: Job<PaymentEventDto>): Promise<void> {
    const { data } = job;
    this.logger.debug(`Processing payment event: ${JSON.stringify(data)}`);

    try {
      const paymentDate = new Date(data.timestamp);
      const date = new Date(
        paymentDate.getFullYear(),
        paymentDate.getMonth(),
        paymentDate.getDate(),
      );
      const hour = paymentDate.getHours();

      let analytics = await this.paymentAnalyticsRepository.findOne({
        where: {
          date,
          hour,
          paymentType: data.paymentType as PaymentType,
          platform: data.platform as UserPlatform,
          currency: data.currency,
          language: data.language || 'en',
          region: data.region || 'US',
        },
      });

      if (!analytics) {
        // Get exchange rate and purchase rate data
        const exchangeRateEntity =
          await this.exchangeRateDbService.findExchangeRate(
            data.currency,
            data.language,
            data.region,
            date,
          );
        const purchaseRateData =
          await this.purchaseRateService.getRealTimePurchaseRates();

        analytics = this.paymentAnalyticsRepository.create({
          date,
          hour,
          paymentType: data.paymentType as PaymentType,
          platform: data.platform as UserPlatform,
          currency: data.currency,
          language: data.language || 'en',
          region: data.region || 'US',
          exchangeRateId: exchangeRateEntity?.id,
          totalRequests: 0,
          successfulPayments: 0,
          failedPayments: 0,
          totalAmount: 0.0,
          totalAmountKRW: 0.0,
          uniqueUsers: 0,
          paymentAttempts: 0,
          totalRegisteredUsers: purchaseRateData.totalRegisteredUsers,
          firstPurchaseRate: purchaseRateData.firstPurchaseRate,
          repurchaseRate: purchaseRateData.repurchaseRate,
          totalPurchaseRate: purchaseRateData.totalPurchaseRate,
          metadata: {},
        });
      }

      // Update metrics based on job type
      if (job.name === 'payment-attempt') {
        analytics.paymentAttempts += 1;
      } else {
        analytics.totalRequests += 1;

        // Convert amount to KRW
        const amountKRW =
          data.amountKRW ||
          (await this.exchangeRateDbService.convertToKRW(
            Number(data.amount),
            data.currency,
            data.language,
            data.region,
            date,
          ));

        switch (data.status) {
          case 'success': {
            analytics.successfulPayments += 1;
            const amountToAdd = Number(data.amount);

            // Debug logging
            this.logger.debug(
              `Before update: totalAmount=${analytics.totalAmount}, adding=${amountToAdd}, type=${typeof analytics.totalAmount}`,
            );

            analytics.totalAmount = Number(analytics.totalAmount) + amountToAdd;
            analytics.totalAmountKRW =
              Number(analytics.totalAmountKRW) + amountKRW;

            this.logger.debug(
              `After update: totalAmount=${analytics.totalAmount}, totalAmountKRW=${analytics.totalAmountKRW}`,
            );
            break;
          }
          case 'failed':
            analytics.failedPayments += 1;
            break;
        }
      }

      // Update unique users
      await this.updateUniqueUsers(analytics, data.userId);

      await this.paymentAnalyticsRepository.save(analytics);

      this.logger.debug(`Updated payment analytics for ${data.paymentType}`);
    } catch (error) {
      this.logger.error(
        `Error processing payment event: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private async updateUniqueUsers(
    analytics: PaymentAnalyticsEntity,
    userId: number,
  ): Promise<void> {
    // Check if user already made payment in this hour
    const existingPayment = await this.paymentAnalyticsRepository.query(
      `
      SELECT COUNT(*) as count 
      FROM payments p 
      WHERE p.userId = ? 
        AND DATE(p.createdAt) = ?
        AND HOUR(p.createdAt) = ?
    `,
      [userId, analytics.date, analytics.hour],
    );

    if (existingPayment[0].count === 0) {
      analytics.uniqueUsers += 1;
    }
  }
}
