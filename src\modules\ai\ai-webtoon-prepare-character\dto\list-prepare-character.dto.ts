import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { PaginationDto } from 'src/modules/notification/dtos/pagination.dto';

export enum SearchBy {
  CHARACTER_NAME = 'character_name',
  ADMIN_NAME = 'admin_name',
}

export enum SearchGender {
  ALL = 'all',
  FEMALE = 'female',
  MALE = 'male',
}

export class ListWebtoonAuthorsDto extends PaginationDto {
  @ApiPropertyOptional({ enum: SearchGender })
  @IsEnum(SearchGender)
  @IsOptional()
  gender: SearchGender;

  @ApiPropertyOptional({ example: '2023-03-27', required: false })
  @IsString()
  @IsOptional()
  fromTime: string;

  @ApiPropertyOptional({ example: '2023-03-27', required: false })
  @IsString()
  @IsOptional()
  toTime: string;

  @ApiPropertyOptional({ enum: SearchBy, required: false })
  @IsOptional()
  @IsEnum(SearchBy)
  searchBy: SearchBy;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }: { value: string }) => value?.trim())
  search: string;
}
