import {
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  IsNotEmpty,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON>ptional,
  IsString,
} from 'class-validator';
import {
  EpisodeBackgroundColors,
  PaymentType,
  PaymentUnitType,
} from '../../../../common/setting.enum';
import { ApiProperty } from '@nestjs/swagger';

export class CreateEpisodeDto {
  @ApiProperty({
    description: 'Episode number',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  episodeNumber: number;

  @ApiProperty({
    description: 'Episode string',
    example: 'Episode 1',
    required: false,
  })
  @IsOptional()
  @IsString()
  episodeString: string;

  @ApiProperty({
    description: 'Content ID',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  contentId: number;

  @ApiProperty({
    description: 'Title',
    example: 'Episode 1',
  })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Expected release date',
    example: '2025-01-01',
  })
  @IsNotEmpty()
  @IsString()
  expectedReleaseDate: string;

  @ApiProperty({
    description: 'Background color',
    example: EpisodeBackgroundColors.WHITE,
  })
  @IsNotEmpty()
  @IsString()
  bgColor: EpisodeBackgroundColors;

  @ApiProperty({
    description: 'Thumbnail',
    example: '/images/episode/thumbnail.jpg',
  })
  @IsNotEmpty()
  @IsString()
  thumbnail: string;

  @ApiProperty({
    description: 'None user viewing',
    example: false,
  })
  @IsNotEmpty()
  @IsBoolean()
  noneUserViewing: boolean;

  @ApiProperty({
    description: 'Images',
    example: [{ path: '/images/episode/image.jpg' }],
  })
  @IsNotEmpty()
  @IsArray()
  images: {
    path: string;
  }[];

  @ApiProperty({
    description: 'Payment type',
    example: PaymentType.FREE,
  })
  @IsOptional()
  @IsString()
  paymentType: PaymentType;

  @ApiProperty({
    description: 'Payment unit',
    example: PaymentUnitType.KRW,
  })
  @IsOptional()
  @IsString()
  paymentUnit: PaymentUnitType;

  @ApiProperty({
    description: 'Payment amount',
    example: 1000,
  })
  @IsOptional()
  @IsNumber()
  paymentAmount: number;
}
