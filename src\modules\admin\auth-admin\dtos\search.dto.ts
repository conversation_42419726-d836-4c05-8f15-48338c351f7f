import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
export class SearchAdminDto {
  @ApiProperty({
    example: 'john',
    description: 'type search admin',
    required: false,
  })
  @IsString()
  @IsOptional()
  search: string;

  @ApiProperty({ example: 1, description: 'page number', required: false })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  page: number;

  @ApiProperty({
    example: 10,
    description: 'limit number',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit: number;
}
