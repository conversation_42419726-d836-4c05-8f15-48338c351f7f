import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserSanction1748969930549 implements MigrationInterface {
  name = 'UpdateUserSanction1748969930549';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP FOREIGN KEY \`FK_38a664c1a40c384e79aa19fc71d\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_9fd33f14c5f0eb084160ff5757\` ON \`sanction\``,
    );
    await queryRunner.query(
      `DROP INDEX \`REL_38a664c1a40c384e79aa19fc71\` ON \`users\``,
    );
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`sanctionId\``);
    await queryRunner.query(`ALTER TABLE \`sanction\` ADD \`userId\` int NULL`);
    await queryRunner.query(
      `ALTER TABLE \`sanction\` ADD CONSTRAINT \`FK_e57cbb733f96e9892197c350319\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`sanction\` DROP FOREIGN KEY \`FK_e57cbb733f96e9892197c350319\``,
    );
    await queryRunner.query(`ALTER TABLE \`sanction\` DROP COLUMN \`userId\``);
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`sanctionId\` int NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`REL_38a664c1a40c384e79aa19fc71\` ON \`users\` (\`sanctionId\`)`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`IDX_9fd33f14c5f0eb084160ff5757\` ON \`sanction\` (\`adminId\`)`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD CONSTRAINT \`FK_38a664c1a40c384e79aa19fc71d\` FOREIGN KEY (\`sanctionId\`) REFERENCES \`sanction\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
