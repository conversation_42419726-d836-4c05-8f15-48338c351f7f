import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength } from 'class-validator';

export class UserUploadDocumentDto {
  @ApiProperty({
    description: 'The document file to upload',
    type: 'string',
    format: 'binary',
    required: true,
  })
  document: any;

  @ApiProperty({
    description: 'Optional description for the document',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;
}
