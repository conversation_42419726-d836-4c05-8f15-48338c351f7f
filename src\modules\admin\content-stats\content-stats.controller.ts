import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ContentStatsService } from './content-stats.service';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ContentStatsRequestDto } from './dtos/content-stats-request.dto';
import { ContentStatsResponseDto } from './dtos/content-stats-response.dto';
import { Roles } from '../../auth/decorators/role.decorator';
import { AdminRole } from '../../../common/role.enum';
import { JwtAdminAuthGuard } from '../auth-admin/guards/jwt-admin-auth.guard';

@Controller('admin/content-stats')
@ApiTags('Content Statistics')
@ApiBearerAuth()
@UseGuards(JwtAdminAuthGuard)
@Roles(AdminRole.ADMIN, AdminRole.SUPER_ADMIN)
export class ContentStatsController {
  constructor(private readonly contentStatsService: ContentStatsService) {}

  @Get()
  @ApiOperation({
    summary: 'Get content analytics statistics from content_analytics table',
    description: `Get daily content analytics statistics with the following data:
    - Free Episode Views (Adult/Non-Adult) - from content_analytics.viewCount
    - Paid Episode Reads (Adult/Non-Adult) - from content_analytics.viewCount  
    - Combined Statistics by Section (Adult/Non-Adult)
    - Total Combined Views per Day
    
    Data is sourced from the content_analytics table which tracks real-time view events.
    Results are displayed by each day in the date range, with the most recent day shown at the top.
    Supports filtering by language setting and custom date range (defaults to last 30 days).`,
  })
  @ApiResponse({
    status: 200,
    description: 'Content analytics statistics retrieved successfully',
    type: ContentStatsResponseDto,
  })
  async getContentStats(
    @Query() filters: ContentStatsRequestDto,
  ): Promise<ContentStatsResponseDto> {
    return await this.contentStatsService.getContentStats(filters);
  }

  @Get('summary')
  @ApiOperation({
    summary: 'Get content analytics summary with aggregate metrics',
    description: `Get content analytics statistics along with summary information:
    - Daily statistics from content_analytics table
    - Total views across all days
    - Average views per day
    - Top performing day
    - Adult content percentage
    - Total number of days in range`,
  })
  @ApiResponse({
    status: 200,
    description: 'Content analytics summary retrieved successfully',
  })
  async getContentAnalyticsSummary(
    @Query() filters: ContentStatsRequestDto,
  ): Promise<{
    stats: ContentStatsResponseDto;
    summary: {
      totalDays: number;
      totalViews: number;
      averageViewsPerDay: number;
      topViewDay: { date: string; views: number };
      adultContentPercentage: number;
    };
  }> {
    return await this.contentStatsService.getContentAnalyticsSummary(filters);
  }
}
