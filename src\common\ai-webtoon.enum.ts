export enum FILTER_ALL {
  ALL = 'all',
}

export enum EAiWebtoonBasicStatus {
  NOT_GENERATED = 'not_generated',
  GENERATING = 'generating',
  GENERATED = 'generated',
}

export enum EAiWebtoonChapterStatusGenerateImage {
  NOT_GENERATED = 'not_generated',
  GENERATING = 'generating',
  GENERATED = 'generated',
  DONE = 'done',
}

export enum EAiWebtoonCutChapterStatusGenerateImage {
  NOT_GENERATED = 'not_generated',
  WAITING = 'waiting',
  GENERATING = 'generating',
  GENERATED = 'generated',
}

export enum EAiWebtoonLanguageSupport {
  EN = 'English',
  KR = 'Korean',
  VI = 'Vietnamese',
}

export enum EAiLanguageAiChapter {
  EN = 'en',
  KR = 'kr',
  VI = 'vi',
}

export enum EAiWebtoonCharacterStatus {
  NEW = 'new',
  WAITING = 'waiting',
  TRAINING = 'training',
  SYNCING = 'syncing',
  READY = 'ready',
}

export enum EAiWebtoonCharacterGender {
  MALE = 'male',
  FEMALE = 'female',
}

export enum EAiWebtoonTrainingCharacterSessionStatus {
  IN_PROGRESS = 'in_progress',
  CANCEL = 'cancel',
  DONE = 'done',
}

export enum EAiWebtoonCharacterType {
  SDXL = 'sdxl',
  FLUX = 'flux',
  DREAM_BOOTH = 'dream_booth',
  B_LORA_STYLE = 'b_lora_style',
}

export enum EAiWebtoonCharacterTestedType {
  ONE = 'one',
  TWO = 'two',
}

export enum EAiApiGenStateManagementType {
  GEN_IMAGE_FLUX = 'gen_image_flux',
  GEN_BACKGROUND_IMAGE = 'gen_background_image',
  INPAINT_IMAGE = 'inpaint_image',
}

export enum EAiFrameImageGenerateType {
  ONE = 'one',
  MULTIPLE = 'multiple',
}

export enum EAiBubbleType {
  Speech = 'speech',
  Caption = 'caption',
  Circle = 'circle',
  Thought = 'thought',
  Shout = 'shout',
  PointedArcs = 'pointedArcs',
  Ellipse = 'ellipse',
  Rectangle = 'rectangle',
  CaptionWithTail = 'caption-withTail',
}

export enum EAiTextAlignment {
  LEFT = 'left',
  RIGHT = 'right',
  CENTER = 'center',
}

export enum EAiFrameImageSizeType {
  DEFAULT = 'default',
  VERTICAL = 'vertical',
  HORIZONTAL_LEFT = 'horizontal_left',
  HORIZONTAL_RIGHT = 'horizontal_right',
  CONTROLNET_CANNY = 'controlnet_canny',
  CONTROLNET_DEPTH = 'controlnet_depth',
}

export enum EAiBaseModelGenImageType {
  FLUX_NORMAL = 'FLUX_NORMAL',
  FLUX_ADULT = 'FLUX_ADULT',
  SDXL_4_OPT = 'SDXL_4_OPT',
  DREAM_BOOTH = 'DREAM_BOOTH',
}

export enum EAiBaseModelInpaintType {
  FLUX_NORMAL = 'FLUX_NORMAL',
  FLUX_ADULT = 'FLUX_ADULT',
  SDXL_ADULT = 'SDXL_ADULT',
  SDXL_4_OPT = 'SDXL_4_OPT',
}

export enum EAiCutChapterType {
  NORMAL = 'normal',
  INPAINT = 'inpaint',
}

export enum EAiTestingModel {
  NORMAL = 'normal',
  OVER_18 = '18+',
}

export enum EAiTypeActionByNormalCut {
  UPLOAD_IMAGE = 'upload_image',
  GENERATE_IMAGE = 'generate_image',
  INPAINT_IMAGE = 'inpaint_image',
}

export enum EAiLetteringClassificationType {
  ALL = 'all',
  NORMAL = 'normal',
  ADULT = 'adult',
}

export enum EAiGenPromptByDescriptionType {
  GENERAL_PROMPT = 'general_prompt',
  NEGATIVE_PROMPT = 'negative_prompt',
  DANBOORU_PROMPT = 'danbooru_prompt',
}

export enum EDowloadFileGoogoleDriveType {
  DOCX = 'docx',
  PDF = 'pdf',
  TXT = 'txt',
  HTML = 'html',
  XLSX = 'xlsx',
  CSV = 'csv',
  PPTX = 'pptx',
}

export enum EAiBaseModelGenImageTextImportByType {
  'Flux Normal' = EAiBaseModelGenImageType.FLUX_NORMAL,
  'Flux Adult' = EAiBaseModelGenImageType.FLUX_ADULT,
  'Animagine XL 4.0 opt' = EAiBaseModelGenImageType.SDXL_4_OPT,
  'Dream Booth' = EAiBaseModelGenImageType.DREAM_BOOTH,
}

export enum EAiFrameImageSizeTextImportByType {
  'Default' = EAiFrameImageSizeType.DEFAULT,
  'Vertical' = EAiFrameImageSizeType.VERTICAL,
  'Horizontal Right' = EAiFrameImageSizeType.HORIZONTAL_RIGHT,
  'Horizontal Left' = EAiFrameImageSizeType.HORIZONTAL_LEFT,
}

export enum EAiWebtoonStoryBorderType {
  NONE = 'none',
  FULL = 'full',
  LEFT = 'left',
  RIGHT = 'right',
  TOP = 'top',
  BOTTOM = 'bottom',
  TOP_BOTTOM = 'top_bottom',
  LEFT_RIGHT = 'left_right',
}

export enum EAiDirectionApi {
  SENT = 'sent',
  RECEIVED = 'received',
}

export interface ISyntheticDataImagesItem {
  uuid: string;
  image: string;
  prompt?: string;
  resultImages: string[];
}
