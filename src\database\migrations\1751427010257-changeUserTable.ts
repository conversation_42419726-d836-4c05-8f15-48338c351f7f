import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeUserTable1751427010257 implements MigrationInterface {
  name = 'ChangeUserTable1751427010257';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`signInIp\``);
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP COLUMN \`restrictedDate\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP COLUMN \`lastSignInDate\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP COLUMN \`refreshToken\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP COLUMN \`refreshTokenExpiresAt\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`gender\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`agreeUseInfomation\` tinyint NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`agreeUseInfomationAt\` bigint NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`isMatureContent\` tinyint NULL DEFAULT 1`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP COLUMN \`isMatureContent\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP COLUMN \`agreeUseInfomationAt\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP COLUMN \`agreeUseInfomation\``,
    );
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`gender\``);
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`refreshTokenExpiresAt\` datetime NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`refreshToken\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`lastSignInDate\` datetime NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`restrictedDate\` datetime NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`signInIp\` varchar(255) NULL`,
    );
  }
}
