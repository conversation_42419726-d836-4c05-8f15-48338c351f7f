import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsArray } from 'class-validator';

export class UpdateSettingDto {
  @ApiProperty({ description: 'The key of the setting', required: true })
  @IsString()
  @IsNotEmpty()
  key: string;

  @ApiProperty({ description: 'The status of the setting', required: true })
  @IsString()
  @IsNotEmpty()
  status: string;
}

export class UpdateMultipleSettingsDto {
  @ApiProperty({
    description: 'The settings to update',
    required: true,
    example: [
      { key: 'setting1', status: 'active' },
      { key: 'setting2', status: 'inactive' },
    ],
  })
  @IsArray()
  @IsNotEmpty()
  settings: UpdateSettingDto[];
}
