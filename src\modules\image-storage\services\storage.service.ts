import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs/promises';
import * as path from 'path';
import { ImageStorageType } from '../dto/upload-image.dto';
import {
  ImageMetadata,
  ImageProcessingResult,
  StorageResult,
} from '../interfaces/image-storage.interface';

@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);
  private readonly baseUploadPath: string;
  private readonly baseUrl: string;

  constructor(private readonly configService: ConfigService) {
    this.baseUploadPath = this.configService.get<string>(
      'IMAGE_STORAGE_PATH',
      path.join(process.cwd()),
    );
    this.baseUrl = this.configService.get<string>(
      'IMAGE_STORAGE_BASE_URL',
      'http://localhost:3001/images',
    );
  }

  async saveImages(
    uploadId: string,
    processedImages: ImageProcessingResult,
    metadata: ImageMetadata,
  ): Promise<StorageResult> {
    try {
      this.logger.log(`Saving images for upload ${uploadId}`);
      console.log('base upload path:', this.baseUploadPath);
      let originalPath: string;
      let originalUrl: string;
      // console.log('metadata', metadata);
      // If targetPath is provided, use it directly
      if (metadata.targetPath) {
        originalPath = path.join(this.baseUploadPath, metadata.targetPath);
        console.log('Using targetPath:', originalPath);
        // Ensure directory exists
        const targetDir = path.dirname(originalPath);
        await this.ensureDirectoryExists(targetDir);

        // Debug buffer before writing
        console.log('=== STORAGE DEBUG ===');
        console.log('processedImages type:', typeof processedImages);
        console.log(
          'originalBuffer type:',
          typeof processedImages.originalBuffer,
        );
        console.log(
          'originalBuffer is Buffer?',
          Buffer.isBuffer(processedImages.originalBuffer),
        );
        console.log(
          'originalBuffer length:',
          processedImages.originalBuffer?.length || 'undefined',
        );

        if (processedImages.originalBuffer) {
          const bufferToWrite = Buffer.isBuffer(processedImages.originalBuffer)
            ? processedImages.originalBuffer
            : Buffer.from(processedImages.originalBuffer);

          console.log('Buffer to write length:', bufferToWrite.length);
          console.log(
            'First 20 bytes:',
            bufferToWrite.slice(0, 20).toString('hex'),
          );

          // Save image to exact path specified
          await fs.writeFile(originalPath, bufferToWrite);

          // Verify file after writing
          const writtenFile = await fs.readFile(originalPath);
          console.log('Written file size:', writtenFile.length);
          console.log(
            'Written file first 20 bytes:',
            writtenFile.slice(0, 20).toString('hex'),
          );
        } else {
          throw new Error('originalBuffer is undefined or null');
        }
        console.log('======================');

        // Generate URL based on the target path
        originalUrl = `${this.baseUrl}/${metadata.targetPath}`;

        this.logger.log(`Image saved to custom path: ${originalPath}`);

        // Return same URL for thumbnail since we're not generating thumbnails
        return {
          originalUrl,
          thumbnailUrl: originalUrl, // Same as original since no processing
          sizes: undefined,
        };
      } else {
        // Fallback to type-based directory structure
        const typeDirectory = this.getTypeDirectory(metadata.type);
        await this.ensureDirectoryExists(typeDirectory);

        // Generate file names
        const fileName = this.generateFileName(uploadId, metadata);

        // Save original image
        originalPath = path.join(typeDirectory, fileName);

        // Debug buffer before writing (type-based path)
        console.log('=== STORAGE DEBUG (Type-based) ===');
        console.log(
          'processedImages.originalBuffer type:',
          typeof processedImages.originalBuffer,
        );
        console.log(
          'originalBuffer is Buffer?',
          Buffer.isBuffer(processedImages.originalBuffer),
        );
        console.log(
          'originalBuffer length:',
          processedImages.originalBuffer?.length || 'undefined',
        );

        const bufferToWrite = Buffer.isBuffer(processedImages.originalBuffer)
          ? processedImages.originalBuffer
          : Buffer.from(processedImages.originalBuffer);

        await fs.writeFile(originalPath, bufferToWrite);
        originalUrl = this.getFileUrl(metadata.type, fileName);

        this.logger.log(`Image saved to type-based path: ${originalPath}`);

        return {
          originalUrl,
          thumbnailUrl: originalUrl, // Same as original since no processing
          sizes: undefined,
        };
      }
    } catch (error) {
      this.logger.error(`Failed to save images for upload ${uploadId}:`, error);
      throw new Error(`Storage failed: ${error.message}`);
    }
  }

  /**
   * Get directory path based on storage type
   */
  private getTypeDirectory(type: ImageStorageType): string {
    return type;
  }

  /**
   * Generate filename for original image
   */
  private generateFileName(uploadId: string, metadata: ImageMetadata): string {
    const timestamp = Date.now();
    const extension = this.getFileExtension(metadata.mimetype);

    // Use original filename if provided, otherwise use uploadId
    const baseName = metadata.filename
      ? this.sanitizeFileName(metadata.filename.replace(/\.[^/.]+$/, ''))
      : uploadId;

    return `${baseName}_${timestamp}${extension}`;
  }

  /**
   * Generate filename for thumbnail
   */
  private generateThumbnailFileName(
    uploadId: string,
    metadata: ImageMetadata,
  ): string {
    const timestamp = Date.now();
    const baseName = metadata.filename
      ? this.sanitizeFileName(metadata.filename.replace(/\.[^/.]+$/, ''))
      : uploadId;

    return `${baseName}_${timestamp}_thumb.jpg`;
  }

  /**
   * Generate filename for specific size
   */
  private generateSizeFileName(
    uploadId: string,
    metadata: ImageMetadata,
    size: string,
  ): string {
    const timestamp = Date.now();
    const baseName = metadata.filename
      ? this.sanitizeFileName(metadata.filename.replace(/\.[^/.]+$/, ''))
      : uploadId;

    return `${baseName}_${timestamp}_${size}.jpg`;
  }

  /**
   * Get file extension from MIME type
   */
  private getFileExtension(mimetype: string): string {
    const extensions = {
      'image/jpeg': '.jpg',
      'image/jpg': '.jpg',
      'image/png': '.png',
      'image/webp': '.webp',
      'image/gif': '.gif',
    };

    return extensions[mimetype] || '.jpg';
  }

  /**
   * Sanitize filename to prevent path traversal and invalid characters
   */
  private sanitizeFileName(fileName: string): string {
    // Remove or replace invalid characters
    return fileName
      .replace(/[^a-zA-Z0-9_-]/g, '_')
      .replace(/_{2,}/g, '_')
      .replace(/^_+|_+$/g, '')
      .substring(0, 100); // Limit length
  }

  /**
   * Generate public URL for file
   */
  private getFileUrl(type: ImageStorageType, fileName: string): string {
    return `${this.baseUrl}/${type}/${fileName}`;
  }

  /**
   * Ensure directory exists, create if not
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        await fs.mkdir(dirPath, { recursive: true });
        this.logger.log(`Created directory: ${dirPath}`);
      } else {
        throw error;
      }
    }
  }

  /**
   * Delete files associated with an upload
   */
  async deleteUploadFiles(
    uploadId: string,
    metadata: ImageMetadata,
  ): Promise<void> {
    try {
      this.logger.log(`Deleting files for upload ${uploadId}`);

      const typeDirectory = this.getTypeDirectory(metadata.type);
      const fileName = this.generateFileName(uploadId, metadata);
      const thumbnailFileName = this.generateThumbnailFileName(
        uploadId,
        metadata,
      );

      // Delete original file
      const originalPath = path.join(typeDirectory, fileName);
      await this.safeDeleteFile(originalPath);

      // Delete thumbnail
      const thumbnailPath = path.join(
        typeDirectory,
        'thumbnails',
        thumbnailFileName,
      );
      await this.safeDeleteFile(thumbnailPath);

      // Delete size variants
      const sizesDirectory = path.join(typeDirectory, 'sizes');
      const sizeVariants = ['small', 'medium', 'large'];

      for (const size of sizeVariants) {
        const sizeFileName = this.generateSizeFileName(
          uploadId,
          metadata,
          size,
        );
        const sizePath = path.join(sizesDirectory, sizeFileName);
        await this.safeDeleteFile(sizePath);
      }

      this.logger.log(`Files deleted successfully for upload ${uploadId}`);
    } catch (error) {
      this.logger.error(
        `Failed to delete files for upload ${uploadId}:`,
        error,
      );
      // Don't throw error as this is cleanup
    }
  }

  /**
   * Safely delete a file (won't throw if file doesn't exist)
   */
  private async safeDeleteFile(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      if (error.code !== 'ENOENT') {
        this.logger.warn(`Failed to delete file ${filePath}:`, error);
      }
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    typeBreakdown: Record<ImageStorageType, { files: number; size: number }>;
  }> {
    const stats = {
      totalFiles: 0,
      totalSize: 0,
      typeBreakdown: {} as Record<
        ImageStorageType,
        { files: number; size: number }
      >,
    };

    try {
      for (const type of Object.values(ImageStorageType)) {
        const typeDirectory = this.getTypeDirectory(type);
        const typeStats = await this.getDirectoryStats(typeDirectory);

        stats.typeBreakdown[type] = typeStats;
        stats.totalFiles += typeStats.files;
        stats.totalSize += typeStats.size;
      }
    } catch (error) {
      this.logger.error('Failed to get storage stats:', error);
    }

    return stats;
  }

  /**
   * Get statistics for a directory
   */
  private async getDirectoryStats(
    dirPath: string,
  ): Promise<{ files: number; size: number }> {
    const stats = { files: 0, size: 0 };

    try {
      const files = await fs.readdir(dirPath, { withFileTypes: true });

      for (const file of files) {
        if (file.isFile()) {
          const filePath = path.join(dirPath, file.name);
          const stat = await fs.stat(filePath);
          stats.files++;
          stats.size += stat.size;
        } else if (file.isDirectory()) {
          const subStats = await this.getDirectoryStats(
            path.join(dirPath, file.name),
          );
          stats.files += subStats.files;
          stats.size += subStats.size;
        }
      }
    } catch (error) {
      // Directory might not exist, which is fine
      if (error.code !== 'ENOENT') {
        this.logger.warn(`Failed to get stats for ${dirPath}:`, error);
      }
    }

    return stats;
  }
}
