import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { AiWebtoonCharacterEntity } from './ai-webtoon-character.entity';
import { AiWebtoonTrainingCharacterSessionEntity } from './ai-webtoon-training-character-session.entity';
import { DefaultEntity } from './default.entity';

@Entity('ai_webtoon_training_log')
export class AiWebtoonTrainingLogEntity extends DefaultEntity {
  @Column({ name: 'ai_webtoon_character_id' })
  aiWebtoonCharacterId: number;

  @Column({ name: 'ai_webtoon_training_character_session_id' })
  aiWebtoonTrainingCharacterSessionId: number;

  @Column({ type: 'decimal', precision: 10, scale: 6 })
  loss: number;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  percent: number;

  @ManyToOne(
    () => AiWebtoonCharacterEntity,
    (aiWebtoonCharacter) => aiWebtoonCharacter.logs,
  )
  @JoinColumn({ name: 'ai_webtoon_character_id' })
  aiWebtoonCharacter: AiWebtoonCharacterEntity;

  @ManyToOne(
    () => AiWebtoonTrainingCharacterSessionEntity,
    (aiWebtoonTrainingCharacterSession) =>
      aiWebtoonTrainingCharacterSession.logs,
  )
  @JoinColumn({ name: 'ai_webtoon_training_character_session_id' })
  aiTrainingSession: AiWebtoonTrainingCharacterSessionEntity;
}
