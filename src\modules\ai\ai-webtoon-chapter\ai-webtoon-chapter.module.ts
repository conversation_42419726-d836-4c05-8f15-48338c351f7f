import { AiServiceModule } from '../ai-service/ai-service.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AiWebtoonChapterRepository } from 'src/database/repositories/ai-webtoon-chapter.repository';
import { AiWebtoonCutChapterRepository } from 'src/database/repositories/ai-webtoon-cut-chapter.repository';
import { AiWebtoonSceneChapterRepository } from 'src/database/repositories/ai-webtoon-scene-chapter.repository';
import { AiWebtoonStoryRepository } from 'src/database/repositories/ai-webtoon-story.repository';
import { AiWebtoonCutChapterModule } from '../ai-webtoon-cut-chapter/ai-webtoon-cut-chapter.module';
import { AiWebtoonStoryModule } from '../ai-webtoon-story/ai-webtoon-story.module';
// import { FileModule } from '../file/file.module';
// import { SocketModule } from '../socket/socket.module';
import { AiWebtoonOrderGenerateCutChapterRepository } from 'src/database/repositories/ai-webtoon-order-generate-cut-chapter.repository';
import { AiWebtoonChapterController } from './ai-webtoon-chapter.controller';
import { AiWebtoonChapterService } from './ai-webtoon-chapter.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AiWebtoonChapterRepository,
      AiWebtoonStoryRepository,
      AiWebtoonOrderGenerateCutChapterRepository,
      AiWebtoonSceneChapterRepository,
      AiWebtoonCutChapterRepository,
    ]),
    AiWebtoonStoryModule,
    AiWebtoonCutChapterModule,
    // SocketModule,
    AiServiceModule,
    // FileModule,
  ],
  controllers: [AiWebtoonChapterController],
  providers: [AiWebtoonChapterService],
  exports: [AiWebtoonChapterService],
})
export class AiWebtoonChapterModule {}
