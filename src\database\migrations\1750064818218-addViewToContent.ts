import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddViewToContent1750064818218 implements MigrationInterface {
  name = 'AddViewToContent1750064818218';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD \`viewCount\` int NOT NULL DEFAULT '0'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content\` DROP COLUMN \`viewCount\``,
    );
  }
}
