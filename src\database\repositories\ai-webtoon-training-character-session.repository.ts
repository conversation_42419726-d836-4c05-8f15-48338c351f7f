import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { AiWebtoonTrainingCharacterSessionEntity } from '../entities/ai-webtoon-training-character-session.entity';

@Injectable()
export class AiWebtoonTrainingCharacterSessionRepository extends Repository<AiWebtoonTrainingCharacterSessionEntity> {
  constructor(private dataSource: DataSource) {
    super(
      AiWebtoonTrainingCharacterSessionEntity,
      dataSource.createEntityManager(),
    );
  }
}
