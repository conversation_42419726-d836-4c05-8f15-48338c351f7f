import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateMenuTable1747731237513 implements MigrationInterface {
  name = 'CreateMenuTable1747731237513';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`menu\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`key\` varchar(255) NOT NULL, \`label\` varchar(255) NOT NULL, \`icon\` varchar(255) NOT NULL, \`route\` varchar(255) NOT NULL, \`order\` int NOT NULL DEFAULT '0', \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active', \`parentId\` int NULL, UNIQUE INDEX \`IDX_947bcf4f014dbed7655bee5ee5\` (\`key\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_947bcf4f014dbed7655bee5ee5\` ON \`menu\``,
    );
    await queryRunner.query(`DROP TABLE \`menu\``);
  }
}
