import { ApiProperty } from '@nestjs/swagger';

export class UserAnalyticsStatsDto {
  @ApiProperty({
    description: 'Date of the analytics (YYYY-MM-DD)',
    example: '2025-01-18',
  })
  date: string;

  @ApiProperty({
    description: 'Total views (login count)',
    example: 1100,
  })
  totalViews: number;

  @ApiProperty({
    description: 'Active users count',
    example: 550,
  })
  userActive: number;

  @ApiProperty({
    description: 'New signups count',
    example: 85,
  })
  signup: number;

  @ApiProperty({
    description: 'Signup rate percentage',
    example: 7.73,
  })
  signupRate: number;

  @ApiProperty({
    description: 'Deleted accounts count',
    example: 7,
  })
  deleteAccount: number;
}

export class HourlyAnalyticsStatsDto {
  @ApiProperty({
    description: 'Hour range (e.g., "00-01", "01-02")',
    example: '14-15',
  })
  hour: string;

  @ApiProperty({
    description: 'Total views (login count)',
    example: 150,
  })
  totalViews: number;

  @ApiProperty({
    description: 'Active users count',
    example: 120,
  })
  userActive: number;

  @ApiProperty({
    description: 'New signups count',
    example: 15,
  })
  signup: number;

  @ApiProperty({
    description: 'Signup rate percentage',
    example: 12.5,
  })
  signupRate: number;

  @ApiProperty({
    description: 'Deleted accounts count',
    example: 2,
  })
  deleteAccount: number;
}

export class MonthlyAnalyticsStatsDto {
  @ApiProperty({
    description: 'Month (YYYY-MM)',
    example: '2025-01',
  })
  month: string;

  @ApiProperty({
    description: 'Total views (login count)',
    example: 34100,
  })
  totalViews: number;

  @ApiProperty({
    description: 'Active users count',
    example: 17050,
  })
  userActive: number;

  @ApiProperty({
    description: 'New signups count',
    example: 2635,
  })
  signup: number;

  @ApiProperty({
    description: 'Signup rate percentage',
    example: 15.45,
  })
  signupRate: number;

  @ApiProperty({
    description: 'Deleted accounts count',
    example: 217,
  })
  deleteAccount: number;

  @ApiProperty({
    description: 'Average daily active users',
    example: 550,
  })
  avgDailyActiveUsers: number;
}

export class UserAnalyticsResponseDto {
  @ApiProperty({
    description: 'Array of daily user analytics statistics',
    type: [UserAnalyticsStatsDto],
  })
  data: UserAnalyticsStatsDto[];

  @ApiProperty({
    description: 'Total number of days in the result',
    example: 31,
  })
  total: number;

  @ApiProperty({
    description: 'Query parameters used',
    example: {
      startDate: '2025-01-01',
      endDate: '2025-01-31',
    },
  })
  queryInfo: {
    startDate: string;
    endDate: string;
  };
}

export class HourlyAnalyticsResponseDto {
  @ApiProperty({
    description: 'Array of hourly user analytics statistics',
    type: [HourlyAnalyticsStatsDto],
  })
  data: HourlyAnalyticsStatsDto[];

  @ApiProperty({
    description: 'Total number of hours in the result',
    example: 24,
  })
  total: number;

  @ApiProperty({
    description: 'Query parameters used',
    example: {
      date: '2025-01-18',
    },
  })
  queryInfo: {
    date: string;
  };
}

export class MonthlyAnalyticsResponseDto {
  @ApiProperty({
    description: 'Array of monthly user analytics statistics',
    type: [MonthlyAnalyticsStatsDto],
  })
  data: MonthlyAnalyticsStatsDto[];

  @ApiProperty({
    description: 'Total number of months in the result',
    example: 3,
  })
  total: number;

  @ApiProperty({
    description: 'Query parameters used',
    example: {
      startMonth: '2025-01',
      endMonth: '2025-03',
    },
  })
  queryInfo: {
    startMonth: string;
    endMonth: string;
  };
}
