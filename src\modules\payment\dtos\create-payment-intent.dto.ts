import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, IsOptional } from 'class-validator';

export class CreatePaymentIntentDto {
  @IsNotEmpty()
  @IsNumber()
  @ApiProperty({
    description: 'Plan ID',
    example: 1,
  })
  planId: number;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'Payment method',
    example: 'paypal',
  })
  paymentMethod: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'Currency',
    example: 'USD',
  })
  currency: string;

  @ApiProperty({
    description: 'Language ID for analytics tracking',
    example: 1,
    required: true,
  })
  @IsNumber()
  @IsNotEmpty()
  languageId: number;

  @ApiProperty({
    description: 'Region for exchange rate calculation',
    example: 'US',
    required: false,
    default: 'US',
  })
  @IsOptional()
  @IsString()
  region?: string;
}
