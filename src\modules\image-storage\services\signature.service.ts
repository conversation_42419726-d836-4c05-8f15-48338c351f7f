import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

@Injectable()
export class SignatureService {
  private readonly logger = new Logger(SignatureService.name);
  private readonly systemPublicKey: string;

  constructor(private readonly configService: ConfigService) {
    this.systemPublicKey = this.configService.get<string>(
      'IMAGE_STORAGE_PUBLIC_KEY',
      '-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...\n-----END PUBLIC KEY-----',
    );
  }

  /**
   * Verify signature of image buffer using provided public key
   */
  verifySignature(
    imageBuffer: Buffer,
    signature: string,
    publicKey: string,
  ): boolean {
    try {
      // Validate signature format (should be hex string)
      if (!this.isValidHexSignature(signature)) {
        this.logger.warn('Invalid signature format - not a valid hex string');
        return false;
      }

      // Validate public key format
      if (!this.isValidPublicKey(publicKey)) {
        this.logger.warn('Invalid public key format');
        return false;
      }

      // For system-to-system communication, verify against system public key
      if (publicKey === this.systemPublicKey) {
        return this.verifyWithSystemKey(imageBuffer, signature);
      }

      // Verify with provided public key
      const verifier = crypto.createVerify('SHA256');
      verifier.update(imageBuffer);
      verifier.end();

      const isValid = verifier.verify(publicKey, signature, 'hex');

      if (isValid) {
        this.logger.log('Signature verification successful');
      } else {
        this.logger.warn('Signature verification failed');
      }

      return isValid;
    } catch (error) {
      this.logger.error('Signature verification error:', error);
      return false;
    }
  }

  /**
   * Generate signature for testing purposes (should not be used in production)
   */
  generateSignature(imageBuffer: Buffer, privateKey: string): string {
    try {
      const signer = crypto.createSign('SHA256');
      signer.update(imageBuffer);
      signer.end();

      return signer.sign(privateKey, 'hex');
    } catch (error) {
      this.logger.error('Signature generation error:', error);
      throw error;
    }
  }

  /**
   * Verify signature with system public key
   */
  private verifyWithSystemKey(imageBuffer: Buffer, signature: string): boolean {
    try {
      const verifier = crypto.createVerify('SHA256');
      verifier.update(imageBuffer);
      verifier.end();

      return verifier.verify(this.systemPublicKey, signature, 'hex');
    } catch (error) {
      this.logger.error('System signature verification error:', error);
      return false;
    }
  }

  /**
   * Validate if signature is a valid hex string
   */
  private isValidHexSignature(signature: string): boolean {
    // Should be hex string, typically 128-512 characters for RSA signatures
    const hexRegex = /^[a-fA-F0-9]+$/;
    return (
      typeof signature === 'string' &&
      signature.length >= 128 &&
      signature.length <= 1024 &&
      hexRegex.test(signature)
    );
  }

  /**
   * Validate public key format
   */
  private isValidPublicKey(publicKey: string): boolean {
    try {
      // Check if it's a valid PEM format
      const pemRegex =
        /^-----BEGIN PUBLIC KEY-----[\s\S]*-----END PUBLIC KEY-----$/;
      if (!pemRegex.test(publicKey.trim())) {
        return false;
      }

      // Try to create a crypto object to validate the key
      crypto.createPublicKey(publicKey);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get system public key (for debugging/testing)
   */
  getSystemPublicKey(): string {
    return this.systemPublicKey;
  }

  /**
   * Check if the provided public key is the system key
   */
  isSystemKey(publicKey: string): boolean {
    return publicKey === this.systemPublicKey;
  }
}
