import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { SubscriptionService } from '../services/subscription.service';
import { CreateSubscriptionDto } from '../dto/create-subscription.dto';
import { UpdateSubscriptionDto } from '../dto/update-subscription.dto';
import { JwtAdminAuthGuard } from '../../auth-admin/guards/jwt-admin-auth.guard';
import { AdminRole } from '../../../../common/role.enum';
import { Roles } from '../../../auth/decorators/role.decorator';

@ApiTags('Admin - Subscriptions')
@ApiBearerAuth()
@UseGuards(JwtAdminAuthGuard)
@Roles(AdminRole.ADMIN, AdminRole.SUPER_ADMIN)
@Controller('admin/subscriptions')
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new subscription' })
  @ApiBody({ type: CreateSubscriptionDto })
  @ApiResponse({
    status: 201,
    description: 'Subscription created successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async create(@Body() createSubscriptionDto: CreateSubscriptionDto) {
    return await this.subscriptionService.create(createSubscriptionDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all subscriptions' })
  @ApiResponse({ status: 200, description: 'List of subscriptions' })
  async findAll() {
    return await this.subscriptionService.findAll();
  }

  @Get('active')
  @ApiOperation({ summary: 'Get all active subscriptions' })
  @ApiResponse({ status: 200, description: 'List of active subscriptions' })
  async findActive() {
    return await this.subscriptionService.findActive();
  }

  @Get('expired')
  @ApiOperation({ summary: 'Get all expired subscriptions' })
  @ApiResponse({ status: 200, description: 'List of expired subscriptions' })
  async findExpired() {
    return await this.subscriptionService.findExpired();
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get subscriptions by user ID' })
  @ApiParam({ name: 'userId', type: 'number', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'List of user subscriptions' })
  async findByUser(@Param('userId', ParseIntPipe) userId: number) {
    return await this.subscriptionService.findByUser(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get subscription by ID' })
  @ApiParam({ name: 'id', type: 'number', description: 'Subscription ID' })
  @ApiResponse({ status: 200, description: 'Subscription details' })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return await this.subscriptionService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update subscription' })
  @ApiParam({ name: 'id', type: 'number', description: 'Subscription ID' })
  @ApiBody({ type: UpdateSubscriptionDto })
  @ApiResponse({
    status: 200,
    description: 'Subscription updated successfully',
  })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateSubscriptionDto: UpdateSubscriptionDto,
  ) {
    return await this.subscriptionService.update(id, updateSubscriptionDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete subscription' })
  @ApiParam({ name: 'id', type: 'number', description: 'Subscription ID' })
  @ApiResponse({
    status: 200,
    description: 'Subscription deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    await this.subscriptionService.remove(id);
    return { message: 'Subscription deleted successfully' };
  }

  @Patch(':id/cancel')
  @ApiOperation({ summary: 'Cancel subscription' })
  @ApiParam({ name: 'id', type: 'number', description: 'Subscription ID' })
  @ApiResponse({
    status: 200,
    description: 'Subscription canceled successfully',
  })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  async cancel(@Param('id', ParseIntPipe) id: number) {
    return await this.subscriptionService.cancel(id);
  }

  @Patch(':id/renew')
  @ApiOperation({ summary: 'Renew subscription' })
  @ApiParam({ name: 'id', type: 'number', description: 'Subscription ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        expiresAt: {
          type: 'number',
          description: 'New expiration timestamp',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Subscription renewed successfully',
  })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  @ApiResponse({ status: 400, description: 'Invalid expiration date' })
  async renew(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { expiresAt: number },
  ) {
    return await this.subscriptionService.renew(id, body.expiresAt);
  }
}
