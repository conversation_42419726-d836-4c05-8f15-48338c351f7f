import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as crypto from 'crypto';
import { WebhookPayload } from '../interfaces/image-storage.interface';

@Injectable()
export class WebhookService {
  private readonly logger = new Logger(WebhookService.name);
  private readonly webhookSecret: string;
  private readonly maxRetries: number;
  private readonly retryDelays: number[] = [1000, 3000, 5000]; // 1s, 3s, 5s

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.webhookSecret = this.configService.get<string>(
      'IMAGE_STORAGE_WEBHOOK_SECRET',
      'default-webhook-secret-change-in-production',
    );
    this.maxRetries = this.configService.get<number>('WEBHOOK_MAX_RETRIES', 3);
  }

  async sendWebhook(payload: WebhookPayload): Promise<void> {
    const webhookUrl = 'http://localhost:3005/'; // Replace with actual webhook URL

    let lastError: Error = new Error('Unknown error');

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        this.logger.log(
          `Sending webhook to ${webhookUrl} (attempt ${attempt + 1}/${this.maxRetries + 1})`,
        );

        await this.sendWebhookRequest(webhookUrl, payload);

        this.logger.log(`Webhook sent successfully to ${webhookUrl}`);
        return;
      } catch (error) {
        lastError = error;
        this.logger.warn(
          `Webhook delivery failed (attempt ${attempt + 1}): ${error.message}`,
        );

        // If this is not the last attempt, wait before retrying
        if (attempt < this.maxRetries) {
          const delay = this.retryDelays[attempt] || 5000;
          this.logger.log(`Retrying webhook in ${delay}ms...`);
          await this.sleep(delay);
        }
      }
    }

    // All attempts failed
    this.logger.error(
      `Webhook delivery failed after ${this.maxRetries + 1} attempts to ${webhookUrl}:`,
      lastError,
    );

    // Optionally, you could store failed webhooks for manual retry
    this.handleFailedWebhook(webhookUrl, payload, lastError);
  }

  /**
   * Send the actual webhook HTTP request
   */
  private async sendWebhookRequest(
    webhookUrl: string,
    payload: WebhookPayload,
  ): Promise<void> {
    const timestamp = Math.floor(Date.now() / 1000);
    const signature = this.generateWebhookSignature(payload, timestamp);

    const headers = {
      'Content-Type': 'application/json',
      'X-Webhook-Timestamp': timestamp.toString(),
      'X-Webhook-Signature': signature,
      'User-Agent': 'ImageStorageService/1.0',
    };

    const timeout = this.configService.get<number>('WEBHOOK_TIMEOUT', 10000);

    try {
      const response = await firstValueFrom(
        this.httpService.post(webhookUrl, payload, {
          headers,
          timeout,
          validateStatus: (status) => status >= 200 && status < 300,
        }),
      );

      this.logger.log(
        `Webhook delivered successfully: ${response.status} ${response.statusText}`,
      );
    } catch (error) {
      if (error.response) {
        // HTTP error response
        throw new Error(
          `Webhook HTTP ${error.response.status}: ${error.response.statusText}`,
        );
      } else if (error.code === 'ECONNABORTED') {
        // Timeout
        throw new Error(`Webhook timeout after ${timeout}ms`);
      } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        // Network errors
        throw new Error(`Network error: ${error.message}`);
      } else {
        // Other errors
        throw new Error(`Webhook error: ${error.message}`);
      }
    }
  }

  /**
   * Generate HMAC signature for webhook verification
   */
  private generateWebhookSignature(
    payload: WebhookPayload,
    timestamp: number,
  ): string {
    const message = `${timestamp}.${JSON.stringify(payload)}`;
    return crypto
      .createHmac('sha256', this.webhookSecret)
      .update(message)
      .digest('hex');
  }

  /**
   * Verify webhook signature (for testing or debugging)
   */
  verifyWebhookSignature(
    payload: WebhookPayload,
    timestamp: number,
    signature: string,
  ): boolean {
    try {
      const expectedSignature = this.generateWebhookSignature(
        payload,
        timestamp,
      );
      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex'),
      );
    } catch (error) {
      this.logger.error('Signature verification error:', error);
      return false;
    }
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      const parsed = new URL(url);
      return ['http:', 'https:'].includes(parsed.protocol);
    } catch (error) {
      console.log(error.message);
      return false;
    }
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Handle failed webhook delivery
   * Could store in database for manual retry, send alerts, etc.
   */
  private handleFailedWebhook(
    webhookUrl: string,
    payload: WebhookPayload,
    error: Error,
  ) {
    this.logger.error(
      `Final webhook failure for upload ${payload.uploadId} to ${webhookUrl}:`,
      {
        uploadId: payload.uploadId,
        webhookUrl,
        error: error.message,
        payload: JSON.stringify(payload),
        timestamp: new Date().toISOString(),
      },
    );

    // TODO: Implement failed webhook storage/alerting
    // - Store in database for manual retry
    // - Send alert to monitoring system
    // - Add to dead letter queue
  }

  /**
   * Test webhook endpoint (for debugging)
   */
  async testWebhook(webhookUrl: string): Promise<{
    success: boolean;
    statusCode?: number;
    message?: string;
  }> {
    try {
      const testPayload: WebhookPayload = {
        uploadId: 'test-webhook-' + Date.now(),
        status: 'completed' as any,
        imageUrl: 'http://example.com/test-image.jpg',
        thumbnailUrl: 'http://example.com/test-thumbnail.jpg',
        createdAt: new Date().toISOString(),
        completedAt: new Date().toISOString(),
      };

      await this.sendWebhookRequest(webhookUrl, testPayload);

      return {
        success: true,
        message: 'Webhook test successful',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
      };
    }
  }

  /**
   * Get webhook configuration info
   */
  getWebhookConfig(): {
    maxRetries: number;
    retryDelays: number[];
    timeout: number;
    hasSecret: boolean;
  } {
    return {
      maxRetries: this.maxRetries,
      retryDelays: this.retryDelays,
      timeout: this.configService.get<number>('WEBHOOK_TIMEOUT', 10000),
      hasSecret:
        !!this.webhookSecret &&
        this.webhookSecret !== 'default-webhook-secret-change-in-production',
    };
  }
}
