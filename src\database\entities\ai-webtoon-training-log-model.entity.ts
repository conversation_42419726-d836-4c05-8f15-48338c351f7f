import { IAiSampleLogTrainingModelItem } from 'src/common/interfaces/ai-webtoon/ai-webtoon-training-log-model.interface';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { AiWebtoonCharacterEntity } from './ai-webtoon-character.entity';
import { AiWebtoonTrainingCharacterSessionEntity } from './ai-webtoon-training-character-session.entity';
import { DefaultEntity } from './default.entity';

@Entity('ai_webtoon_training_log_model')
export class AiWebtoonTrainingLogModelEntity extends DefaultEntity {
  @Column({ name: 'ai_webtoon_character_id' })
  aiWebtoonCharacterId: number;

  @Column({ name: 'ai_webtoon_training_character_session_id' })
  aiWebtoonTrainingCharacterSessionId: number;

  @Column({ name: 'model_url', type: 'varchar', nullable: true })
  modelUrl?: string | null;

  @Column({ type: 'int' })
  epoch: number;

  @Column({ type: 'json', default: () => '(JSON_ARRAY())' })
  samples: IAiSampleLogTrainingModelItem[];

  @ManyToOne(
    () => AiWebtoonCharacterEntity,
    (aiWebtoonCharacter) => aiWebtoonCharacter.logs,
  )
  @JoinColumn({ name: 'ai_webtoon_character_id' })
  aiWebtoonCharacter: AiWebtoonCharacterEntity;

  @ManyToOne(
    () => AiWebtoonTrainingCharacterSessionEntity,
    (aiWebtoonTrainingCharacterSession) =>
      aiWebtoonTrainingCharacterSession.logs,
  )
  @JoinColumn({ name: 'ai_webtoon_training_character_session_id' })
  aiTrainingSession: AiWebtoonTrainingCharacterSessionEntity;

  isApplied: boolean;
}
