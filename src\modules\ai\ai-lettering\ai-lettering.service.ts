import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { EAiLetteringClassificationType } from 'src/common/ai-webtoon.enum';
import { AI_MESSAGE_CONFIG, MESSAGE_CONFIG } from 'src/common/message.config';
import { In, Not } from 'typeorm';
import { AiLetteringLanguageRepository } from 'src/database/repositories/ai-lettering-language.repository';
import { CreateMultipleAiLetteringDto } from './dto/create-ai-lettering.dto';
import { ListAiLetteringDto } from './dto/list-ai-lettering.dto';
import { UpdateAiLetteringDto } from './dto/update-ai-lettering.dto';
import { isValidTimeZone } from 'src/common/utilities/check.utility';
import * as moment from 'moment-timezone';
import { AiLetteringRepository } from 'src/database/repositories/ai-lettering.repository';

@Injectable()
export class AiLetteringService {
  constructor(
    private readonly aiLetteringRepository: AiLetteringRepository,
    private readonly aiLetteringLanguageRepository: AiLetteringLanguageRepository,
  ) {}
  private readonly logger = new Logger(AiLetteringService.name);

  async createMultiple(dto: CreateMultipleAiLetteringDto) {
    const names = dto.data.map((item) => item.name);
    const existingItems = await this.aiLetteringRepository.find({
      where: { name: In(names) },
    });

    if (existingItems.length > 0) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_LETTERING_EXISTS);
    }

    const newItems = dto.data.map((item) => ({
      name: item.name,
      extension: item.extension,
      url: item.url,
      thumbnailUrl: item.thumbnailUrl,
      classification: dto.classification,
      aiLetteringLanguageId: dto.aiLetteringLanguageId,
      aiLetteringTypeId: dto.aiLetteringTypeId,
    }));

    await this.aiLetteringRepository.save(newItems);
    return true;
  }

  async list(dto: ListAiLetteringDto, timezone: string) {
    if (!isValidTimeZone(timezone)) {
      throw new BadRequestException('timezone_invalid', 'Timezone invalid');
    }

    const query = this.aiLetteringRepository
      .createQueryBuilder('ai_lettering')
      .leftJoinAndSelect(
        'ai_lettering.aiLetteringLanguage',
        'aiLetteringLanguage',
      )
      .leftJoinAndSelect('ai_lettering.aiLetteringType', 'aiLetteringType');

    if (dto.name) {
      query.andWhere('ai_lettering.name LIKE :name', { name: `%${dto.name}%` });
    }

    if (
      dto.classification &&
      dto.classification !== EAiLetteringClassificationType.ALL
    ) {
      query.andWhere('ai_lettering.classification = :classification', {
        classification: dto.classification,
      });
    }

    if (dto.aiLetteringLanguageId) {
      const language = await this.aiLetteringLanguageRepository.findOne({
        where: { id: dto.aiLetteringLanguageId },
      });
      if (!language || language.title !== 'All') {
        query.andWhere(
          'ai_lettering.aiLetteringLanguageId = :aiLetteringLanguageId',
          {
            aiLetteringLanguageId: dto.aiLetteringLanguageId,
          },
        );
      }
    }

    if (dto.aiLetteringTypeId) {
      query.andWhere('ai_lettering.aiLetteringTypeId = :aiLetteringTypeId', {
        aiLetteringTypeId: dto.aiLetteringTypeId,
      });
    }

    if (dto.startTime) {
      const startTime = moment(dto.startTime)
        .tz(timezone)
        .startOf('day')
        .utc()
        .format('YYYY-MM-DD HH:mm:ss');

      query.andWhere('ai_lettering.createdAt >= :startTime', {
        startTime,
      });
    }

    if (dto.endTime) {
      const endTime = moment(dto.endTime)
        .tz(timezone)
        .endOf('day')
        .utc()
        .format('YYYY-MM-DD HH:mm:ss');

      query.andWhere('ai_lettering.createdAt <= :endTime', {
        endTime,
      });
    }

    if (dto.aiWebtoonStoryId) {
      query
        .innerJoin(
          'ai_webtoon_story_ai_lettering',
          'story_lettering',
          'story_lettering.ai_lettering_id = ai_lettering.id',
        )
        .andWhere('story_lettering.ai_webtoon_story_id = :aiWebtoonStoryId', {
          aiWebtoonStoryId: dto.aiWebtoonStoryId,
        });
    }

    const [items, total] = await query
      .orderBy('ai_lettering.createdAt', 'DESC')
      .skip((dto.page - 1) * dto.limit)
      .take(dto.limit)
      .getManyAndCount();

    return {
      data: items,
      page: dto.page,
      limit: dto.limit,
      total,
      totalPages: Math.ceil(total / dto.limit),
    };
  }

  async detail(id: number) {
    const item = await this.aiLetteringRepository.findOne({
      where: { id },
      relations: ['aiLetteringLanguage', 'aiLetteringType'],
    });
    if (!item) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_LETTERING_NOT_FOUND);
    }

    return item;
  }

  async update(id: number, dto: UpdateAiLetteringDto) {
    const item = await this.aiLetteringRepository.findOne({
      where: { id },
    });
    if (!item) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_LETTERING_NOT_FOUND);
    }

    // Check for duplicate name
    const existingItem = await this.aiLetteringRepository.findOne({
      where: {
        name: dto.name,
        id: Not(id),
      },
    });

    if (existingItem) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_LETTERING_EXISTS);
    }

    // Update item
    item.name = dto.name;
    item.classification = dto.classification;
    item.extension = dto.extension;
    item.url = dto.url;
    item.thumbnailUrl = dto.thumbnailUrl;
    item.aiLetteringLanguageId = dto.aiLetteringLanguageId;
    item.aiLetteringTypeId = dto.aiLetteringTypeId;

    await this.aiLetteringRepository.save(item);
    return true;
  }

  async deleteMultiple(ids: number[]) {
    const items = await this.aiLetteringRepository.find({
      where: {
        id: In(ids),
      },
    });
    if (items.length !== ids.length) {
      throw new NotFoundException(MESSAGE_CONFIG.ITEMS_NOT_FOUND);
    }

    await this.aiLetteringRepository.softDelete(ids);
    return true;
  }
}
