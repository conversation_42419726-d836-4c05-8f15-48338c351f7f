import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength } from 'class-validator';

export class UserUploadSupportAttachmentDto {
  @ApiProperty({
    description: 'Support attachment files (max 10 files, 25MB each)',
    type: 'array',
    items: {
      type: 'string',
      format: 'binary',
    },
    required: true,
  })
  files: any[];

  @ApiProperty({
    description: 'Optional description for the attachments',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;
}
