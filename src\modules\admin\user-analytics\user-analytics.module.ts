import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserAnalyticsController } from './user-analytics.controller';
import { UserAnalyticsService } from './user-analytics.service';
import { UserAnalyticsEntity } from '../../../database/entities/user-analytics.entity';
import { UserAnalyticsEntityV2 } from '../../../database/entities/user-analytics_v2.entity';
import { UserAnalyticRepository } from '../../../database/repositories/user-analytics.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserAnalyticsEntity, UserAnalyticsEntityV2]),
  ],
  controllers: [UserAnalyticsController],
  providers: [UserAnalyticsService, UserAnalyticRepository],
  exports: [UserAnalyticsService],
})
export class UserAnalyticsModule {}
