import {
  Body,
  Controller,
  Delete,
  Param,
  ParseIntPipe,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { JwtAdminAuthGuard } from 'src/modules/admin/auth-admin/guards/jwt-admin-auth.guard';
import { AiWebtoonSceneChapterService } from './ai-webtoon-scene-chapter.service';
import { AddImagesDto, RemoveImageDto } from './dto/add-images.dto';
import { CreateSceneDto } from './dto/create-scene.dto';
import { ReceiveGenerateImagesDto } from './dto/receive-generate-images.dto';
import { UpdateSceneDto } from './dto/update-scene.dto';

@ApiTags('ai-webtoon-scene-chapter')
@Controller('ai-webtoon-scene-chapter')
export class AiWebtoonSceneChapterController {
  constructor(
    private readonly aiWebtoonSceneChapterService: AiWebtoonSceneChapterService,
  ) {}

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/generate-images/:id')
  generateImages(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonSceneChapterService.generateImages(id);
  }

  @Post('/receive-images')
  receiveGenerateImages(@Body() body: ReceiveGenerateImagesDto) {
    return this.aiWebtoonSceneChapterService.receiveGenerateImages(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/add-images/:id')
  addImages(@Param('id', ParseIntPipe) id: number, @Body() body: AddImagesDto) {
    return this.aiWebtoonSceneChapterService.addImages(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Delete('/remove-image/:id')
  removeImage(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: RemoveImageDto,
  ) {
    return this.aiWebtoonSceneChapterService.removeImage(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/:chapterId')
  createSceneByChapter(
    @Body() body: CreateSceneDto,
    @Param('chapterId', ParseIntPipe) chapterId: number,
  ) {
    return this.aiWebtoonSceneChapterService.createSceneByChapter(
      chapterId,
      body,
    );
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('/:id')
  updateScene(
    @Body() body: UpdateSceneDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonSceneChapterService.updateScene(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Delete('/:id')
  deleteScene(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonSceneChapterService.deleteScene(id);
  }
}
