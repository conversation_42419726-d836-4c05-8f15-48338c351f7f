import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUrl,
} from 'class-validator';
import { Type } from 'class-transformer';
import { EAiWebtoonStoryBorderType } from 'src/common/ai-webtoon.enum';

export class CombineImagesDto {
  @ApiProperty()
  @IsString()
  @IsUrl()
  frontImage: string;

  @ApiProperty()
  @IsString()
  @IsUrl()
  backImage: string;

  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  imageCenterPointX: number;

  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  imageCenterPointY: number;

  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  resizeWidth: number;

  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  resizeHeight: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  rotate?: number | null;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  @Type(() => Array)
  topLeft?: number[] | null;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  @Type(() => Array)
  bottomRight?: number[] | null;

  @ApiProperty({ enum: EAiWebtoonStoryBorderType })
  @IsNotEmpty()
  @IsEnum(EAiWebtoonStoryBorderType)
  borderType: EAiWebtoonStoryBorderType;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  borderColor: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  borderWeight: number;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  frameHeightImageContainer: number;
}
