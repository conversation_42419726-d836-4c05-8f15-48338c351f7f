import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FaqAdminController } from './controllers/faq-admin.controller';
import { FaqAdminService } from './services/faq-admin.service';
import { FaqRepository } from '../../../database/repositories/faq.repository';
import { SupportCategoryRepository } from '../../../database/repositories/support-category.repository';
import { FaqEntity } from '../../../database/entities/faq.entity';
import { SupportCategoryEntity } from '../../../database/entities/support-category.entity';

@Module({
  imports: [TypeOrmModule.forFeature([FaqEntity, SupportCategoryEntity])],
  controllers: [FaqAdminController],
  providers: [FaqAdminService, FaqRepository, SupportCategoryRepository],
  exports: [FaqAdminService],
})
export class FaqAdminModule {}
