export enum SettingGroup {
  UI_MENU = 'ui_menu',
  ACCESS_PAGE = 'access_page',
  IMAGE_ACCESS_PAGE = 'image_access_page',
  LINK_TREE = 'link_tree',
  LINK_TREE_WHITEPAPER = 'link_tree_whitepaper',
  LANGUAGE = 'language',
  CONTENT_CLASSIFICATION = 'content_classification',
  CONTENT_DATE = 'content_date',
  AUTHOR_PAYMENT_FORMAT = 'author_payment_format',
  SUPPORT_AUTO_REPLY = 'support_auto_reply',
}

export const SettingGroupLimit = {
  [SettingGroup.LINK_TREE]: false,
  [SettingGroup.LINK_TREE_WHITEPAPER]: true,
  [SettingGroup.UI_MENU]: false,
  [SettingGroup.ACCESS_PAGE]: true,
  [SettingGroup.IMAGE_ACCESS_PAGE]: true,
  [SettingGroup.LANGUAGE]: false,
  [SettingGroup.CONTENT_CLASSIFICATION]: false,
  [SettingGroup.CONTENT_DATE]: false,
  [SettingGroup.AUTHOR_PAYMENT_FORMAT]: false,
  [SettingGroup.SUPPORT_AUTO_REPLY]: true,
};

export const SettingGroupUploadAccess = {
  [SettingGroup.LINK_TREE]: false,
  [SettingGroup.LINK_TREE_WHITEPAPER]: false,
  [SettingGroup.UI_MENU]: false,
  [SettingGroup.ACCESS_PAGE]: false,
  [SettingGroup.IMAGE_ACCESS_PAGE]: true,
  [SettingGroup.LANGUAGE]: false,
  [SettingGroup.CONTENT_CLASSIFICATION]: false,
  [SettingGroup.CONTENT_DATE]: false,
  [SettingGroup.AUTHOR_PAYMENT_FORMAT]: false,
  [SettingGroup.SUPPORT_AUTO_REPLY]: false,
};

export interface LinkTreeInterface {
  categories1: string;
  categories2: string;
  url: string;
  status: 'active' | 'inactive';
}

export interface LinkTreeWhitepaperInterface {
  categories1: string;
  categories2: string;
  url: string;
  status: 'active' | 'inactive';
}

export enum LanguageKey {
  LANGUAGE = 'language',
  SUBSCRIPTION_STATUS = 'subscriptionStatus',
  COINS_STATUS = 'coinsStatus',
  CURRENCY = 'currency',
}

export enum SanctionType {
  PERMANENT = 'permanent',
  PERIOD = 'period',
}

export enum ContentSettingType {
  CLASSIFICATION = 'classification',
  DATE = 'date',
  LANGUAGE = 'language',
  PAYMENT_FORMAT = 'payment_format',
}

export enum EpisodeBackgroundColors {
  BLACK = '#000000',
  WHITE = '#ffffff',
}

export enum PaymentType {
  FREE = 'free',
  PAID = 'paid',
}

export enum PaymentUnitType {
  KRW = 'krw',
}
