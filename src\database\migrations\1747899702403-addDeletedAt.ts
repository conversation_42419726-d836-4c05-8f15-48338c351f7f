import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDeletedAt1747899702403 implements MigrationInterface {
  name = 'AddDeletedAt1747899702403';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`deletedAt\` datetime(6) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`admins\` ADD \`deletedAt\` datetime(6) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`menu_admin\` ADD \`deletedAt\` datetime(6) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`menu\` ADD \`deletedAt\` datetime(6) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`menu\` DROP COLUMN \`deletedAt\``);
    await queryRunner.query(
      `ALTER TABLE \`menu_admin\` DROP COLUMN \`deletedAt\``,
    );
    await queryRunner.query(`ALTER TABLE \`admins\` DROP COLUMN \`deletedAt\``);
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`deletedAt\``);
  }
}
