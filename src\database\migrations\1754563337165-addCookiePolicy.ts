import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCookiePolicy1754563337165 implements MigrationInterface {
  name = 'AddCookiePolicy1754563337165';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_73484cd8756e4a121b4ce26b6b\` ON \`legal_document\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`legal_document\` CHANGE \`type\` \`type\` enum ('terms_of_use', 'privacy_policy', 'cookie_policy') NOT NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX \`IDX_73484cd8756e4a121b4ce26b6b\` ON \`legal_document\` (\`status\`, \`type\`)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_73484cd8756e4a121b4ce26b6b\` ON \`legal_document\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`legal_document\` CHANGE \`type\` \`type\` enum ('terms_of_use', 'privacy_policy') NOT NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX \`IDX_73484cd8756e4a121b4ce26b6b\` ON \`legal_document\` (\`status\`, \`type\`)`,
    );
  }
}
