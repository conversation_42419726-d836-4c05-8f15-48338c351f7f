import { Seeder } from '@jorgebodega/typeorm-seeding';
import { DataSource } from 'typeorm';
import { AdminStatus, MenuAdminStatus } from '../../common/status.enum';
import { AdminRole } from '../../common/role.enum';
import * as bcrypt from 'bcrypt';
import { AdminEntity } from '../entities/admin.entity';
import { MenuEntity } from '../entities/menu.entity';
import { MenuAdminEntity } from '../entities/menu-admin.entity';

export class AdminSeeder extends Seeder {
  async run(datasource: DataSource) {
    const adminRepo = datasource.getRepository(AdminEntity);
    const email = '<EMAIL>';
    const username = 'admin';
    // Check if admin already exists
    const adminData = await adminRepo.findOne({
      where: {
        email: email,
      },
    });

    if (adminData) {
      console.log('\nAdmin already exists');

      // Always reassign menus to ensure admin has access to new menus
      await this.assignMenusToAdmin(datasource, adminData);
      return;
    }

    // Create new admin
    const admin = await adminRepo.save({
      email: email,
      username: username,
      password: bcrypt.hashSync('password', 10),
      status: AdminStatus.ACTIVE,
      role: AdminRole.SUPER_ADMIN,
    });
    console.log('\nAdmin created successfully:', admin.username);

    // Assign all menus to the new admin
    await this.assignMenusToAdmin(datasource, admin);
  }

  private async assignMenusToAdmin(datasource: DataSource, admin: AdminEntity) {
    const menuRepo = datasource.getRepository(MenuEntity);
    const menuAdminRepo = datasource.getRepository(MenuAdminEntity);

    // Get all menus
    const allMenus = await menuRepo.find();

    if (allMenus.length === 0) {
      console.log('No menus found. Please run MenuSeeder first.');
      return;
    }

    console.log(`\nAssigning ${allMenus.length} menus to admin...`);

    // Assign each menu to admin
    for (const menu of allMenus) {
      // Check if assignment already exists
      const existingAssignment = await menuAdminRepo.findOne({
        where: {
          admin: { id: admin.id },
          menu: { id: menu.id },
        },
      });

      if (!existingAssignment) {
        await menuAdminRepo.save({
          admin: admin,
          menu: menu,
          status: MenuAdminStatus.ACTIVE,
        });
        console.log(`Assigned menu: ${menu.label}`);
      }
    }

    console.log(`Successfully assigned all menus to admin: ${admin.username}`);
  }
}
