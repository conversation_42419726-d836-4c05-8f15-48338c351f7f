import {
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
  Body,
  Req,
} from '@nestjs/common';

import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { RoleGuard } from '../auth-admin/guards/role.guard';
import { Roles } from '../../auth/decorators/role.decorator';
import { AdminRole } from '../../../common/role.enum';
import { UserAdminDto } from './dtos/user-admin.dto';
import { UserService } from 'src/modules/user/user.service';
import { UserSanctionRequestDto } from './dtos/user-sanction-request.dto';
import { JwtAdminAuthGuard } from '../auth-admin/guards/jwt-admin-auth.guard';
import { ChangePasswordByAdminDto } from '../../user/dtos/change-password-admin.dto';
@ApiTags('Admin: User management')
@ApiBearerAuth()
@Controller('user-management')
export class UserAdminController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @UseGuards(JwtAdminAuthGuard, RoleGuard)
  @Roles(AdminRole.SUPER_ADMIN, AdminRole.ADMIN)
  @ApiOperation({ summary: 'Get users' })
  getUsers(@Query() query: UserAdminDto) {
    return this.userService.getUsers(query);
  }

  @Get('user-by-id/:id')
  @UseGuards(JwtAdminAuthGuard, RoleGuard)
  @Roles(AdminRole.SUPER_ADMIN, AdminRole.ADMIN)
  @ApiOperation({ summary: 'Get user by id' })
  getUserById(@Param('id') id: number) {
    return this.userService.getUserById(id);
  }

  @Post('sanction-user')
  @UseGuards(JwtAdminAuthGuard, RoleGuard)
  @Roles(AdminRole.SUPER_ADMIN, AdminRole.ADMIN)
  @ApiOperation({ summary: 'Sanction user' })
  sanctionUser(@Body() body: UserSanctionRequestDto, @Req() req: any) {
    return this.userService.sanctionUser(body, req.user);
  }

  @Post('change-password-by-admin')
  @UseGuards(JwtAdminAuthGuard, RoleGuard)
  @Roles(AdminRole.SUPER_ADMIN, AdminRole.ADMIN)
  @ApiOperation({ summary: 'Change password by admin' })
  changePasswordByAdmin(@Body() body: ChangePasswordByAdminDto) {
    return this.userService.changePasswordByAdmin(body);
  }
}
