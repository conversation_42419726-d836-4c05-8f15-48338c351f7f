import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateContentsTable1749525557597 implements MigrationInterface {
  name = 'UpdateContentsTable1749525557597';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD \`status\` enum ('active', 'inactive') NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`content\` DROP COLUMN \`status\``);
  }
}
