import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  SupportResponseEntity,
  ResponseStatus,
} from '../entities/support-response.entity';

@Injectable()
export class SupportResponseRepository extends Repository<SupportResponseEntity> {
  constructor(
    @InjectRepository(SupportResponseEntity)
    private repository: Repository<SupportResponseEntity>,
  ) {
    super(repository.target, repository.manager, repository.queryRunner);
  }

  async findCompletedResponsesByTicket(ticketId: number) {
    return await this.find({
      where: {
        ticketId,
        status:
          ResponseStatus.COMPLETE ||
          ResponseStatus.COMPLETE_ADD ||
          ResponseStatus.AUTOMATED_REPLY,
      },
      relations: ['admin'],
      order: { createdAt: 'ASC' },
    });
  }

  async findAllResponsesByTicket(ticketId: number) {
    return await this.find({
      where: { ticketId },
      relations: ['admin'],
      order: { createdAt: 'ASC' },
    });
  }

  async findAdminResponse(responseId: number, adminId: number) {
    return await this.findOne({
      where: {
        id: responseId,
        adminId,
      },
      relations: ['ticket'],
    });
  }

  async hasCompletedResponse(ticketId: number): Promise<boolean> {
    const count = await this.count({
      where: {
        ticketId,
        status:
          ResponseStatus.COMPLETE ||
          ResponseStatus.COMPLETE_ADD ||
          ResponseStatus.AUTOMATED_REPLY,
      },
    });
    return count > 0;
  }

  async createAutoReply(ticketId: number, adminId: number, content: string) {
    const autoReply = this.create({
      ticketId,
      adminId,
      response: content,
      status: ResponseStatus.AUTOMATED_REPLY,
      responseCompletedTime: new Date(),
      isInternal: false,
    });

    return await this.save(autoReply);
  }
}
