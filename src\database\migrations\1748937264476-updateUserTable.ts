import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserTable1748937264476 implements MigrationInterface {
  name = 'UpdateUserTable1748937264476';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` CHANGE \`status\` \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`admins\` CHANGE \`status\` \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`admins\` CHANGE \`status\` \`status\` enum ('active', 'inactive', 'blocked', 'deleted') NOT NULL DEFAULT 'active'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` CHANGE \`status\` \`status\` enum ('active', 'inactive', 'blocked', 'deleted') NOT NULL DEFAULT 'active'`,
    );
  }
}
