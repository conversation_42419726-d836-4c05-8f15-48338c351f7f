import { <PERSON>du<PERSON> } from '@nestjs/common';
import { Setting<PERSON>ontroller } from './setting.controller';
import { SettingService } from './setting.service';
import { SettingRepository } from 'src/database/repositories/setting.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SettingEntity } from '../../../database/entities/setting.entity';
import { SettingMetadataRepository } from 'src/database/repositories/setting-metadat.repository';
import { SettingMetadata } from 'src/database/entities/setting_metadata.entity';
@Module({
  imports: [TypeOrmModule.forFeature([SettingEntity, SettingMetadata])],
  controllers: [SettingController],
  providers: [SettingService, SettingRepository, SettingMetadataRepository],
})
export class SettingModule {}
