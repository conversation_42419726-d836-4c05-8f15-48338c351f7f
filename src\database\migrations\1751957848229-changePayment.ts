import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangePayment1751957848229 implements MigrationInterface {
  name = 'ChangePayment1751957848229';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`subscription_plans\` CHANGE \`duration_month\` \`duration_day\` int NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`subscription_plans\` CHANGE \`duration_day\` \`duration_month\` int NOT NULL`,
    );
  }
}
