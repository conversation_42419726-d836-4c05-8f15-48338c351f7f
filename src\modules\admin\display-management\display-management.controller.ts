import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { DisplayManagementService } from './display-management.service';
import { CreateDisplayMultipleDto } from './dtos/create-display.dto';
import { DisplayType } from '../../../common/common.config';
import { ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { Roles } from '../../auth/decorators/role.decorator';
import { AdminRole } from '../../../common/role.enum';

@ApiTags('Admin: Display Management')
@Controller('display-management')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Roles(AdminRole.ADMIN, AdminRole.SUPER_ADMIN)
export class DisplayManagementController {
  constructor(
    private readonly displayManagementService: DisplayManagementService,
  ) {}

  @Post('create-display')
  async createDisplay(@Body() createDisplayDto: CreateDisplayMultipleDto) {
    return await this.displayManagementService.createDisplay(createDisplayDto);
  }

  @Get('get-display')
  @ApiQuery({
    name: 'type',
    type: String,
    enum: DisplayType,
    required: true,
  })
  async getDisplay(@Query('type') type: DisplayType) {
    return await this.displayManagementService.getDisplay(type);
  }
}
