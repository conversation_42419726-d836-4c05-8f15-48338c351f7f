import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { AiWebtoonCharacterTestedEntity } from '../entities/ai-webtoon-character-tested.entity';

@Injectable()
export class AiWebtoonCharacterTestedRepository extends Repository<AiWebtoonCharacterTestedEntity> {
  constructor(private dataSource: DataSource) {
    super(AiWebtoonCharacterTestedEntity, dataSource.createEntityManager());
  }
}
