import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AiApiGenStateManagementModule } from '../ai-api-gen-state-management/ai-api-gen-state-management.module';
import { AiServiceModule } from '../ai-service/ai-service.module';
import { NotificationGoogleModule } from 'src/modules/notification-google/notification-google.module';
import { AiWebtoonCharacterRepository } from 'src/database/repositories/ai-webtoon-character.repository';
import { AiWebtoonTrainingCharacterSessionRepository } from 'src/database/repositories/ai-webtoon-training-character-session.repository';
import { AiWebtoonTrainingLogRepository } from 'src/database/repositories/ai-webtoon-training-log.repository';
import { AiWebtoonTrainingLogModelRepository } from 'src/database/repositories/ai-webtoon-training-log-model.repository';
import { AiWebtoonCharacterTestedRepository } from 'src/database/repositories/ai-webtoon-character-tested.repository';
import { AiWebtoonCharacterController } from './ai-webtoon-character.controller';
import { AiWebtoonCharacterService } from './ai-webtoon-character.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AiWebtoonCharacterRepository,
      AiWebtoonTrainingCharacterSessionRepository,
      AiWebtoonTrainingLogRepository,
      AiWebtoonTrainingLogModelRepository,
      AiWebtoonCharacterTestedRepository,
    ]),
    AiServiceModule,
    AiApiGenStateManagementModule,
    NotificationGoogleModule,
  ],
  controllers: [AiWebtoonCharacterController],
  providers: [AiWebtoonCharacterService],
  exports: [AiWebtoonCharacterService],
})
export class AiWebtoonCharacterModule {}
