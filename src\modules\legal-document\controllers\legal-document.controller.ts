import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  Req,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { LegalDocumentService } from '../services/legal-document.service';
import { UserLegalDocumentFilterDto } from '../dtos/user-legal-document-filter.dto';
import { OptionalJwtAuthGuard } from '../../auth/guards/optional-jwt-auth.guard';

@ApiTags('Legal Documents')
@Controller('legal-documents')
export class LegalDocumentController {
  constructor(private readonly legalDocumentService: LegalDocumentService) {}

  @Get()
  @ApiOperation({
    summary: 'Get active legal documents list',
    description:
      'Get list of active legal documents, optionally filtered by type',
  })
  @ApiResponse({
    status: 200,
    description: 'Legal documents retrieved successfully',
  })
  async getLegalDocumentsList(@Query() filterDto: UserLegalDocumentFilterDto) {
    const documents =
      await this.legalDocumentService.getLegalDocumentsList(filterDto);

    return {
      status: 'success',
      message: 'Legal documents retrieved successfully',
      data: documents,
    };
  }

  @Get('id/:id')
  @UseGuards(OptionalJwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get legal document detail by ID',
    description:
      'Get legal document detail by ID and increment view count. Tracks analytics if user is authenticated.',
  })
  @ApiResponse({
    status: 200,
    description: 'Legal document retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Legal document not found',
  })
  async getLegalDocumentDetailById(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: any,
  ) {
    const userId = req.user?.id;
    const document = await this.legalDocumentService.getLegalDocumentDetailById(
      id,
      userId,
    );

    return {
      status: 'success',
      message: 'Legal document retrieved successfully',
      data: document,
    };
  }

  @Get(':identifier')
  @UseGuards(OptionalJwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get legal document detail by identifier',
    description:
      'Get legal document detail by name identifier and increment view count. Tracks analytics if user is authenticated.',
  })
  @ApiResponse({
    status: 200,
    description: 'Legal document retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Legal document not found',
  })
  async getLegalDocumentDetail(
    @Param('identifier') identifier: string,
    @Req() req: any,
  ) {
    const userId = req.user?.id;
    const document = await this.legalDocumentService.getLegalDocumentDetail(
      identifier,
      userId,
    );

    return {
      status: 'success',
      message: 'Legal document retrieved successfully',
      data: document,
    };
  }
}
