import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>Array,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';

export class CreateSceneDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  @Min(1)
  order: number;

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  cutIds?: number[];
}
