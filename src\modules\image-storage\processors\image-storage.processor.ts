import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { StatusService } from '../services/status.service';
import { StorageService } from '../services/storage.service';
import { ImageProcessingService } from '../services/image-processing.service';
import { WebhookService } from '../services/webhook.service';
import { UploadStatus } from '../dto/upload-response.dto';
import {
  ProcessImageJob,
  WebhookPayload,
} from '../interfaces/image-storage.interface';

@Processor('image-storage')
export class ImageStorageProcessor extends WorkerHost {
  private readonly logger = new Logger(ImageStorageProcessor.name);

  constructor(
    private readonly statusService: StatusService,
    private readonly storageService: StorageService,
    private readonly imageProcessingService: ImageProcessingService,
    private readonly webhookService: WebhookService,
  ) {
    super();
  }

  async process(job: Job<ProcessImageJob>) {
    const { uploadId, imageBuffer, metadata } = job.data;
    // Convert imageBuffer back to <PERSON>uffer if it was serialized as an object

    try {
      this.logger.log(`Starting image processing for ${uploadId}`);

      // Update status to processing
      await this.statusService.updateUploadStatus(
        uploadId,
        UploadStatus.PROCESSING,
        10,
      );
      // Step 1: Image optimization and thumbnail generation
      console.log('=== PROCESSOR DEBUG ===');
      console.log('imageBuffer type:', typeof imageBuffer);
      console.log('imageBuffer is Buffer?', Buffer.isBuffer(imageBuffer));
      console.log('imageBuffer constructor:', imageBuffer?.constructor?.name);

      if (imageBuffer) {
        if (Buffer.isBuffer(imageBuffer)) {
          console.log('Buffer length:', imageBuffer.length);
          console.log(
            'First 20 bytes:',
            imageBuffer.slice(0, 20).toString('hex'),
          );
        } else if (typeof imageBuffer === 'object') {
          console.log('Object keys:', Object.keys(imageBuffer).slice(0, 10));
          console.log(
            'Object values sample:',
            Object.values(imageBuffer).slice(0, 10),
          );
        }
      }

      this.logger.log(`Processing image for ${uploadId}`);

      // Handle BullMQ serialized Buffer format
      let bufferData: Buffer;
      if (Buffer.isBuffer(imageBuffer)) {
        bufferData = imageBuffer;
      } else if (
        imageBuffer &&
        typeof imageBuffer === 'object' &&
        (imageBuffer as any).type === 'Buffer' &&
        Array.isArray((imageBuffer as any).data)
      ) {
        // This is the serialized format from BullMQ
        bufferData = Buffer.from((imageBuffer as any).data);
      } else if (typeof imageBuffer === 'object') {
        // Fallback for other object formats
        bufferData = Buffer.from(Object.values(imageBuffer as any));
      } else {
        throw new Error(`Invalid imageBuffer format: ${typeof imageBuffer}`);
      }

      console.log('Converted buffer length:', bufferData.length);
      console.log(
        'Converted buffer first 20 bytes:',
        bufferData.slice(0, 20).toString('hex'),
      );
      console.log('======================');

      const processedImages = this.imageProcessingService.processImage(
        bufferData,
        metadata.type,
      );

      await this.statusService.updateUploadStatus(
        uploadId,
        UploadStatus.PROCESSING,
        40,
      );

      // Step 2: Save to storage
      this.logger.log(`Saving images to storage for ${uploadId}`);
      const storageResult = await this.storageService.saveImages(
        uploadId,
        processedImages,
        metadata,
      );

      await this.statusService.updateUploadStatus(
        uploadId,
        UploadStatus.PROCESSING,
        80,
      );

      // Step 3: Update final status
      this.logger.log(`Finalizing upload ${uploadId}`);
      await this.statusService.completeUpload(
        uploadId,
        storageResult.originalUrl,
        storageResult.thumbnailUrl,
        storageResult.sizes,
      );

      await this.statusService.updateUploadStatus(
        uploadId,
        UploadStatus.COMPLETED,
        100,
      );

      // return true;

      // Step 4: Send webhook notification
      const webhookPayload: WebhookPayload = {
        uploadId,
        status: UploadStatus.COMPLETED,
        imageUrl: storageResult.originalUrl,
        thumbnailUrl: storageResult.thumbnailUrl,
        metadata,
        createdAt: new Date().toISOString(),
        completedAt: new Date().toISOString(),
      };

      await this.webhookService.sendWebhook(webhookPayload);

      this.logger.log(
        `Image processing completed successfully for ${uploadId}`,
      );
      return webhookPayload;
    } catch (error) {
      this.logger.error(`Image processing failed for ${uploadId}:`, error);

      // Update status to failed
      await this.statusService.updateUploadStatus(
        uploadId,
        UploadStatus.FAILED,
        0,
        error.message,
      );

      // Send failure webhook
      const failurePayload: WebhookPayload = {
        uploadId,
        status: UploadStatus.FAILED,
        error: error.message,
        metadata,
        createdAt: new Date().toISOString(),
      };

      try {
        await this.webhookService.sendWebhook(failurePayload);
      } catch (webhookError) {
        this.logger.error(
          `Failed to send failure webhook for ${uploadId}:`,
          webhookError,
        );
      }

      throw error;
    }
  }

  /**
   * Handle job failure
   */
  async onFailed(job: Job<ProcessImageJob>, error: Error) {
    const { uploadId } = job.data;

    this.logger.error(`Job failed for upload ${uploadId}:`, error);

    // Final attempt to update status
    try {
      await this.statusService.updateUploadStatus(
        uploadId,
        UploadStatus.FAILED,
        0,
        `Job failed after ${job.attemptsMade} attempts: ${error.message}`,
      );
    } catch (statusError) {
      this.logger.error(
        `Failed to update final status for ${uploadId}:`,
        statusError,
      );
    }
  }
}
