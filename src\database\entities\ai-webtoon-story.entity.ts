import {
  EAiWebtoonBasicStatus,
  EAiWebtoonStoryBorderType,
} from 'src/common/ai-webtoon.enum';
import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm';
import { AdminEntity } from './admin.entity';
import { AiLetteringLanguageEntity } from './ai-lettering-language.entity';
import { AiWebtoonChapterEntity } from './ai-webtoon-chapter.entity';
import { AiWebtoonStoryAiLetteringEntity } from './ai-webtoon-story-ai-lettering.entity';
import { DefaultEntity } from './default.entity';
import { IAiGeneralCharacter } from 'src/common/interfaces/ai-webtoon/ai-webtoon-character.interface';

@Entity('ai_webtoon_story')
export class AiWebtoonStoryEntity extends DefaultEntity {
  @Column({ type: 'varchar' })
  title: string;

  @Column({ type: 'json', default: () => '(JSON_ARRAY())' })
  characters: IAiGeneralCharacter[];

  @Column({
    type: 'enum',
    enum: EAiWebtoonBasicStatus,
    name: 'status',
    default: EAiWebtoonBasicStatus.NOT_GENERATED,
  })
  status: EAiWebtoonBasicStatus;

  @Column({ name: 'is_ready', type: 'boolean', default: false })
  isReady: boolean;

  @Column({ name: 'is_hidden', type: 'boolean', default: false })
  isHidden: boolean;

  @Column({ name: 'font_family', type: 'varchar', default: 'Anime Ace' })
  fontFamily: string;

  @Column({ name: 'font_size', type: 'int', default: 12 })
  fontSize: number;

  @Column({ name: 'font_style', type: 'varchar', default: 'normal' })
  fontStyle: string;

  @Column({ name: 'font_weight', type: 'varchar', default: 'regular' })
  fontWeight: string;

  @Column({ name: 'dialogue_color', type: 'varchar', default: '#000000' })
  dialogueColor: string;

  @Column({ name: 'dialogue_font_style', type: 'varchar', default: 'normal' })
  dialogueFontStyle: string;

  @Column({ name: 'bubble_fill_color', type: 'varchar', default: '#000000' })
  bubbleFillColor: string;

  @Column({ name: 'bubble_stroke_color', type: 'varchar', default: '#000000' })
  bubbleStrokeColor: string;

  @Column({
    name: 'latest_chapter_updated_at',
    type: 'datetime',
    nullable: true,
    default: null,
  })
  latestChapterUpdatedAt: Date | null;

  @Column({
    name: 'border_type',
    type: 'enum',
    enum: EAiWebtoonStoryBorderType,
    default: EAiWebtoonStoryBorderType.FULL,
  })
  borderType: EAiWebtoonStoryBorderType;

  @Column({
    name: 'border_color',
    type: 'varchar',
    default: '#000000',
  })
  borderColor: string;

  @Column({
    name: 'border_weight',
    type: 'int',
    default: 2,
  })
  borderWeight: number;

  @Column({ name: 'ai_lettering_language_id', type: 'int' })
  aiLetteringLanguageId: number;

  @Column({ name: 'admin_id', type: 'int' })
  adminId: number;

  @ManyToOne(() => AdminEntity)
  @JoinColumn({ name: 'admin_id' })
  admin: AdminEntity;

  @ManyToOne(() => AiLetteringLanguageEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'ai_lettering_language_id' })
  aiLetteringLanguage: AiLetteringLanguageEntity;

  @OneToMany(
    () => AiWebtoonChapterEntity,
    (aiWebtoonChapter) => aiWebtoonChapter.aiWebtoonStory,
  )
  aiWebtoonChapters?: AiWebtoonChapterEntity[];

  @OneToMany(
    () => AiWebtoonStoryAiLetteringEntity,
    (aiWebtoonStoryAiLettering) => aiWebtoonStoryAiLettering.aiWebtoonStory,
  )
  aiWebtoonStoryAiLetterings: AiWebtoonStoryAiLetteringEntity[];

  numberOfChapters?: number;
  numberOfChaptersDone?: number;
}
