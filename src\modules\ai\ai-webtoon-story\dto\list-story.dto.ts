import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { DirectionSort } from 'src/common/common.enum';
import { PaginationDto } from 'src/modules/notification/dtos/pagination.dto';

export enum ListStoryWebtoonSearchBy {
  TITLE = 'title',
  ADMIN_NAME = 'admin_name',
}

export enum ListStoryWebtoonSearchByTime {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

export enum ListStoryWebtoonHidden {
  ALL = 'all',
  SHOW = 'show',
  HIDE = 'hide',
}

export class ListStoryWebtoonDto extends PaginationDto {
  @ApiProperty({ enum: ListStoryWebtoonHidden, required: false })
  @IsOptional()
  @IsEnum(ListStoryWebtoonHidden)
  hidden: ListStoryWebtoonHidden;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  search: string;

  @ApiProperty({ example: '2023-03-27', required: false })
  @IsString()
  @IsOptional()
  fromTime: string;

  @ApiProperty({ example: '2023-03-27', required: false })
  @IsString()
  @IsOptional()
  toTime: string;

  @ApiProperty({ enum: ListStoryWebtoonSearchBy, required: false })
  @IsOptional()
  @IsEnum(ListStoryWebtoonSearchBy)
  searchBy: ListStoryWebtoonSearchBy;

  @ApiProperty({ enum: ListStoryWebtoonSearchByTime, required: false })
  @IsOptional()
  @IsEnum(ListStoryWebtoonSearchByTime)
  searchByTime: ListStoryWebtoonSearchByTime;

  @ApiProperty({ enum: ListStoryWebtoonSearchByTime, required: false })
  @IsOptional()
  @IsEnum(ListStoryWebtoonSearchByTime)
  sortBy: ListStoryWebtoonSearchByTime;

  @ApiProperty({ enum: DirectionSort, required: false })
  @IsOptional()
  @IsEnum(DirectionSort)
  direction: DirectionSort;
}
