import { Column, Entity, ManyToOne } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { EpisodeEntity } from './episode.entity';

@Entity('episode_image')
export class EpisodeImageEntity extends DefaultEntity {
  @Column({ nullable: false })
  path: string;

  @Column({ type: 'int', nullable: true, default: 0 })
  order: number;

  @ManyToOne(() => EpisodeEntity, (episode) => episode.images)
  episode: EpisodeEntity;
}
