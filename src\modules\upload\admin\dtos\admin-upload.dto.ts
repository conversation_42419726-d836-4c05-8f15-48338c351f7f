import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength } from 'class-validator';

export class AdminFileUploadDto {
  @ApiProperty({
    description: 'The image file to upload',
    type: 'string',
    format: 'binary',
    required: true,
  })
  image: any;

  @ApiProperty({
    description: 'Optional description for the file',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;
}

export class AdminBulkUploadDto {
  @ApiProperty({
    description: 'The archive file (ZIP/RAR) to upload',
    type: 'string',
    format: 'binary',
    required: true,
  })
  archive: any;

  @ApiProperty({
    description: 'Description for the bulk upload',
    required: false,
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiProperty({
    description: 'Extract files automatically',
    required: false,
    default: true,
  })
  @IsOptional()
  autoExtract?: boolean;
}
