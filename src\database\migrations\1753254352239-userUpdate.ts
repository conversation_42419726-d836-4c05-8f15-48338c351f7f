import { MigrationInterface, QueryRunner } from 'typeorm';

export class UserUpdate1753254352239 implements MigrationInterface {
  name = 'UserUpdate1753254352239';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_97672ac88f789774dd47f7c8be\` ON \`users\``,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`IDX_c4dd678058708647766157c6e4\` ON \`users\` (\`email\`, \`deletedAt\`)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_c4dd678058708647766157c6e4\` ON \`users\``,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`IDX_97672ac88f789774dd47f7c8be\` ON \`users\` (\`email\`)`,
    );
  }
}
