import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ACCOUNT_DELETION_CONFIG } from '../../../config/account-deletion.config';

@Injectable()
export class RateLimitService {
  private readonly attemptStore = new Map<
    string,
    { count: number; resetTime: number }
  >();

  checkAccountDeletionRateLimit(userId: number) {
    const key = `account_deletion_attempts:${userId}`;
    const now = Date.now();
    const windowMs =
      ACCOUNT_DELETION_CONFIG.RATE_LIMIT_WINDOW_MINUTES * 60 * 1000;

    // Get current attempts
    const existing = this.attemptStore.get(key);

    // If no existing record or window has expired, start fresh
    if (!existing || now >= existing.resetTime) {
      this.attemptStore.set(key, {
        count: 1,
        resetTime: now + windowMs,
      });
      return;
    }

    // Check if limit exceeded
    if (existing.count >= ACCOUNT_DELETION_CONFIG.RATE_LIMIT_ATTEMPTS) {
      const remainingMinutes = Math.ceil(
        (existing.resetTime - now) / (1000 * 60),
      );
      throw new HttpException(
        `Too many account deletion attempts. Please try again in ${remainingMinutes} minutes.`,
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    // Increment attempts
    existing.count += 1;
    this.attemptStore.set(key, existing);
  }

  resetAccountDeletionAttempts(userId: number) {
    const key = `account_deletion_attempts:${userId}`;
    this.attemptStore.delete(key);
  }

  // Clean up expired entries periodically
  private cleanupExpiredEntries(): void {
    const now = Date.now();
    for (const [key, data] of this.attemptStore.entries()) {
      if (now >= data.resetTime) {
        this.attemptStore.delete(key);
      }
    }
  }

  constructor() {
    // Run cleanup every 15 minutes
    setInterval(() => this.cleanupExpiredEntries(), 15 * 60 * 1000);
  }
}
