import { Injectable } from '@nestjs/common';
import { AnalyticsQueueService } from '../../analytics/services/analytics-queue.service';

/**
 * Example integration for User Analytics tracking in Auth operations
 * This shows how to integrate analytics tracking for user authentication events
 */
@Injectable()
export class AuthAnalyticsIntegrationExample {
  constructor(private readonly analyticsQueueService: AnalyticsQueueService) {}

  /**
   * Example: Track user registration
   * This should be called after successful user registration
   */
  async trackUserRegistration(
    userId: number,
    platform?: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    await this.analyticsQueueService.trackUserActivity({
      userId,
      activityType: 'register',
      timestamp: Date.now(),
      platform: platform || 'pc',
      metadata: {
        source: 'registration',
        ...metadata,
      },
    });
  }

  /**
   * Example: Track user login
   * This should be called after successful login
   */
  async trackUserLogin(
    userId: number,
    platform?: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    await this.analyticsQueueService.trackUserActivity({
      userId,
      activityType: 'login',
      timestamp: Date.now(),
      platform: platform || 'pc',
      metadata: {
        source: 'login',
        ...metadata,
      },
    });
  }

  /**
   * Example: Track user account deletion
   * This should be called when user account is deleted
   */
  async trackUserAccountDeletion(
    userId: number,
    platform?: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    await this.analyticsQueueService.trackUserActivity({
      userId,
      activityType: 'delete',
      timestamp: Date.now(),
      platform: platform || 'pc',
      metadata: {
        source: 'account_deletion',
        ...metadata,
      },
    });
  }
}

/**
 * Example integration in AuthService
 *
 * @Injectable()
 * export class AuthService {
 *   constructor(
 *     private readonly analyticsQueueService: AnalyticsQueueService,
 *     // ... other dependencies
 *   ) {}
 *
 *   async login(loginDto: LoginDto, req: any): Promise<any> {
 *     // Existing login logic
 *     const result = await this.performLogin(loginDto);
 *
 *     // Track login analytics
 *     if (result.user) {
 *       await this.analyticsQueueService.trackUserActivity({
 *         userId: result.user.id,
 *         activityType: 'login',
 *         timestamp: Date.now(),
 *         platform: req.headers['user-agent']?.includes('Mobile') ? 'mobile' : 'pc',
 *         metadata: {
 *           source: 'login',
 *           userAgent: req.headers['user-agent'],
 *           ipAddress: req.ip,
 *         },
 *       });
 *     }
 *
 *     return result;
 *   }
 *
 *   async register(registerDto: RegisterDto, req: any): Promise<any> {
 *     // Existing registration logic
 *     const result = await this.performRegistration(registerDto);
 *
 *     // Track registration analytics
 *     if (result.user) {
 *       await this.analyticsQueueService.trackUserActivity({
 *         userId: result.user.id,
 *         activityType: 'register',
 *         timestamp: Date.now(),
 *         platform: req.headers['user-agent']?.includes('Mobile') ? 'mobile' : 'pc',
 *         metadata: {
 *           source: 'registration',
 *           userAgent: req.headers['user-agent'],
 *           ipAddress: req.ip,
 *         },
 *       });
 *     }
 *
 *     return result;
 *   }
 * }
 */
