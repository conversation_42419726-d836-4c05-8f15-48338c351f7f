import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { LegalAdminService } from '../services/legal-admin.service';
import { CreateLegalDocumentDto } from '../dtos/create-legal-document.dto';
import { UpdateLegalDocumentDto } from '../dtos/update-legal-document.dto';
import { LegalDocumentFilterDto } from '../dtos/legal-document-filter.dto';
import { DeleteLegalDocumentsDto } from '../dtos/delete-legal-documents.dto';
import { JwtAdminAuthGuard } from '../../auth-admin/guards/jwt-admin-auth.guard';
import { MenuPermissionGuard } from '../../auth-admin/guards/menu-permission.guard';
import { MenuPermissions } from '../../auth-admin/decorators/menu-permission.decorator';
import { LegalDocumentStatus } from '../../../../database/entities/legal-document.entity';

@ApiTags('Admin - Legal Documents')
@ApiBearerAuth()
@Controller('admin/legal-documents')
@UseGuards(JwtAdminAuthGuard, MenuPermissionGuard)
@MenuPermissions('legal-document-management')
export class LegalAdminController {
  constructor(private readonly legalAdminService: LegalAdminService) {}

  @Post()
  @ApiOperation({ summary: 'Create new legal document' })
  @ApiResponse({
    status: 201,
    description: 'Legal document created successfully',
  })
  async createLegalDocument(
    @Body() createDto: CreateLegalDocumentDto,
    @Req() req: any,
  ) {
    const adminId = req.user.id;
    const document = await this.legalAdminService.createLegalDocument(
      createDto,
      adminId,
    );

    return {
      status: 'success',
      message: 'Legal document created successfully',
      data: document,
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get legal documents list with filters' })
  @ApiResponse({
    status: 200,
    description: 'Legal documents retrieved successfully',
  })
  async getLegalDocumentsList(@Query() filterDto: LegalDocumentFilterDto) {
    const result =
      await this.legalAdminService.getLegalDocumentsList(filterDto);

    return {
      status: 'success',
      message: 'Legal documents retrieved successfully',
      ...result,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get legal document detail' })
  @ApiResponse({
    status: 200,
    description: 'Legal document retrieved successfully',
  })
  async getLegalDocumentDetail(@Param('id', ParseIntPipe) id: number) {
    const document = await this.legalAdminService.getLegalDocumentDetail(id);

    return {
      status: 'success',
      message: 'Legal document retrieved successfully',
      data: document,
    };
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update legal document' })
  @ApiResponse({
    status: 200,
    description: 'Legal document updated successfully',
  })
  async updateLegalDocument(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateLegalDocumentDto,
  ) {
    const document = await this.legalAdminService.updateLegalDocument(
      id,
      updateDto,
    );

    return {
      status: 'success',
      message: 'Legal document updated successfully',
      data: document,
    };
  }

  @Put(':id/status')
  @ApiOperation({ summary: 'Update legal document status' })
  @ApiResponse({
    status: 200,
    description: 'Legal document status updated successfully',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
          enum: Object.values(LegalDocumentStatus),
          description: 'New status of the legal document',
        },
      },
    },
  })
  async updateLegalDocumentStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { status: LegalDocumentStatus },
  ) {
    const document = await this.legalAdminService.updateLegalDocumentStatus(
      id,
      body.status,
    );

    return {
      status: 'success',
      message: 'Legal document status updated successfully',
      data: document,
    };
  }

  @Delete()
  @ApiOperation({
    summary: 'Delete multiple legal documents (soft delete)',
  })
  @ApiResponse({
    status: 200,
    description: 'Legal documents deleted successfully',
  })
  async deleteLegalDocuments(@Body() deleteDto: DeleteLegalDocumentsDto) {
    await this.legalAdminService.deleteLegalDocuments(deleteDto.ids);

    return {
      status: 'success',
      message: 'Legal documents deleted successfully',
    };
  }
}
