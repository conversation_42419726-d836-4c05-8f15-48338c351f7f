import { IsOptional, IsString } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class UpdateAuthorDto {
  @ApiProperty({
    description: 'Author name',
    example: 'name',
  })
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty({
    description: 'Author email',
    example: 'email',
  })
  @IsString()
  @IsOptional()
  email: string;

  @ApiProperty({
    description: 'Author bank number',
    example: 'bankNumber',
  })
  @IsString()
  @IsOptional()
  bankNumber: string;

  @ApiProperty({
    description: 'Author memo',
    example: 'memo',
  })
  @IsString()
  @IsOptional()
  memo: string;
}
