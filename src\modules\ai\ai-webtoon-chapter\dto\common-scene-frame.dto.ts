import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import {
  EAiBaseModelGenImageType,
  EAiBaseModelInpaintType,
  EAiBubbleType,
  EAiTextAlignment,
  EAiTypeActionByNormalCut,
  EAiWebtoonStoryBorderType,
} from 'src/common/ai-webtoon.enum';

export class AiCoordinateDto {
  @IsArray()
  head: number[];

  @IsArray()
  mid: number[];

  @IsArray()
  bubble: number[];
}

export class AiItemDialogueBubblesDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  characterUuid?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  content: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  contentJson: string;

  @ApiProperty()
  @ValidateNested()
  @Type(() => AiCoordinateDto)
  coordinate: AiCoordinateDto;

  @ApiProperty()
  @IsEnum(EAiBubbleType)
  @IsOptional()
  bubbleType: EAiBubbleType;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  bubbleBorderColor: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  bubbleBackgroundColor: string;

  @ApiProperty({ required: false })
  @IsOptional()
  bubbleWidth: number | 'auto';

  @ApiProperty({ required: false })
  @IsOptional()
  bubbleHeight: number | 'auto';

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  fontFamily: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  fontSize: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  fontStyle: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  fontWeight: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  bubbleFontColor: string;
}

export class ItemEffectsDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  image: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  rotate: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  width: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  height: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  x: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  y: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  scaleX: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  scaleY: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  zIndex: number;
}

export class ItemTextDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  text: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  textJson: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  rotate: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  scale: number;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  fontFamily: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  fontSize: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  fontStyle: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  fontWeight: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  fontColor: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  x: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  y: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  scaleX: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  scaleY: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  zIndex: number;

  @ApiProperty({ required: false, enum: EAiTextAlignment })
  @IsEnum(EAiTextAlignment)
  @IsOptional()
  textAlign: EAiTextAlignment;
}

class ItemImageCenterPointDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  x: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  y: number;
}

export class EditConfigDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  image?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  left?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  top?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  width?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  height?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  frameWidth?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  frameHeight?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  scaleX?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  scaleY?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  angle?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  cropX?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  cropY?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  cropWidth?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  cropHeight?: number;

  @ApiProperty({ required: false })
  @ValidateNested()
  @Type(() => ItemImageCenterPointDto)
  @IsOptional()
  imageCenterPoint?: ItemImageCenterPointDto;
}

export class BorderConfigDto {
  @ApiProperty({ enum: EAiWebtoonStoryBorderType })
  @IsNotEmpty()
  @IsEnum(EAiWebtoonStoryBorderType)
  borderType: EAiWebtoonStoryBorderType;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  borderColor: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  borderWeight: number;
}

export class ItemImageNormalDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  prompt?: string;

  @ApiProperty()
  @IsEnum(EAiTypeActionByNormalCut)
  type: EAiTypeActionByNormalCut;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  image: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  rootImage: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  finishedImage?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  editImage?: string;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  imageWidth: number;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  imageHeight: number;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  frameWidthImageContainer: number;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  frameHeightImageContainer: number;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  seed?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  sampler?: string;

  @ApiProperty({ required: false })
  @IsInt()
  @IsOptional()
  steps?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  cfgScale?: number;

  @ApiProperty({ required: false })
  @IsEnum(EAiBaseModelGenImageType || EAiBaseModelInpaintType)
  @IsOptional()
  baseModel?: EAiBaseModelGenImageType | EAiBaseModelInpaintType;

  @ApiProperty()
  @ValidateNested({ each: true })
  @Type(() => AiItemDialogueBubblesDto)
  @IsArray()
  bubbles: AiItemDialogueBubblesDto[];

  @ApiProperty()
  @ValidateNested({ each: true })
  @Type(() => ItemEffectsDto)
  @IsArray()
  effects: ItemEffectsDto[];

  @ApiProperty()
  @ValidateNested({ each: true })
  @Type(() => ItemTextDto)
  @IsArray()
  texts: ItemTextDto[];

  @ApiProperty({ required: false })
  @ValidateNested()
  @Type(() => EditConfigDto)
  @IsOptional()
  editConfig?: EditConfigDto;

  @ApiProperty({ required: false })
  @ValidateNested()
  @Type(() => BorderConfigDto)
  @IsOptional()
  borderConfig?: BorderConfigDto;
}

export class ItemImageInpaintDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  image: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  prompt: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  imageWidth: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  imageHeight: number;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  frameWidthImageContainer: number;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  frameHeightImageContainer: number;

  @ApiProperty()
  @IsArray()
  @IsString({ each: true })
  characters: string[];

  @ApiProperty()
  @IsEnum(EAiBaseModelInpaintType)
  baseModel: EAiBaseModelInpaintType;

  @ApiProperty()
  @ValidateNested({ each: true })
  @Type(() => AiItemDialogueBubblesDto)
  @IsArray()
  bubbles: AiItemDialogueBubblesDto[];

  @ApiProperty()
  @ValidateNested({ each: true })
  @Type(() => ItemEffectsDto)
  @IsArray()
  effects: ItemEffectsDto[];

  @ApiProperty()
  @ValidateNested({ each: true })
  @Type(() => ItemTextDto)
  @IsArray()
  texts: ItemTextDto[];

  @ApiProperty({ required: false })
  @ValidateNested()
  @Type(() => EditConfigDto)
  @IsOptional()
  editConfig?: EditConfigDto;
}

export class ItemImageSketchDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  image: string;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  imageWidth: number;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  imageHeight: number;
}
