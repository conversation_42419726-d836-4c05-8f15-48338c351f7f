import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AiWebtoonChapterService } from './ai-webtoon-chapter.service';

import { ListAiWebtoonChapterDto } from './dto/list-ai-webtoon-chapter.dto';
import { MergePreviewImagesDto } from './dto/merge-preview-images.dto';
import { ReceiveMergePreviewImagesDto } from './dto/receive-merge-preview-images.dto';
import { UpdateBgColorDto } from './dto/update-bg-color.dto';
import { UpdateWebtoonChapterDto } from './dto/update-webtoon-chapter.dto';
import { UploadFileChapterByGoogleDriveDto } from './dto/upload-file-chapter-by-google-drive.dto';
import { JwtAdminAuthGuard } from 'src/modules/admin/auth-admin/guards/jwt-admin-auth.guard';

@ApiTags('ai-webtoon-chapter')
@Controller('ai-webtoon-chapter')
export class AiWebtoonChapterController {
  constructor(
    private readonly aiWebtoonChapterService: AiWebtoonChapterService,
  ) {}

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/merge-preview-images')
  mergePreviewImages(@Body() body: MergePreviewImagesDto) {
    return this.aiWebtoonChapterService.mergePreviewImages(body);
  }

  @Post('/receive-merge-preview-images')
  receiveMergePreviewImages(@Body() body: ReceiveMergePreviewImagesDto) {
    return this.aiWebtoonChapterService.receiveMergePreviewImages(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/upload-file-google-drive-by-chapter/:id')
  uploadFileChapterByGoogleDrive(
    @Body() body: UploadFileChapterByGoogleDriveDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonChapterService.uploadFileChapterByGoogleDrive(
      id,
      body,
    );
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get()
  list(@Query() dto: ListAiWebtoonChapterDto, @Req() req: any) {
    const timezone = (req.headers.timezone as string) || 'Asia/Bangkok';
    return this.aiWebtoonChapterService.list(dto, timezone);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('/:id')
  detail(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonChapterService.detail(id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('/update-bg-color/:id')
  updateBgColor(
    @Body() body: UpdateBgColorDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonChapterService.updateBgColor(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('/:id')
  update(
    @Body() body: UpdateWebtoonChapterDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonChapterService.update(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Delete('/:id')
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonChapterService.delete(id);
  }
}
