import {
  IsString,
  Is<PERSON>ptional,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>rl,
  IsIn,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Length,
  IsEnum,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { ResponseStatus } from 'src/database/entities/support-response.entity';

// Q&A Status enum as per requirement
export enum QA_STATUS {
  UNANSWERED = 'unanswered',
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETE = 'complete',
  COMPLETE_ADD = 'complete_add',
  AUTOMATED_REPLY = 'automated_reply',
}

export class QAListDto {
  @ApiProperty({
    description: 'Date filter type',
    enum: ['submit_date', 'response_date'],
    required: false,
  })
  @IsOptional()
  @IsIn(['submit_date', 'response_date'])
  dateType?: 'submit_date' | 'response_date';

  @ApiProperty({
    description: 'From date (YYYY-MM-DD)',
    example: '2025-01-01',
    required: false,
  })
  @IsOptional()
  @IsString()
  fromTime?: string;

  @ApiProperty({
    description: 'To date (YYYY-MM-DD)',
    example: '2025-01-31',
    required: false,
  })
  @IsOptional()
  @IsString()
  toTime?: string;

  @ApiProperty({
    description: 'Q&A status filter',
    enum: [
      'all',
      'unanswered',
      'pending',
      'processing',
      'complete',
      'complete_add',
      'automated_reply',
    ],
    required: false,
  })
  @IsOptional()
  @IsIn([
    'all',
    'unanswered',
    'pending',
    'processing',
    'complete',
    'complete_add',
    'automated_reply',
  ])
  status?: 'all' | QA_STATUS;

  @ApiProperty({
    description: 'Read status filter',
    enum: ['all', 'true', 'false'],
    required: false,
  })
  @IsOptional()
  @IsIn(['all', 'true', 'false'])
  isRead?: 'all' | 'true' | 'false';

  @ApiProperty({
    description: 'Search type',
    enum: ['title', 'description', 'id', 'handler'],
    required: false,
  })
  @IsOptional()
  @IsIn(['title', 'description', 'id', 'handler'])
  searchType?: 'title' | 'description' | 'id' | 'handler';

  @ApiProperty({
    description: 'Search keyword (max 100 characters)',
    example: 'payment issue',
    required: false,
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  keyword?: string;

  @ApiProperty({
    description: 'Page number (min 1)',
    example: 1,
    required: false,
    minimum: 1,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  page?: number;

  @ApiProperty({
    description: 'Items per page (min 1)',
    example: 20,
    required: false,
    minimum: 1,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  take?: number;
}

export class QAAnswerCreateDto {
  @ApiProperty({
    description: 'Answer response content',
    example:
      'Thank you for your question. We have reviewed your account and found...',
  })
  @IsString()
  response: string;

  @ApiProperty({
    description: 'Response status after answering',
    enum: ResponseStatus,
    example: ResponseStatus.COMPLETE,
  })
  @IsEnum(ResponseStatus)
  status: ResponseStatus;

  @ApiProperty({
    description: 'Array of file URLs for attachments',
    example: ['https://example.com/solution.png'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  files?: string[];
}

export class QAMultipleAnswersDto {
  @ApiProperty({
    description: 'Array of answers to create for the Q&A question',
    type: [QAAnswerCreateDto],
    example: [
      {
        response: 'First response: Initial answer to your question...',
        status: 'complete',
        files: ['https://example.com/attachment1.png'],
      },
      {
        response: 'Second response: Additional information...',
        status: 'complete_add',
        files: [],
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QAAnswerCreateDto)
  answers: QAAnswerCreateDto[];
}

export class QAByUserIdDto {
  @ApiProperty({
    description: 'Page number',
    example: 1,
    required: false,
    minimum: 1,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  page?: number;

  @ApiProperty({
    description: 'Items per page',
    example: 20,
    required: false,
    minimum: 1,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  take?: number;
}

export class DeleteListQaDto {
  @ApiProperty({
    description: 'Array of Q&A question IDs to delete',
    example: [1, 2, 3, 4, 5],
    type: [Number],
  })
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  ids: number[];
}
