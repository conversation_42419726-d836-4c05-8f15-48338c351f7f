import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AiLetteringTypeRepository } from 'src/database/repositories/ai-lettering-type.repository';
import { AiLetteringRepository } from 'src/database/repositories/ai-lettering.repository';
import { AiLetteringTypeController } from './ai-lettering-type.controller';
import { AiLetteringTypeService } from './ai-lettering-type.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AiLetteringTypeRepository,
      AiLetteringRepository,
    ]),
  ],
  controllers: [AiLetteringTypeController],
  providers: [AiLetteringTypeService],
  exports: [AiLetteringTypeService],
})
export class AiLetteringTypeModule {}
