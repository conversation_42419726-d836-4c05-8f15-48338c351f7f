import { IsOptional, IsString, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class BaseAnalyticsV2QueryDto {
  @ApiProperty({
    description: 'Language filter (empty for all languages)',
    example: 'en',
    required: false,
  })
  @IsOptional()
  @IsString()
  language?: string;
}

export class HourlyAnalyticsV2QueryDto extends BaseAnalyticsV2QueryDto {
  @ApiProperty({
    description: 'Date for hourly analytics (YYYY/MM/DD)',
    example: '2025/01/15',
    required: true,
  })
  @IsString()
  @Matches(/^\d{4}\/\d{2}\/\d{2}$/, {
    message: 'Date must be in format YYYY/MM/DD',
  })
  date: string;
}

export class DailyAnalyticsV2QueryDto extends BaseAnalyticsV2QueryDto {
  @ApiProperty({
    description: 'Start date for daily analytics (YYYY/MM/DD)',
    example: '2025/01/15',
    required: true,
  })
  @IsString()
  @Matches(/^\d{4}\/\d{2}\/\d{2}$/, {
    message: 'Start date must be in format YYYY/MM/DD',
  })
  startDate: string;

  @ApiProperty({
    description: 'End date for daily analytics (YYYY/MM/DD)',
    example: '2025/03/28',
    required: true,
  })
  @IsString()
  @Matches(/^\d{4}\/\d{2}\/\d{2}$/, {
    message: 'End date must be in format YYYY/MM/DD',
  })
  endDate: string;
}

export class MonthlyAnalyticsV2QueryDto extends BaseAnalyticsV2QueryDto {
  @ApiProperty({
    description: 'Start month for analytics (YYYY/MM)',
    example: '2025/01',
    required: true,
  })
  @IsString()
  @Matches(/^\d{4}\/\d{2}$/, {
    message: 'Start month must be in format YYYY/MM',
  })
  startMonth: string;

  @ApiProperty({
    description: 'End month for analytics (YYYY/MM)',
    example: '2025/08',
    required: true,
  })
  @IsString()
  @Matches(/^\d{4}\/\d{2}$/, {
    message: 'End month must be in format YYYY/MM',
  })
  endMonth: string;
}
