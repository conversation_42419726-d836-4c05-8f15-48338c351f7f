import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsEnum, IsInt, IsOptional, IsString, Min } from 'class-validator';
import { EAiLetteringClassificationType } from 'src/common/ai-webtoon.enum';
import { PaginationDto } from 'src/modules/notification/dtos/pagination.dto';

export class ListAiLetteringDto extends PaginationDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }: { value: string }) => value?.trim())
  name: string;

  @ApiProperty({ enum: EAiLetteringClassificationType, required: false })
  @IsOptional()
  @IsEnum(EAiLetteringClassificationType)
  classification: EAiLetteringClassificationType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  aiLetteringLanguageId: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  aiLetteringTypeId: number;

  @ApiProperty({ example: '2023-03-27', required: false })
  @IsString()
  @IsOptional()
  startTime: string;

  @ApiProperty({ example: '2023-03-27', required: false })
  @IsString()
  @IsOptional()
  endTime: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  aiWebtoonStoryId: number;
}
