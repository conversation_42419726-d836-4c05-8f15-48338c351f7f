import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddContentSettingType1749051830763 implements MigrationInterface {
  name = 'AddContentSettingType1749051830763';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content_setting\` ADD \`type\` enum ('classification', 'date') NOT NULL DEFAULT 'classification'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content_setting\` DROP COLUMN \`type\``,
    );
  }
}
