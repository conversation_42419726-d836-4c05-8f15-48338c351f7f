import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { BorderConfigDto } from '../ai-webtoon-chapter/dto/common-scene-frame.dto';
import { CreateStoryWebtoonDto } from '../ai-webtoon-story/dto/create-story.dto';
import { CreateChapterManualDto } from '../ai-webtoon-story/dto/create-chapter-manual.dto';
import { AiWebtoonStoryService } from './ai-webtoon-story.service';
import { AddAiLetteringsDto } from './dto/add-ai-letterings.dto';
import { CopyChaptersDto } from './dto/copy-chapters.dto';
import { ListStoryWebtoonDto } from './dto/list-story.dto';
import { RemoveAiLetteringsDto } from './dto/remove-ai-letterings.dto';
import { UpdateFontConfigDto } from './dto/update-font-config.dto';
import { ChooseCharacterDto } from './dto/choose-character.dto';
import { RemoveCharacterDto } from './dto/remove-character.dto';
import { JwtAdminAuthGuard } from 'src/modules/admin/auth-admin/guards/jwt-admin-auth.guard';

@ApiTags('ai-webtoon-story')
@Controller('ai-webtoon-story')
export class AiWebtoonStory {
  constructor(private readonly aiWebtoonStoryService: AiWebtoonStoryService) {}

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('create-chapter/:id')
  createChapter(
    @Body() body: CreateChapterManualDto,
    @Param('id', ParseIntPipe) id: number,
    @Req() req,
  ) {
    return this.aiWebtoonStoryService.createChapter(id, body, req.user.id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('/choose-character/:id')
  chooseCharacter(
    @Body() body: ChooseCharacterDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonStoryService.chooseCharacter(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('/remove-character/:id')
  removeCharacter(
    @Body() body: RemoveCharacterDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonStoryService.removeCharacter(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('/update-font-config/:id')
  updateFontConfig(
    @Body() body: UpdateFontConfigDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonStoryService.updateFontConfig(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('/update-border-config/:id')
  updateBorderConfig(
    @Body() body: BorderConfigDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonStoryService.updateBorderConfig(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('/stories-used-by-character/:characterId')
  getStoriesUsedByCharacter(
    @Param('characterId', ParseIntPipe) characterId: number,
  ) {
    return this.aiWebtoonStoryService.getStoriesUsedByCharacter(characterId);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('copy-chapters-for-story/:id')
  copyChapters(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: CopyChaptersDto,
  ) {
    return this.aiWebtoonStoryService.copyChapters(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('update-hidden/:id')
  updateHidden(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonStoryService.updateHidden(id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/add-ai-letterings/:id')
  addAiLetterings(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: AddAiLetteringsDto,
  ) {
    return this.aiWebtoonStoryService.addAiLetterings(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Delete('/remove-ai-letterings/:id')
  removeAiLetterings(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: RemoveAiLetteringsDto,
  ) {
    return this.aiWebtoonStoryService.removeAiLetterings(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post()
  create(@Body() body: CreateStoryWebtoonDto, @Req() req) {
    return this.aiWebtoonStoryService.create(body, req.user.id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get()
  list(@Query() dto: ListStoryWebtoonDto, @Req() req) {
    const timezone = (req.headers.timezone as string) || 'Asia/Bangkok';
    return this.aiWebtoonStoryService.list(dto, timezone);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('/:id')
  detail(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonStoryService.detail(id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('/:id')
  update(
    @Body() body: CreateStoryWebtoonDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonStoryService.update(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Delete('/:id')
  delete(@Param('id', ParseIntPipe) id: number, @Req() req) {
    return this.aiWebtoonStoryService.delete(id, req.user.id);
  }
}
