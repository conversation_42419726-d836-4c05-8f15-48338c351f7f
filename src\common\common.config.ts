export enum Platform {
  PC = 'pc',
  MOBILE = 'mobile',
}

export enum UserType {
  DELETED = 'deleted',
  GENERAL = 'general',
  RESTRICTED = 'restricted',
}

export const DateFieldFilter = {
  SIGNUP_DATE: 'createdAt',
  VERIFIED_DATE: 'verifiedAt',
  RECENT_SIGNIN_DATE: 'lastSignInDate',
  DELETED_DATE: 'deletedAt',
  RESTRICTED_DATE: 'restrictedDate',
};

export const InputFieldFilter = {
  ID: 'id',
  EMAIL: 'email',
  REF: 'ref',
  SIGNUP_IP: 'signUpIp',
  RECENT_IP: 'recentIp',
};

export enum DisplayType {
  MAIN_BANNER = 'main_banner',
  GENRE_DISPLAY = 'genre_display',
  POPULAR_DISPLAY = 'popular_display',
  PARTNER_DISPLAY = 'partner_display',
}

export enum UserContentType {
  RECENTLY_READ = 'recently_read',
}

export const RecentlySortBy = {
  LATEST: 'latest',
  UPDATED: 'updated',
  TITLE: 'title',
};

export const RecentlySortOrder = {
  ASC: 'asc',
  DESC: 'desc',
};

export const Gender = {
  MALE: 'male',
  FEMALE: 'female',
  OTHER: 'other',
};

export const PaymentMethod = {
  STRIPE: 'stripe',
  PAYPAL: 'paypal',
};

export const adminNameSystem = 'admin';
