import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import configuration from 'src/config/configuration.global';
import { UserService } from '../../user/user.service';
interface JwtPayload {
  id: string;
  email: string;
  uuid: string;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly userService: UserService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configuration().jwt.secret || 'defaultSecret',
    });
  }

  async validate(payload: JwtPayload) {
    await this.userService.validateUser(payload.email, payload.uuid);
    return {
      id: payload.id,
      email: payload.email,
      uuid: payload.uuid,
    };
  }
}
