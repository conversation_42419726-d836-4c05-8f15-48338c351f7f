import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateFlowAITables1754989904426 implements MigrationInterface {
  name = 'CreateFlowAITables1754989904426';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`ai_webtoon_training_log\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`ai_webtoon_character_id\` int NOT NULL, \`ai_webtoon_training_character_session_id\` int NOT NULL, \`loss\` decimal(10,6) NOT NULL, \`percent\` decimal(5,2) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_webtoon_training_character_session\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`ai_webtoon_character_id\` int NOT NULL, \`images\` json NOT NULL DEFAULT (JSON_ARRAY()), \`url\` varchar(255) NULL, \`status\` enum ('in_progress', 'cancel', 'done') NOT NULL DEFAULT 'in_progress', \`end_date\` datetime NULL, \`description_prompt\` text NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_webtoon_character\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`name\` varchar(255) NOT NULL, \`avatar\` varchar(255) NOT NULL, \`gender\` enum ('male', 'female') NULL, \`status\` enum ('new', 'waiting', 'training', 'syncing', 'ready') NOT NULL DEFAULT 'new', \`images_traineds\` json NOT NULL DEFAULT (JSON_ARRAY()), \`images_preparing_trainings\` json NOT NULL DEFAULT (JSON_ARRAY()), \`type\` enum ('sdxl', 'flux', 'dream_booth', 'b_lora_style') NOT NULL DEFAULT 'flux', \`sample_prompts\` json NOT NULL DEFAULT (JSON_ARRAY()), \`model_url\` varchar(255) NULL, \`stories_id_used\` json NOT NULL DEFAULT (JSON_ARRAY()), \`api_gen_image_synceds\` json NOT NULL DEFAULT (JSON_ARRAY()), \`description_prompt_preparing_training\` text NULL, \`training_order_time\` datetime NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_webtoon_training_log_model\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`ai_webtoon_character_id\` int NOT NULL, \`ai_webtoon_training_character_session_id\` int NOT NULL, \`model_url\` varchar(255) NULL, \`epoch\` int NOT NULL, \`samples\` json NOT NULL DEFAULT (JSON_ARRAY()), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_webtoon_synthetic_data\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`story_name\` varchar(255) NOT NULL, \`flux_kontext_images\` json NOT NULL DEFAULT (JSON_ARRAY()), \`b_lora_images\` json NOT NULL DEFAULT (JSON_ARRAY()), \`status\` enum ('not_generated', 'generating', 'generated') NOT NULL DEFAULT 'not_generated', \`api_gen_name\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_lettering_language\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`title\` varchar(255) NOT NULL, \`priority\` int NOT NULL, \`is_default\` tinyint NOT NULL DEFAULT 0, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_webtoon_cut_chapter\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`ai_webtoon_scene_chapter_id\` int NOT NULL, \`ai_webtoon_chapter_id\` int NOT NULL, \`type\` enum ('normal', 'inpaint') NOT NULL DEFAULT 'normal', \`info\` text NULL, \`description\` text NULL, \`negative_description\` text NULL, \`general_prompt\` text NULL, \`negative_prompt\` text NULL, \`danbooru_prompt\` text NULL, \`is_generating_prompt\` tinyint NOT NULL DEFAULT 0, \`is_generating_negative_prompt\` tinyint NOT NULL DEFAULT 0, \`is_generating_danbooru_prompt\` tinyint NOT NULL DEFAULT 0, \`characters\` json NOT NULL DEFAULT (JSON_ARRAY()), \`images\` json NOT NULL DEFAULT (JSON_ARRAY()), \`image_index\` int NOT NULL DEFAULT '0', \`number_of_images\` int NOT NULL DEFAULT '1', \`total_number_of_images\` int NOT NULL DEFAULT '0', \`status_generate_image\` enum ('not_generated', 'waiting', 'generating', 'generated') NOT NULL DEFAULT 'not_generated', \`controlnet_canny_images\` json NOT NULL DEFAULT (JSON_ARRAY()), \`controlnet_depth_images\` json NOT NULL DEFAULT (JSON_ARRAY()), \`controlnet_canny_index\` int NOT NULL DEFAULT '0', \`controlnet_depth_index\` int NOT NULL DEFAULT '0', \`control_net_strength_canny\` decimal(5,3) NOT NULL DEFAULT '0.600', \`control_net_strength_depth\` decimal(5,3) NOT NULL DEFAULT '0.600', \`bg_image\` varchar(255) NULL, \`api_gen_name\` varchar(255) NULL, \`image_generate_type\` enum ('one', 'multiple') NULL, \`is_preview\` tinyint NOT NULL DEFAULT 0, \`order\` int NOT NULL DEFAULT '1', \`order_by_preview\` int NULL, \`image_size_type\` enum ('default', 'vertical', 'horizontal_left', 'horizontal_right', 'controlnet_canny', 'controlnet_depth') NOT NULL DEFAULT 'default', \`image_width\` int NOT NULL DEFAULT '688', \`image_height\` int NOT NULL DEFAULT '720', \`base_model\` enum ('FLUX_NORMAL', 'FLUX_ADULT', 'SDXL_4_OPT', 'DREAM_BOOTH') NOT NULL DEFAULT 'FLUX_NORMAL', \`seed\` varchar(255) NULL, \`cfg_scale\` decimal(12,6) NOT NULL DEFAULT '5.000000', \`steps\` int NOT NULL DEFAULT '28', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_webtoon_scene_chapter\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`ai_webtoon_chapter_id\` int NOT NULL, \`title\` varchar(255) NOT NULL, \`description\` text NULL, \`negative_prompt\` text NULL, \`prompt\` text NULL, \`number_of_images\` int NOT NULL DEFAULT '1', \`width\` int NOT NULL DEFAULT '688', \`height\` int NOT NULL DEFAULT '720', \`images\` json NOT NULL DEFAULT (JSON_ARRAY()), \`image_size_type\` enum ('default', 'vertical', 'horizontal_left', 'horizontal_right', 'controlnet_canny', 'controlnet_depth') NOT NULL DEFAULT 'default', \`order\` int NOT NULL DEFAULT '1', \`status_generate_image\` enum ('not_generated', 'generating', 'generated') NOT NULL DEFAULT 'not_generated', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_webtoon_chapter\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`ai_webtoon_story_id\` int NOT NULL, \`admin_id\` int NOT NULL, \`title\` varchar(255) NOT NULL, \`chapter\` decimal(20,2) NOT NULL, \`file\` varchar(255) NULL, \`file_name\` varchar(255) NULL, \`status\` enum ('not_generated', 'generating', 'generated') NOT NULL DEFAULT 'not_generated', \`statusGenerateImage\` enum ('not_generated', 'generating', 'generated', 'done') NOT NULL DEFAULT 'not_generated', \`bg_color\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_lettering_type\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`title\` varchar(255) NOT NULL, \`priority\` int NOT NULL, \`is_default\` tinyint NOT NULL DEFAULT 0, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_lettering\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`ai_lettering_language_id\` int NULL, \`ai_lettering_type_id\` int NULL, \`name\` varchar(255) NOT NULL, \`classification\` enum ('all', 'normal', 'adult') NOT NULL DEFAULT 'all', \`extension\` varchar(255) NOT NULL, \`url\` varchar(255) NOT NULL, \`thumbnail_url\` varchar(255) NOT NULL, UNIQUE INDEX \`IDX_db5a248ed892ba880198904a2f\` (\`name\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_webtoon_story_ai_lettering\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`ai_webtoon_story_id\` int NOT NULL, \`ai_lettering_id\` int NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_webtoon_story\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`title\` varchar(255) NOT NULL, \`characters\` json NOT NULL DEFAULT (JSON_ARRAY()), \`status\` enum ('not_generated', 'generating', 'generated') NOT NULL DEFAULT 'not_generated', \`is_ready\` tinyint NOT NULL DEFAULT 0, \`is_hidden\` tinyint NOT NULL DEFAULT 0, \`font_family\` varchar(255) NOT NULL DEFAULT 'Anime Ace', \`font_size\` int NOT NULL DEFAULT '12', \`font_style\` varchar(255) NOT NULL DEFAULT 'normal', \`font_weight\` varchar(255) NOT NULL DEFAULT 'regular', \`dialogue_color\` varchar(255) NOT NULL DEFAULT '#000000', \`dialogue_font_style\` varchar(255) NOT NULL DEFAULT 'normal', \`bubble_fill_color\` varchar(255) NOT NULL DEFAULT '#000000', \`bubble_stroke_color\` varchar(255) NOT NULL DEFAULT '#000000', \`latest_chapter_updated_at\` datetime NULL, \`border_type\` enum ('none', 'full', 'left', 'right', 'top', 'bottom', 'top_bottom', 'left_right') NOT NULL DEFAULT 'full', \`border_color\` varchar(255) NOT NULL DEFAULT '#000000', \`border_weight\` int NOT NULL DEFAULT '2', \`ai_lettering_language_id\` int NOT NULL, \`admin_id\` int NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_webtoon_prepare_character\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`name\` varchar(255) NOT NULL, \`gender\` enum ('male', 'female') NOT NULL DEFAULT 'male', \`note\` text NULL, \`status\` enum ('not_generated', 'generating', 'generated') NOT NULL DEFAULT 'not_generated', \`admin_id\` int NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_webtoon_prepare_character_generated\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`ai_webtoon_prepare_character_id\` int NOT NULL, \`ai_webtoon_character_id\` int NULL, \`admin_id\` int NOT NULL, \`prompt\` text NULL, \`collection_images\` json NOT NULL DEFAULT (JSON_ARRAY()), \`cut_images\` json NOT NULL DEFAULT (JSON_ARRAY()), \`number_of_images\` int NOT NULL DEFAULT '0', \`lora_strength\` decimal(5,3) NULL DEFAULT '1.000', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_webtoon_order_generate_cut_chapter\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`chapter_id\` int NOT NULL, \`cuts_id\` json NOT NULL DEFAULT (JSON_ARRAY()), \`type\` enum ('normal', 'inpaint') NOT NULL DEFAULT 'normal', \`cut_type\` enum ('normal', 'inpaint') NOT NULL DEFAULT 'normal', \`image_generate_type\` enum ('one', 'multiple') NOT NULL DEFAULT 'one', \`data\` json NOT NULL DEFAULT (JSON_OBJECT()), \`api_gen_name\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_webtoon_character_tested\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`ai_webtoon_character_id_1\` int NULL, \`ai_webtoon_character_id_2\` int NULL, \`admin_id\` int NOT NULL, \`prompt\` text NULL, \`bg\` text NULL, \`prompt_character_1\` text NULL, \`prompt_character_2\` text NULL, \`images\` json NOT NULL DEFAULT (JSON_ARRAY()), \`type\` enum ('one', 'two') NOT NULL DEFAULT 'one', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_api_gen_state_management\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`name\` varchar(255) NOT NULL, \`field_in_env\` varchar(255) NOT NULL, \`type\` enum ('gen_image_flux', 'gen_background_image', 'inpaint_image') NOT NULL DEFAULT 'gen_image_flux', \`status\` tinyint NOT NULL DEFAULT 0, \`is_maintenance\` tinyint NOT NULL DEFAULT 1, \`start_time\` datetime NULL, UNIQUE INDEX \`IDX_e2511ec7a520813586e333cccf\` (\`name\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_training_log\` ADD CONSTRAINT \`FK_901d890cb107f1457cec089ec3f\` FOREIGN KEY (\`ai_webtoon_character_id\`) REFERENCES \`ai_webtoon_character\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_training_log\` ADD CONSTRAINT \`FK_d2491bd1c8ed04c521e353461b3\` FOREIGN KEY (\`ai_webtoon_training_character_session_id\`) REFERENCES \`ai_webtoon_training_character_session\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_training_character_session\` ADD CONSTRAINT \`FK_ee8f42ecf508c0aa5e0491f7995\` FOREIGN KEY (\`ai_webtoon_character_id\`) REFERENCES \`ai_webtoon_character\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_training_log_model\` ADD CONSTRAINT \`FK_e674a26914bcaba811ae578a5aa\` FOREIGN KEY (\`ai_webtoon_character_id\`) REFERENCES \`ai_webtoon_character\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_training_log_model\` ADD CONSTRAINT \`FK_6d0a98f8138b79d78e1ecb05216\` FOREIGN KEY (\`ai_webtoon_training_character_session_id\`) REFERENCES \`ai_webtoon_training_character_session\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_cut_chapter\` ADD CONSTRAINT \`FK_66c63d428ea3e847ca6f2e50a35\` FOREIGN KEY (\`ai_webtoon_chapter_id\`) REFERENCES \`ai_webtoon_chapter\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_cut_chapter\` ADD CONSTRAINT \`FK_70242ef38747e7fb839e7708250\` FOREIGN KEY (\`ai_webtoon_scene_chapter_id\`) REFERENCES \`ai_webtoon_scene_chapter\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_scene_chapter\` ADD CONSTRAINT \`FK_eba53007ff0a1bca0daae1c9d04\` FOREIGN KEY (\`ai_webtoon_chapter_id\`) REFERENCES \`ai_webtoon_chapter\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_chapter\` ADD CONSTRAINT \`FK_6c2944fd96d8d88f50d38ff8715\` FOREIGN KEY (\`admin_id\`) REFERENCES \`admins\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_chapter\` ADD CONSTRAINT \`FK_c2a7e67f79d732278a83e0e2026\` FOREIGN KEY (\`ai_webtoon_story_id\`) REFERENCES \`ai_webtoon_story\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_lettering\` ADD CONSTRAINT \`FK_42d12a0bdbe7e776734f29eb35b\` FOREIGN KEY (\`ai_lettering_language_id\`) REFERENCES \`ai_lettering_language\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_lettering\` ADD CONSTRAINT \`FK_289dd101b48b2599cd50ea31839\` FOREIGN KEY (\`ai_lettering_type_id\`) REFERENCES \`ai_lettering_type\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_story_ai_lettering\` ADD CONSTRAINT \`FK_83c0a0d69afa7e70a8d1203cca0\` FOREIGN KEY (\`ai_webtoon_story_id\`) REFERENCES \`ai_webtoon_story\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_story_ai_lettering\` ADD CONSTRAINT \`FK_aa6848ab54837bc22e3274bde61\` FOREIGN KEY (\`ai_lettering_id\`) REFERENCES \`ai_lettering\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_story\` ADD CONSTRAINT \`FK_46ce5f04c44b69768b505f9a7a8\` FOREIGN KEY (\`admin_id\`) REFERENCES \`admins\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_story\` ADD CONSTRAINT \`FK_01d4cf59a5757337e50f976a0f7\` FOREIGN KEY (\`ai_lettering_language_id\`) REFERENCES \`ai_lettering_language\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_prepare_character\` ADD CONSTRAINT \`FK_70aa848f4f8e8d345a40dba7094\` FOREIGN KEY (\`admin_id\`) REFERENCES \`admins\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_prepare_character_generated\` ADD CONSTRAINT \`FK_ebebbd347b94c4a414ffe6cd8cb\` FOREIGN KEY (\`ai_webtoon_character_id\`) REFERENCES \`ai_webtoon_character\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_prepare_character_generated\` ADD CONSTRAINT \`FK_c0cbe2dc913d183fcb2173b3503\` FOREIGN KEY (\`ai_webtoon_prepare_character_id\`) REFERENCES \`ai_webtoon_prepare_character\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_prepare_character_generated\` ADD CONSTRAINT \`FK_4e7a6e730a97837734d1bcb4c56\` FOREIGN KEY (\`admin_id\`) REFERENCES \`admins\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_order_generate_cut_chapter\` ADD CONSTRAINT \`FK_95423a9c7b880bce3a5ff80e40d\` FOREIGN KEY (\`chapter_id\`) REFERENCES \`ai_webtoon_chapter\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_character_tested\` ADD CONSTRAINT \`FK_9864b7aaa9d6d40a284ed0ba588\` FOREIGN KEY (\`ai_webtoon_character_id_1\`) REFERENCES \`ai_webtoon_character\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_character_tested\` ADD CONSTRAINT \`FK_201f1efbff3d0a3572715bd5f00\` FOREIGN KEY (\`ai_webtoon_character_id_2\`) REFERENCES \`ai_webtoon_character\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_character_tested\` ADD CONSTRAINT \`FK_45dbf46094846ee6aed493722bd\` FOREIGN KEY (\`admin_id\`) REFERENCES \`admins\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_character_tested\` DROP FOREIGN KEY \`FK_45dbf46094846ee6aed493722bd\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_character_tested\` DROP FOREIGN KEY \`FK_201f1efbff3d0a3572715bd5f00\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_character_tested\` DROP FOREIGN KEY \`FK_9864b7aaa9d6d40a284ed0ba588\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_order_generate_cut_chapter\` DROP FOREIGN KEY \`FK_95423a9c7b880bce3a5ff80e40d\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_prepare_character_generated\` DROP FOREIGN KEY \`FK_4e7a6e730a97837734d1bcb4c56\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_prepare_character_generated\` DROP FOREIGN KEY \`FK_c0cbe2dc913d183fcb2173b3503\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_prepare_character_generated\` DROP FOREIGN KEY \`FK_ebebbd347b94c4a414ffe6cd8cb\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_prepare_character\` DROP FOREIGN KEY \`FK_70aa848f4f8e8d345a40dba7094\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_story\` DROP FOREIGN KEY \`FK_01d4cf59a5757337e50f976a0f7\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_story\` DROP FOREIGN KEY \`FK_46ce5f04c44b69768b505f9a7a8\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_story_ai_lettering\` DROP FOREIGN KEY \`FK_aa6848ab54837bc22e3274bde61\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_story_ai_lettering\` DROP FOREIGN KEY \`FK_83c0a0d69afa7e70a8d1203cca0\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_lettering\` DROP FOREIGN KEY \`FK_289dd101b48b2599cd50ea31839\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_lettering\` DROP FOREIGN KEY \`FK_42d12a0bdbe7e776734f29eb35b\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_chapter\` DROP FOREIGN KEY \`FK_c2a7e67f79d732278a83e0e2026\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_chapter\` DROP FOREIGN KEY \`FK_6c2944fd96d8d88f50d38ff8715\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_scene_chapter\` DROP FOREIGN KEY \`FK_eba53007ff0a1bca0daae1c9d04\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_cut_chapter\` DROP FOREIGN KEY \`FK_70242ef38747e7fb839e7708250\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_cut_chapter\` DROP FOREIGN KEY \`FK_66c63d428ea3e847ca6f2e50a35\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_training_log_model\` DROP FOREIGN KEY \`FK_6d0a98f8138b79d78e1ecb05216\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_training_log_model\` DROP FOREIGN KEY \`FK_e674a26914bcaba811ae578a5aa\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_training_character_session\` DROP FOREIGN KEY \`FK_ee8f42ecf508c0aa5e0491f7995\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_training_log\` DROP FOREIGN KEY \`FK_d2491bd1c8ed04c521e353461b3\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_webtoon_training_log\` DROP FOREIGN KEY \`FK_901d890cb107f1457cec089ec3f\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_e2511ec7a520813586e333cccf\` ON \`ai_api_gen_state_management\``,
    );
    await queryRunner.query(`DROP TABLE \`ai_api_gen_state_management\``);
    await queryRunner.query(`DROP TABLE \`ai_webtoon_character_tested\``);
    await queryRunner.query(
      `DROP TABLE \`ai_webtoon_order_generate_cut_chapter\``,
    );
    await queryRunner.query(
      `DROP TABLE \`ai_webtoon_prepare_character_generated\``,
    );
    await queryRunner.query(`DROP TABLE \`ai_webtoon_prepare_character\``);
    await queryRunner.query(`DROP TABLE \`ai_webtoon_story\``);
    await queryRunner.query(`DROP TABLE \`ai_webtoon_story_ai_lettering\``);
    await queryRunner.query(
      `DROP INDEX \`IDX_db5a248ed892ba880198904a2f\` ON \`ai_lettering\``,
    );
    await queryRunner.query(`DROP TABLE \`ai_lettering\``);
    await queryRunner.query(`DROP TABLE \`ai_lettering_type\``);
    await queryRunner.query(`DROP TABLE \`ai_webtoon_chapter\``);
    await queryRunner.query(`DROP TABLE \`ai_webtoon_scene_chapter\``);
    await queryRunner.query(`DROP TABLE \`ai_webtoon_cut_chapter\``);
    await queryRunner.query(`DROP TABLE \`ai_lettering_language\``);
    await queryRunner.query(`DROP TABLE \`ai_webtoon_synthetic_data\``);
    await queryRunner.query(`DROP TABLE \`ai_webtoon_training_log_model\``);
    await queryRunner.query(`DROP TABLE \`ai_webtoon_character\``);
    await queryRunner.query(
      `DROP TABLE \`ai_webtoon_training_character_session\``,
    );
    await queryRunner.query(`DROP TABLE \`ai_webtoon_training_log\``);
  }
}
