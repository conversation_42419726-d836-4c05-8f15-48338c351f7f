import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { UserEntity } from '../../database/entities/user.entity';
import { UserAdminDto } from '../admin/user-admin/dtos/user-admin.dto';
import { plainToInstance } from 'class-transformer';
import { SanctionRepository } from '../../database/repositories/sanction.repository';
import { UserRepository } from '../../database/repositories/user.repository';
import { UserSanctionRequestDto } from '../admin/user-admin/dtos/user-sanction-request.dto';
import { SanctionEntity } from '../../database/entities/sanction.entity';
import { DateFieldFilter } from '../../common/common.config';
import { ChangePasswordByAdminDto } from './dtos/change-password-admin.dto';
import * as bcrypt from 'bcrypt';
import { SanctionType } from '../../common/setting.enum';
import { MESSAGE_CONFIG } from '../../common/message.config';
import { CommonStatus } from '../../common/status.enum';
import { SessionRepository } from '../../database/repositories/session.repository';
import { UpdateProfileDto } from './dtos/update-profile.dto';
import { SessionEntity } from '../../database/entities/session.entity';
import { PaymentEntity } from '../../database/entities/payment.entity';
import { PaymentRepository } from '../../database/repositories/payment.repository';
import {
  DeleteAccountDto,
  AccountDeletionRequirementsDto,
  AccountDeletionResponseDto,
} from './dtos/delete-account.dto';
import {
  ACCOUNT_DELETION_CONFIG,
  getAccountDeletionRequirements,
} from '../../config/account-deletion.config';
import { AccountDeletionAuditRepository } from '../../database/repositories/account-deletion-audit.repository';
import { RateLimitService } from './services/rate-limit.service';
import { AnalyticsQueueService } from '../analytics/services/analytics-queue.service';
import { PaymentStatus } from '../../common/status.enum';
import { v4 as uuid } from 'uuid';
import {
  CheckPasswordDto,
  CheckPasswordResponseDto,
} from './dtos/check-password.dto';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(UserEntity)
    private usersRepository: UserRepository,
    private sanctionRepository: SanctionRepository,
    private sessionRepository: SessionRepository,
    private paymentRepository: PaymentRepository,
    private accountDeletionAuditRepository: AccountDeletionAuditRepository,
    private rateLimitService: RateLimitService,
    private analyticsQueueService: AnalyticsQueueService,
  ) {}

  async findByEmail(email: string) {
    const user = await this.usersRepository.findOne({ where: { email } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }

  async findById(id: number) {
    const user = await this.usersRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }

  async getProfile(id: number) {
    const user = await this.usersRepository.findOne({
      where: { id, sessions: { status: CommonStatus.ACTIVE } },
      relations: [
        'sessions',
        'subscriptions',
        'subscriptions.subscriptionPlan',
        'payments',
      ],
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return plainToInstance(UserEntity, user);
  }

  async getPaymentHistory(id: number, limit: number, page: number) {
    const payments = await this.paymentRepository.find({
      where: { user: { id } },
      relations: ['subscription', 'subscription.subscriptionPlan'],
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    const total = await this.paymentRepository.count({
      where: { user: { id } },
    });

    return {
      data: plainToInstance(PaymentEntity, payments),
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      limit,
    };
  }

  async updateProfile(id: number, updateProfileDto: UpdateProfileDto) {
    try {
      const user = await this.usersRepository.findOne({ where: { id } });
      if (!user) {
        throw new NotFoundException(MESSAGE_CONFIG.USER_NOT_FOUND);
      }
      if (updateProfileDto.oldPassword) {
        const isPasswordValid = await bcrypt.compare(
          updateProfileDto.oldPassword,
          user.password,
        );
        if (!isPasswordValid) {
          throw new BadRequestException(MESSAGE_CONFIG.INVALID_OLD_PASSWORD);
        }

        if (updateProfileDto.newPassword) {
          user.password = await bcrypt.hash(updateProfileDto.newPassword, 10);
        }
      }

      if (updateProfileDto.gender) {
        user.gender = updateProfileDto.gender;
      }
      if (updateProfileDto.isMatureContent) {
        user.isMatureContent = updateProfileDto.isMatureContent;
      }
      if (updateProfileDto.agreeUseInfomation) {
        user.agreeUseInfomation = updateProfileDto.agreeUseInfomation;
        user.agreeUseInfomationAt = Date.now();
      }
      if (updateProfileDto.isAdultVerified !== undefined) {
        user.isAdultVerified = updateProfileDto.isAdultVerified;
      }
      await this.usersRepository.save(user);
      return { message: 'Profile updated successfully' };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async getDevices(id: number, limit: number, page: number) {
    const user = await this.usersRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const queryBuilder = this.sessionRepository
      .createQueryBuilder('session')
      .leftJoinAndSelect('session.user', 'user')
      .where('session.userId = :id', { id })
      .andWhere('session.status = :status', { status: CommonStatus.ACTIVE });

    const sessions = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    console.log(sessions);

    const total = await queryBuilder.getCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data: plainToInstance(SessionEntity, sessions),
      total,
      totalPages,
      currentPage: page,
      limit,
    };
  }

  async getUsers(query: UserAdminDto) {
    try {
      const {
        userType,
        platform,
        dateFieldFilter,
        dateFieldFilterStart,
        dateFieldFilterEnd,
        search,
        email,
        page = 1,
        limit = 10,
        inputFieldFilter,
      } = query;

      const queryBuilder = this.usersRepository
        .createQueryBuilder('user')
        .withDeleted()
        .leftJoinAndSelect('user.sanctions', 'sanctions');

      if (userType) {
        switch (userType) {
          case 'general':
            // Users who are not deleted and either have no sanctions or all sanctions have expired
            queryBuilder.andWhere('user.deletedAt IS NULL');
            queryBuilder.andWhere(
              '(NOT EXISTS (SELECT 1 FROM sanction WHERE sanction.userId = user.id) OR ' +
                '(NOT EXISTS (SELECT 1 FROM sanction WHERE sanction.userId = user.id AND sanction.endDate IS NULL) AND ' +
                'NOT EXISTS (SELECT 1 FROM sanction WHERE sanction.userId = user.id AND sanction.endDate > :currentDate)))',
              {
                currentDate: new Date(),
              },
            );
            break;

          case 'restricted':
            // Users who have active sanctions (permanent or not expired yet)
            queryBuilder.andWhere(
              '(EXISTS (SELECT 1 FROM sanction WHERE sanction.userId = user.id AND ((sanction.endDate IS NULL OR sanction.endDate > :currentDate) OR sanction.type = :sanctionType)))',
              {
                currentDate: new Date(),
                sanctionType: SanctionType.PERMANENT,
              },
            );
            break;

          case 'deleted':
            queryBuilder.andWhere('user.deletedAt IS NOT NULL');
            break;
        }
      }

      if (platform) {
        queryBuilder.andWhere('user.platform = :platform', { platform });
      }

      if (dateFieldFilter === DateFieldFilter.SIGNUP_DATE) {
        queryBuilder.andWhere('user.createdAt >= :dateFieldFilterStart', {
          dateFieldFilterStart,
        });
        queryBuilder.andWhere('user.createdAt <= :dateFieldFilterEnd', {
          dateFieldFilterEnd,
        });
      }

      if (
        dateFieldFilter &&
        dateFieldFilter !== DateFieldFilter.RESTRICTED_DATE
      ) {
        queryBuilder.andWhere(
          `user.${dateFieldFilter} >= :dateFieldFilterStart`,
          {
            dateFieldFilterStart,
          },
        );
        queryBuilder.andWhere(
          `user.${dateFieldFilter} <= :dateFieldFilterEnd`,
          {
            dateFieldFilterEnd,
          },
        );
      } else if (dateFieldFilter === DateFieldFilter.RESTRICTED_DATE) {
        queryBuilder.andWhere(
          'user.sanctions.startDate >= :dateFieldFilterStart OR user.sanctions.startDate IS NULL',
          {
            dateFieldFilterStart,
          },
        );
        queryBuilder.andWhere(
          'user.sanctions.startDate <= :dateFieldFilterEnd OR user.sanctions.startDate IS NULL',
          {
            dateFieldFilterEnd,
          },
        );
      }

      if (search) {
        queryBuilder.andWhere(`user.${inputFieldFilter} LIKE :search`, {
          search: `%${search}%`,
        });
      }

      // Email search (shortcut filter)
      if (email) {
        queryBuilder.andWhere('user.email LIKE :email', {
          email: `%${email}%`,
        });
      }

      const allUsers = await queryBuilder.getMany();

      const allUsersEmailId = allUsers.map((user) => {
        return {
          id: user.id,
          email: user.email,
        };
      });

      queryBuilder.skip((page - 1) * limit).take(limit);

      const users = await queryBuilder.getMany();
      const total = await queryBuilder.getCount();
      const totalPages = Math.ceil(total / limit);

      return {
        data: plainToInstance(UserEntity, users),
        allUsersEmailId,
        total,
        totalPages,
        currentPage: page,
        limit,
      };
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.message);
    }
  }

  async getUserById(id: number) {
    const user = await this.usersRepository.findOne({
      where: { id },
      relations: [
        'sanctions',
        'sanctions.admin',
        'payments',
        'subscriptions',
        'subscriptions.subscriptionPlan',
        'sessions',
      ],
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return plainToInstance(UserEntity, user);
  }

  async sanctionUser(body: UserSanctionRequestDto, admin: any) {
    const user = await this.usersRepository.findOne({
      where: { id: body.userId },
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const sanction = await this.sanctionRepository.save({
      type: body.type,
      reason: body.reason,
      adminNote: body.adminNote,
      startDate: body.startDate || null,
      endDate: body.endDate || null,
      admin: admin.adminId,
      user: user,
    });

    return plainToInstance(SanctionEntity, sanction);
  }

  async changePasswordByAdmin(body: ChangePasswordByAdminDto) {
    try {
      const user = await this.usersRepository.findOne({
        where: { id: body.userId },
      });
      if (!user) {
        throw new NotFoundException('User not found');
      }
      await this.usersRepository.update(user.id, {
        password: await bcrypt.hash(body.password, 10),
      });
      return { message: 'Password changed successfully' };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async validateUser(email: string, uuid: string) {
    const session = await this.sessionRepository.findOne({
      where: { uuid, status: CommonStatus.ACTIVE },
    });
    if (!session) {
      throw new UnauthorizedException(MESSAGE_CONFIG.SESSION_NOT_FOUND);
    }
    const user = await this.usersRepository.findOne({
      where: { email },
      relations: ['sanctions'],
    });
    if (!user) {
      throw new BadRequestException({
        message: 'User not found',
        statusCode: 400,
        error: 'User not found',
      });
    }

    if (user.sanctions.length > 0) {
      const currentDate = new Date();
      const isExpired = user.sanctions.some(
        (sanction) => sanction.endDate && sanction.endDate < currentDate,
      );
      if (isExpired) {
        throw new BadRequestException(MESSAGE_CONFIG.USER_SANCTION_NOT_EXPIRED);
      }
    }

    return user;
  }

  // Account Deletion Methods

  getAccountDeletionRequirements(): AccountDeletionRequirementsDto {
    return getAccountDeletionRequirements();
  }

  async deleteAccount(
    userId: number,
    dto: DeleteAccountDto,
  ): Promise<AccountDeletionResponseDto> {
    // 1. Rate limiting check
    this.rateLimitService.checkAccountDeletionRateLimit(userId);

    // 2. Get user with relations
    const user = await this.usersRepository.findOne({
      where: { id: userId },
      relations: ['subscriptions', 'payments', 'sessions'],
    });

    if (!user) {
      throw new NotFoundException(MESSAGE_CONFIG.USER_NOT_FOUND);
    }

    // 3. Core validations
    await this.validateAccountDeletion(user, dto);

    // 4. Conditional validations based on config
    this.applyConditionalValidations(dto);

    // 5. Check deletion eligibility
    await this.checkDeletionEligibility(user);

    // 6. Process deletion
    const deletionId = uuid();
    const result = await this.processDeletion(user, dto, deletionId);

    // 7. Reset rate limit on successful deletion
    this.rateLimitService.resetAccountDeletionAttempts(userId);

    return result;
  }

  private async validateAccountDeletion(
    user: UserEntity,
    dto: DeleteAccountDto,
  ): Promise<void> {
    // Validate current password
    const isPasswordValid = await bcrypt.compare(
      dto.currentPassword,
      user.password,
    );
    if (!isPasswordValid) {
      throw new BadRequestException('Invalid current password');
    }
  }

  private applyConditionalValidations(dto: DeleteAccountDto) {
    // CAPTCHA validation (if enabled)
    if (ACCOUNT_DELETION_CONFIG.CAPTCHA_ENABLED) {
      if (!dto.captchaToken) {
        throw new BadRequestException('CAPTCHA token is required');
      }
      this.validateCaptcha(dto.captchaToken);
    }

    // Note: confirmText validation is handled by DTO validation decorator
  }

  private validateCaptcha(captchaToken: string): void {
    // TODO: Implement CAPTCHA validation with your preferred provider
    // For now, we'll just validate that the token is not empty
    if (!captchaToken || captchaToken.trim().length === 0) {
      throw new BadRequestException('Invalid CAPTCHA token');
    }
  }

  private async checkDeletionEligibility(user: UserEntity): Promise<void> {
    // Check for active subscriptions
    const activeSubscriptions = user.subscriptions?.filter(
      (sub) => sub.status === CommonStatus.ACTIVE && !sub.deletedAt,
    );

    if (activeSubscriptions && activeSubscriptions.length > 0) {
      throw new ForbiddenException(
        'Cannot delete account with active subscriptions. Please cancel your subscriptions first.',
      );
    }

    // Check for pending payments
    const pendingPayments = await this.paymentRepository.find({
      where: {
        user: { id: user.id },
        status: PaymentStatus.PENDING,
      },
    });

    if (pendingPayments.length > 0) {
      throw new ForbiddenException(
        'Cannot delete account with pending payments. Please resolve pending payments first.',
      );
    }

    // TODO: Add more eligibility checks as needed
    // - Check for open support tickets
    // - Check for outstanding debts
    // - Check for admin restrictions
  }

  private async processDeletion(
    user: UserEntity,
    dto: DeleteAccountDto,
    deletionId: string,
  ): Promise<AccountDeletionResponseDto> {
    // Create audit record
    const scheduledDeletionAt =
      ACCOUNT_DELETION_CONFIG.GRACE_PERIOD_DAYS > 0
        ? new Date(
            Date.now() +
              ACCOUNT_DELETION_CONFIG.GRACE_PERIOD_DAYS * 24 * 60 * 60 * 1000,
          )
        : null;

    await this.accountDeletionAuditRepository.createAuditRecord({
      userId: user.id,
      deletionId,
      reason: dto.reason,
      scheduledDeletionAt: scheduledDeletionAt || undefined,
    });

    // Track analytics
    try {
      await this.analyticsQueueService.trackUserActivity({
        userId: user.id,
        activityType: 'account_deletion',
        platform: 'web', // Could be determined from request headers
        timestamp: Date.now(),
        metadata: {
          reason: dto.reason,
          hasActiveSubscription:
            user.subscriptions?.some(
              (sub) => sub.status === CommonStatus.ACTIVE,
            ) || false,
          accountAge: this.calculateAccountAge(user.createdAt),
          gracePeriod: ACCOUNT_DELETION_CONFIG.GRACE_PERIOD_DAYS,
        },
      });
    } catch (error) {
      console.error('Failed to track account deletion analytics:', error);
      // Don't throw - analytics failure shouldn't block account deletion
    }

    // Process deletion based on grace period
    if (ACCOUNT_DELETION_CONFIG.GRACE_PERIOD_DAYS > 0) {
      return await this.scheduleGracePeriodDeletion(
        user,
        deletionId,
        scheduledDeletionAt!,
      );
    } else {
      return await this.immediateAccountDeletion(user, deletionId);
    }
  }

  private async scheduleGracePeriodDeletion(
    user: UserEntity,
    deletionId: string,
    scheduledDeletionAt: Date,
  ): Promise<AccountDeletionResponseDto> {
    // Mark user for deletion but don't delete immediately
    await this.usersRepository.update(user.id, {
      // Add deletion fields to user entity if needed
      // deletionScheduledAt: scheduledDeletionAt,
      // deletionId: deletionId,
    });

    return {
      success: true,
      message: `Account deletion scheduled. You have ${ACCOUNT_DELETION_CONFIG.GRACE_PERIOD_DAYS} days to recover your account.`,
      data: {
        deletionId,
        gracePeriodUntil: scheduledDeletionAt,
        dataRetentionPeriod: 30, // Days to keep data after deletion
      },
    };
  }

  private async immediateAccountDeletion(
    user: UserEntity,
    deletionId: string,
  ): Promise<AccountDeletionResponseDto> {
    await this.performAccountDeletion(user);

    // Mark audit as completed
    await this.accountDeletionAuditRepository.markAsCompleted(deletionId);

    return {
      success: true,
      message: 'Account deleted successfully',
      data: {
        deletionId,
        dataRetentionPeriod: 30,
      },
    };
  }

  private async performAccountDeletion(user: UserEntity): Promise<void> {
    // Soft delete approach - anonymize data but keep records
    const anonymizedEmail = `deleted_user_${user.id}@deleted.local`;

    await this.usersRepository.update(user.id, {
      email: anonymizedEmail,
      // Clear personal information
      // name: 'Deleted User',
      // phone: null,
      deletedAt: new Date(),
    });

    // Deactivate all sessions
    await this.sessionRepository.update(
      { user: { id: user.id } },
      { status: CommonStatus.INACTIVE },
    );

    // TODO: Add more cleanup operations
    // - Cancel active subscriptions
    // - Archive payment history (don't delete for compliance)
    // - Clean up analytics data (GDPR compliance)
    // - Clean up uploaded files
    // - Notify other services
  }

  private calculateAccountAge(createdAt: Date): number {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - createdAt.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // Days
  }

  async checkPassword(
    userId: number,
    checkPasswordDto: CheckPasswordDto,
  ): Promise<CheckPasswordResponseDto> {
    try {
      const user = await this.usersRepository.findOne({
        where: { id: userId },
        select: ['id', 'password'],
      });

      if (!user) {
        throw new NotFoundException(MESSAGE_CONFIG.USER_NOT_FOUND);
      }

      const isPasswordValid = await bcrypt.compare(
        checkPasswordDto.password,
        user.password,
      );

      if (isPasswordValid) {
        return {
          isValid: true,
          message: 'Password is valid',
        };
      } else {
        return {
          isValid: false,
          message: 'Invalid password',
        };
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to verify password');
    }
  }
}
