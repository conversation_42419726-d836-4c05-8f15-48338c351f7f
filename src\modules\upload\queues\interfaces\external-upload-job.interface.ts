import { UploadContext } from '../../../../database/entities/upload.entity';

export interface ExternalUploadJobData {
  type: UploadContext;
  imagePath: string;
  uploadUrl: string;
  metadata?: {
    userId?: number;
    uploadId?: number;
    description?: string;
    [key: string]: any;
  };
}

export interface ExternalUploadJobResult {
  success: boolean;
  originalPath: string;
  externalUrl?: string;
  uploadResponse?: any;
  error?: string;
}

export interface ExternalUploadRequest {
  imagePath: string;
  uploadUrl: string;
  signature: string;
  publicKey: string;
  type: UploadContext;
  metadata?: any;
}
