import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { GenreEntity } from '../entities/genre.entity';

@Injectable()
export class GenreRepository extends Repository<GenreEntity> {
  constructor(private dataSource: DataSource) {
    super(GenreEntity, dataSource.createEntityManager());
  }

  async findOneById(id: number) {
    const genre = await this.findOne({ where: { id } });
    return genre;
  }
}
