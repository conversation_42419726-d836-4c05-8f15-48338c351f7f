import { Injectable } from '@nestjs/common';
import * as path from 'path';
import * as fs from 'fs/promises';
import * as sharp from 'sharp';
import { UPLOAD_CONFIG } from '../../config/upload.config';

@Injectable()
export class FileProcessingService {
  async generateThumbnail(
    originalFilePath: string,
    relativeFilePath: string,
  ): Promise<string | null> {
    try {
      // Create thumbnail directory
      const thumbnailDir = path.join(
        process.cwd(),
        UPLOAD_CONFIG.baseUploadPath,
        'thumbnails',
        path.dirname(relativeFilePath),
      );

      await fs.mkdir(thumbnailDir, { recursive: true });

      // Generate thumbnail filename
      const originalFileName = path.basename(originalFilePath);
      const thumbnailFileName = `thumb_${originalFileName}`;
      const thumbnailPath = path.join(thumbnailDir, thumbnailFileName);

      // Generate thumbnail using sharp
      await sharp(originalFilePath)
        .resize(
          UPLOAD_CONFIG.thumbnailSize.width,
          UPLOAD_CONFIG.thumbnailSize.height,
          {
            fit: 'inside',
            withoutEnlargement: true,
          },
        )
        .jpeg({ quality: 80 })
        .toFile(thumbnailPath);

      // Return relative path for database storage
      return `/${UPLOAD_CONFIG.baseUploadPath}/thumbnails/${relativeFilePath}/${thumbnailFileName}`;
    } catch (error) {
      console.error('Failed to generate thumbnail:', error);
      return null;
    }
  }

  async extractMetadata(file: any, filePath?: string): Promise<any> {
    const metadata: any = {
      originalName: file.originalname,
      size: file.size,
      mimeType: file.mimetype,
      uploadedAt: new Date().toISOString(),
    };

    // Extract image metadata
    if (file.mimetype.startsWith('image/') && filePath) {
      try {
        const imageMetadata = await sharp(filePath).metadata();
        metadata.image = {
          width: imageMetadata.width,
          height: imageMetadata.height,
          format: imageMetadata.format,
          hasAlpha: imageMetadata.hasAlpha,
          colorspace: imageMetadata.space,
        };
      } catch (error) {
        console.error('Failed to extract image metadata:', error);
        metadata.image = { error: 'Failed to extract image metadata' };
      }
    }

    // Extract document metadata (basic)
    if (file.mimetype === 'application/pdf') {
      metadata.document = {
        type: 'pdf',
        // Could add PDF-specific metadata extraction here
      };
    }

    if (file.mimetype.includes('word') || file.mimetype.includes('document')) {
      metadata.document = {
        type: 'word',
        // Could add Word document metadata extraction here
      };
    }

    return metadata;
  }

  async deleteFile(filePath: string): Promise<boolean> {
    try {
      const fullPath = path.join(process.cwd(), filePath.replace(/^\//, ''));
      await fs.unlink(fullPath);

      // Also delete thumbnail if exists
      const thumbnailPath = this.getThumbnailPath(filePath);
      if (thumbnailPath) {
        try {
          const fullThumbnailPath = path.join(
            process.cwd(),
            thumbnailPath.replace(/^\//, ''),
          );
          await fs.unlink(fullThumbnailPath);
        } catch (error) {
          // Thumbnail deletion failure is not critical
          console.warn('Failed to delete thumbnail:', error);
        }
      }

      return true;
    } catch (error) {
      console.error('Failed to delete file:', error);
      return false;
    }
  }

  async optimizeImage(filePath: string): Promise<void> {
    try {
      const tempPath = `${filePath}.tmp`;

      await sharp(filePath)
        .jpeg({ quality: 85, progressive: true })
        .png({ compressionLevel: 6 })
        .toFile(tempPath);

      // Replace original with optimized version
      await fs.rename(tempPath, filePath);
    } catch (error) {
      console.error('Failed to optimize image:', error);
      // Clean up temp file if it exists
      try {
        await fs.unlink(`${filePath}.tmp`);
      } catch {
        // Ignore cleanup errors
      }
    }
  }

  generateFileName(originalName: string): string {
    const ext = path.extname(originalName);
    const name = path
      .basename(originalName, ext)
      .replace(/[^a-zA-Z0-9-_]/g, '_') // Replace special chars with underscore
      .substring(0, 100); // Limit length

    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);

    return `${name}_${timestamp}_${random}${ext}`;
  }

  private getThumbnailPath(originalPath: string): string | null {
    if (!originalPath.includes('/uploads/')) {
      return null;
    }

    const relativePath = originalPath.replace(/^.*\/uploads\//, '');
    const fileName = path.basename(relativePath);
    const dirPath = path.dirname(relativePath);

    return `/${UPLOAD_CONFIG.baseUploadPath}/thumbnails/${dirPath}/thumb_${fileName}`;
  }
}
