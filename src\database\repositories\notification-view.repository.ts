import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  NotificationViewEntity,
  NotificationType,
} from '../entities/notification-view.entity';

@Injectable()
export class NotificationViewRepository extends Repository<NotificationViewEntity> {
  constructor(
    @InjectRepository(NotificationViewEntity)
    private repository: Repository<NotificationViewEntity>,
  ) {
    super(repository.target, repository.manager, repository.queryRunner);
  }

  async markAsViewed(
    userId: number,
    notificationType: NotificationType,
    notificationId: number,
  ) {
    const existingView = await this.findOne({
      where: { userId, notificationType, notificationId },
    });

    if (!existingView) {
      return await this.save({
        userId,
        notificationType,
        notificationId,
      });
    }

    return existingView;
  }

  async hasViewed(
    userId: number,
    notificationType: NotificationType,
    notificationId: number,
  ): Promise<boolean> {
    const count = await this.count({
      where: { userId, notificationType, notificationId },
    });

    return count > 0;
  }

  async getViewCount(
    notificationType: NotificationType,
    notificationId: number,
  ): Promise<number> {
    return await this.count({
      where: { notificationType, notificationId },
    });
  }

  async checkIfViewed(
    userId: number,
    notificationType: NotificationType,
    notificationId: number,
  ): Promise<boolean> {
    const existingView = await this.findOne({
      where: { userId, notificationType, notificationId },
    });

    return !!existingView;
  }
}
