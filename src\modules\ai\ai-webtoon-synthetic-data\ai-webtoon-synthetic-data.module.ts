import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AiWebtoonCharacterRepository } from 'src/database/repositories/ai-webtoon-character.repository';
import { AiWebtoonTrainingCharacterSessionRepository } from 'src/database/repositories/ai-webtoon-training-character-session.repository';
import { AiWebtoonSyntheticDataRepository } from '../../../database/repositories/ai-webtoon-synthetic-data.repository';
import { AiApiGenStateManagementModule } from '../ai-api-gen-state-management/ai-api-gen-state-management.module';
import { AiWebtoonSyntheticDataController } from './ai-webtoon-synthetic-data.controller';
import { AiWebtoonSyntheticDataService } from './ai-webtoon-synthetic-data.service';
import { AiServiceModule } from '../ai-service/ai-service.module';
// import { SocketModule } from '../socket/socket.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AiWebtoonSyntheticDataRepository,
      AiWebtoonCharacterRepository,
      AiWebtoonTrainingCharacterSessionRepository,
    ]),
    AiApiGenStateManagementModule,
    AiServiceModule,
    // SocketModule,
  ],
  controllers: [AiWebtoonSyntheticDataController],
  providers: [AiWebtoonSyntheticDataService],
  exports: [AiWebtoonSyntheticDataService],
})
export class AiWebtoonSyntheticDataModule {}
