import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { ContentEntity } from '../entities/content.entity';
@Injectable()
export class ContentRepository extends Repository<ContentEntity> {
  constructor(private dataSource: DataSource) {
    super(ContentEntity, dataSource.createEntityManager());
  }

  async updateViewCount(id: number) {
    await this.update(id, { viewCount: () => 'viewCount + 1' });
  }
}
