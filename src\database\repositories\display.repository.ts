import { DataSource, Repository } from 'typeorm';
import { Injectable, NotFoundException } from '@nestjs/common';
import { DisplayEntity } from '../entities/display.entity';
import { MESSAGE_CONFIG } from '../../common/message.config';
@Injectable()
export class DisplayRepository extends Repository<DisplayEntity> {
  constructor(private dataSource: DataSource) {
    super(DisplayEntity, dataSource.createEntityManager());
  }

  async findOneById(id: number) {
    const display = await this.findOne({ where: { id } });
    if (!display) {
      throw new NotFoundException(MESSAGE_CONFIG.DISPLAY_NOT_FOUND);
    }
    return display;
  }
}
