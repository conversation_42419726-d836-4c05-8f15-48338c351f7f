import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
// import { firstValueFrom } from 'rxjs';
import {
  EAiApiGenStateManagementType,
  EAiDirectionApi,
  EAiTestingModel,
  EAiWebtoonCharacterStatus,
  EAiWebtoonCharacterTestedType,
  EAiWebtoonCharacterType,
  EAiWebtoonTrainingCharacterSessionStatus,
} from 'src/common/ai-webtoon.enum';
// import { CacheService } from 'src/common/cache/cache.service';
import { AI_MESSAGE_CONFIG } from 'src/common/message.config';
// import { HttpService } from '@nestjs/axios';
import {
  IGenerateSamplePromptsRequest,
  ISendTestingDataRequest,
} from 'src/common/interfaces/ai-webtoon/ai-send-request.interface';
import { AiWebtoonCharacterEntity } from 'src/database/entities/ai-webtoon-character.entity';
import { AiWebtoonTrainingLogEntity } from 'src/database/entities/ai-webtoon-training-log.entity';
import { AiWebtoonCharacterTestedRepository } from 'src/database/repositories/ai-webtoon-character-tested.repository';
import { AiWebtoonCharacterRepository } from 'src/database/repositories/ai-webtoon-character.repository';
import { AiWebtoonTrainingCharacterSessionRepository } from 'src/database/repositories/ai-webtoon-training-character-session.repository';
import { AiWebtoonTrainingLogModelRepository } from 'src/database/repositories/ai-webtoon-training-log-model.repository';
import { AiWebtoonTrainingLogRepository } from 'src/database/repositories/ai-webtoon-training-log.repository';
import { NotificationGoogleService } from 'src/modules/notification-google/notification-google.service';
import { EntityManager, In } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { AiApiGenStateManagementService } from '../ai-api-gen-state-management/ai-api-gen-state-management.service';
import { AiService } from '../ai-service/ai-service.service';
import { CharacterTrainingDto } from './dto/character-training.dto';
import { CreateAiWebtoonCharacterDto } from './dto/create-ai-webtoon-character.dto';
import { GenerateDescriptionLabelByImagesDto } from './dto/generate-description-label-by-images.dto';
import { GetSampleTestingDto } from './dto/get-sample-testing.dto';
import {
  FILTER_AI_WEBTOON_CHARACTER_GENDER,
  FILTER_AI_WEBTOON_CHARACTER_STATUS,
  ListAiWebtoonCharacterDto,
} from './dto/list-ai-character.dto';
import { ListTrainingSessionByCharacterDto } from './dto/list-training-session-by-character.dto';
import { ReceiveLogCharacterTrainingDto } from './dto/receive-log-character-training.dto';
import {
  ReceiveLogModelSyncSampleDto,
  ReceiveLogModelSyncUrlModelDto,
} from './dto/receive-log-model-sync.dto';
import { ReceiveSyncedCharacterDto } from './dto/receive-synced-character.dto';
import { ReceiveTestingDto } from './dto/receive-testing.dto';
import {
  CreateSamplePromptDto,
  DeleteSamplePromptDto,
} from './dto/sample-prompts.dto';
import {
  CharacterTestingDto,
  SaveCharacterTestingDto,
} from './dto/save-character-testing.dto';

@Injectable()
export class AiWebtoonCharacterService {
  constructor(
    private readonly aiWebtoonCharacterRepository: AiWebtoonCharacterRepository,
    private readonly aiWebtoonTrainingCharacterSessionRepository: AiWebtoonTrainingCharacterSessionRepository,
    private readonly aiWebtoonTrainingLogRepository: AiWebtoonTrainingLogRepository,
    private readonly aiWebtoonTrainingLogModelRepository: AiWebtoonTrainingLogModelRepository,
    private readonly aiWebtoonCharacterTestedRepository: AiWebtoonCharacterTestedRepository,
    private readonly aiApiGenStateManagementService: AiApiGenStateManagementService,
    private readonly aiService: AiService,
    // private readonly cacheService: CacheService,
    private readonly notificationGoogleService: NotificationGoogleService,
  ) {}
  private readonly logger = new Logger(AiWebtoonCharacterService.name);

  async create(body: CreateAiWebtoonCharacterDto) {
    const result = await this.aiWebtoonCharacterRepository.save(body);
    return { id: result?.id };
  }

  softDelete(id: number) {
    return this.aiWebtoonCharacterRepository.softDelete(id);
  }

  async update(id: number, body: CreateAiWebtoonCharacterDto) {
    const character = await this.aiWebtoonCharacterRepository.findOne({
      where: { id },
    });
    if (!character)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND);

    const data = {
      ...character,
      ...body,
    };
    await this.aiWebtoonCharacterRepository.save(data);
    return true;
  }

  async listWaitingAndOrderNumber(type: EAiWebtoonCharacterType) {
    const characters = await this.aiWebtoonCharacterRepository.find({
      where: {
        status: EAiWebtoonCharacterStatus.WAITING,
        type,
      },
      order: {
        trainingOrderTime: 'ASC',
      },
    });

    const result = characters.map((character, index) => ({
      id: character.id,
      orderNumber: index + 1,
    }));

    return result;
  }

  async list(dto: ListAiWebtoonCharacterDto) {
    const query = this.aiWebtoonCharacterRepository
      .createQueryBuilder('aiWtCharacter')
      .leftJoinAndSelect(
        'aiWtCharacter.session',
        'session',
        'session.status = :sessionStatus',
        { sessionStatus: EAiWebtoonTrainingCharacterSessionStatus.DONE },
      )
      .where('aiWtCharacter.type = :type', { type: dto.type });

    if (dto.name) {
      query.andWhere('aiWtCharacter.name LIKE :name', {
        name: `%${dto.name}%`,
      });
    }

    if (
      dto.status &&
      dto.status !== (FILTER_AI_WEBTOON_CHARACTER_STATUS.ALL as string)
    ) {
      query.andWhere('aiWtCharacter.status = :status', {
        status: dto.status,
      });
    }

    if (
      dto.gender &&
      dto.gender !== (FILTER_AI_WEBTOON_CHARACTER_GENDER.ALL as string)
    ) {
      query.andWhere('aiWtCharacter.gender = :gender', {
        gender: dto.gender,
      });
    }

    const [items, total] = await query
      .orderBy('aiWtCharacter.id', 'DESC')
      .skip((dto.page - 1) * dto.limit)
      .take(dto.limit)
      .getManyAndCount();

    if (items.length) {
      const logs: AiWebtoonTrainingLogEntity[] =
        await this.aiWebtoonTrainingLogRepository

          .createQueryBuilder('aiWtTrainingLog')
          .where('aiWtTrainingLog.aiWebtoonCharacterId IN (:ids)', {
            ids: items.map((e) => e.id),
          })
          .andWhere(
            'aiWtTrainingLog.id IN (SELECT MAX(id) FROM ai_webtoon_training_log GROUP BY ai_webtoon_character_id)',
          )
          .getMany();

      items.forEach((e) => {
        const log = logs.find((log) => log.aiWebtoonCharacterId === e.id);
        e.countImage = e.imagesTraineds?.length || 0;

        e.loss = log ? log.loss : null;
        e.percent =
          log && e.status === EAiWebtoonCharacterStatus.TRAINING
            ? log.percent
            : null;
        e.isTesting = !!e.session?.length;
        e.imagesTraineds = [];
        delete e.session;
      });
    }

    const dataWaitingAndOrderNumber = await this.listWaitingAndOrderNumber(
      dto.type,
    );

    return {
      data: items,
      dataWaitingAndOrderNumber,
      page: dto.page,
      limit: dto.limit,
      totalPages: Math.ceil(total / dto.limit),
      total,
    };
  }

  async listAll() {
    const items = await this.aiWebtoonCharacterRepository
      .createQueryBuilder('aiWtCharacter')
      .innerJoin('aiWtCharacter.session', 'session')
      .where('aiWtCharacter.status != :status', {
        status: EAiWebtoonCharacterStatus.NEW,
      })
      .getMany();

    items.forEach((item) => {
      item.imagesTraineds = [];
      item.imagesPreparingTrainings = [];
    });

    return items;
  }

  async listAllByType(type: EAiWebtoonCharacterType) {
    const items = await this.aiWebtoonCharacterRepository
      .createQueryBuilder('aiWtCharacter')
      .innerJoin('aiWtCharacter.session', 'session')
      .where('aiWtCharacter.status != :status', {
        status: EAiWebtoonCharacterStatus.NEW,
      })
      .andWhere('aiWtCharacter.type = :type', { type })
      .getMany();

    items.forEach((item) => {
      item.imagesTraineds = [];
      item.imagesPreparingTrainings = [];
    });

    return items;
  }

  async listAllHaveBeenTrained(type: EAiWebtoonCharacterType) {
    const items = await this.aiWebtoonCharacterRepository

      .createQueryBuilder('aiWtCharacter')
      .innerJoin('aiWtCharacter.session', 'session')
      .where('aiWtCharacter.status != :status', {
        status: EAiWebtoonCharacterStatus.NEW,
      })
      .andWhere('aiWtCharacter.type = :type', { type })
      .andWhere('session.status = :sessionStatus', {
        sessionStatus: EAiWebtoonTrainingCharacterSessionStatus.DONE,
      })
      .getMany();

    items.forEach((item) => {
      item.countImage = item.imagesTraineds?.length || 0;
      item.countStoryUsed = item.storiesIdUsed?.length || 0;
      item.imagesTraineds = [];
    });

    return items;
  }

  async listTrainingSessionByCharacter(dto: ListTrainingSessionByCharacterDto) {
    const [items, total] =
      await this.aiWebtoonTrainingCharacterSessionRepository
        .createQueryBuilder('aiWtTrainingCharacterSession')
        .where(
          'aiWtTrainingCharacterSession.aiWebtoonCharacterId = :aiWebtoonCharacterId',
          {
            aiWebtoonCharacterId: dto.aiWebtoonCharacterId,
          },
        )
        .orderBy('aiWtTrainingCharacterSession.id', 'ASC')
        .skip((dto.page - 1) * dto.limit)
        .take(dto.limit)
        .getManyAndCount();

    if (items.length) {
      const logs = await this.aiWebtoonTrainingLogRepository
        .createQueryBuilder('aiWtTrainingLog')
        .where(
          'aiWtTrainingLog.aiWebtoonTrainingCharacterSessionId IN (:ids)',
          {
            ids: items.map((e) => e.id),
          },
        )
        .andWhere(
          'aiWtTrainingLog.id IN (SELECT MAX(id) FROM ai_webtoon_training_log GROUP BY ai_webtoon_training_character_session_id)',
        )
        .getMany();

      items.forEach((e) => {
        e.countImage = e.images.length;
        const log = logs.find(
          (log) => log.aiWebtoonTrainingCharacterSessionId === e.id,
        );
        e.loss = log ? log.loss : null;
        e.percent = log ? log.percent : null;
      });
    }

    return {
      data: items,
      page: dto.page,
      limit: dto.limit,
      totalPages: Math.ceil(total / dto.limit),
      total,
    };
  }

  async detail(id: number) {
    const character = await this.aiWebtoonCharacterRepository.findOne({
      where: { id },
    });
    if (!character)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND);
    character.countImage = character.imagesTraineds.length;
    character.countStoryUsed = character.storiesIdUsed.length;
    return character;
  }

  async generateDescriptionLabelByImages(
    body: GenerateDescriptionLabelByImagesDto,
  ) {
    return await this.aiService.sendGenerateDescriptionLabelByImagesRequest({
      image_links: body.images,
      prompt: body.prompt || '',
    });
  }

  async orderTraining(body: CharacterTrainingDto) {
    const character = await this.aiWebtoonCharacterRepository.findOne({
      where: { id: body.id },
    });
    if (!character)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND);

    if (
      character.status === EAiWebtoonCharacterStatus.TRAINING ||
      character.status === EAiWebtoonCharacterStatus.SYNCING
    )
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_CHARACTER_TRAINING_OR_SYNCING,
      );

    await this.aiWebtoonCharacterRepository.update(character.id, {
      status: EAiWebtoonCharacterStatus.WAITING,
      trainingOrderTime: new Date(),
      descriptionPromptPreparingTraining: body.descriptionPrompt,
      imagesPreparingTrainings: body.data.map((e) => ({
        url: e.url,
        prompt: e.prompt,
      })),
      apiGenImageSynceds: [],
    });

    return true;
  }

  async cancelOrderTraining(id: number) {
    const character = await this.aiWebtoonCharacterRepository.findOne({
      where: { id },
    });
    if (!character)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND);

    if (character.status !== EAiWebtoonCharacterStatus.WAITING)
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_WAITING);

    await this.aiWebtoonCharacterRepository.update(character.id, {
      status: character.imagesTraineds.length
        ? EAiWebtoonCharacterStatus.READY
        : EAiWebtoonCharacterStatus.NEW,
      trainingOrderTime: null,
      descriptionPromptPreparingTraining: null,
      imagesPreparingTrainings: [],
    });

    return true;
  }

  async receiveLogTraining(body: ReceiveLogCharacterTrainingDto) {
    this.logger.log('🚀 receiveLogTraining ~ body >> ', JSON.stringify(body));

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Receive Log Training - Character ${body.characterId}, Loss ${body.loss}, Percent ${body.percent}, URL ${body.url}`,
        data: body,
        isSuccess: true,
      },
    );

    const [character, sessionTraining] = await Promise.all([
      this.aiWebtoonCharacterRepository.findOne({
        where: { id: body.characterId },
      }),
      this.aiWebtoonTrainingCharacterSessionRepository.findOne({
        where: {
          aiWebtoonCharacterId: body.characterId,
          status: EAiWebtoonTrainingCharacterSessionStatus.IN_PROGRESS,
        },
      }),
    ]);

    if (!character)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND);
    if (!sessionTraining)
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_CHARACTER_TRAINING_SESSION_NOT_FOUND,
      );

    await this.aiWebtoonTrainingLogRepository.save({
      aiWebtoonCharacterId: character.id,
      aiWebtoonTrainingCharacterSessionId: sessionTraining.id,
      loss: body.loss,
      percent: body.percent,
    });

    if (body.url) {
      sessionTraining.url = body.url;
      await this.aiWebtoonTrainingCharacterSessionRepository.save(
        sessionTraining,
      );
    }

    return true;
  }

  async receiveLogModelSyncSample(body: ReceiveLogModelSyncSampleDto) {
    this.logger.log(
      '🚀 receiveLogModelSyncSample ~ body >> ',
      JSON.stringify(body),
    );

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Receive Log Model Sync Sample - Character ${body.characterId} - Epoch ${body.epoch}`,
        data: body,
        isSuccess: true,
      },
    );

    const [character, sessionTraining] = await Promise.all([
      this.aiWebtoonCharacterRepository.findOne({
        where: { id: body.characterId },
      }),
      this.aiWebtoonTrainingCharacterSessionRepository.findOne({
        where: {
          aiWebtoonCharacterId: body.characterId,
          status: In([
            EAiWebtoonTrainingCharacterSessionStatus.IN_PROGRESS,
            EAiWebtoonTrainingCharacterSessionStatus.DONE,
          ]),
        },
        order: { id: 'DESC' },
      }),
    ]);

    if (!character)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND);
    if (!sessionTraining)
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_CHARACTER_TRAINING_SESSION_NOT_FOUND,
      );

    await this.aiWebtoonTrainingLogModelRepository.save({
      aiWebtoonCharacterId: character.id,
      aiWebtoonTrainingCharacterSessionId: sessionTraining.id,
      epoch: body.epoch,
      samples: body.samples,
    });

    return true;
  }

  async receiveLogModelUrl(body: ReceiveLogModelSyncUrlModelDto) {
    this.logger.log(
      '🚀 receiveLogModelSyncUrlModelDto ~ body >> ',
      JSON.stringify(body),
    );

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Receive Log Model Sync Url - Character ${body.characterId}`,
        data: body,
        isSuccess: true,
      },
    );

    const [character, sessionTraining] = await Promise.all([
      this.aiWebtoonCharacterRepository.findOne({
        where: { id: body.characterId },
      }),
      this.aiWebtoonTrainingCharacterSessionRepository.findOne({
        where: {
          aiWebtoonCharacterId: body.characterId,
          status: In([
            EAiWebtoonTrainingCharacterSessionStatus.IN_PROGRESS,
            EAiWebtoonTrainingCharacterSessionStatus.DONE,
          ]),
        },
        order: { id: 'DESC' },
      }),
    ]);

    if (!character)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND);
    if (!sessionTraining)
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_CHARACTER_TRAINING_SESSION_NOT_FOUND,
      );

    await Promise.all(
      body.data.map((item) =>
        this.aiWebtoonTrainingLogModelRepository.update(
          {
            aiWebtoonCharacterId: character.id,
            aiWebtoonTrainingCharacterSessionId: sessionTraining.id,
            epoch: item.epoch,
          },
          {
            modelUrl: item.modelUrl,
          },
        ),
      ),
    );

    const highestEpoch = Math.max(...body.data.map((item) => item.epoch));
    const latestModel = body.data.find((item) => item.epoch === highestEpoch);
    if (latestModel) {
      await this.aiWebtoonCharacterRepository.update(character.id, {
        modelUrl: latestModel.modelUrl,
      });
    }

    return true;
  }

  async listLogModelTrainingByCharacter(id: number) {
    const character = await this.aiWebtoonCharacterRepository.findOne({
      where: { id },
    });
    if (!character) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND);
    }

    const listLogModel = await this.aiWebtoonTrainingLogModelRepository.find({
      where: {
        aiWebtoonCharacterId: id,
      },
      order: {
        id: 'DESC',
      },
    });

    listLogModel.forEach((logModel) => {
      logModel.isApplied = logModel.modelUrl === character.modelUrl;
    });

    return listLogModel;
  }

  async applyModelTraining(aiWebtoonTrainingLogModelId: number) {
    const logModel = await this.aiWebtoonTrainingLogModelRepository.findOne({
      where: {
        id: aiWebtoonTrainingLogModelId,
      },
      relations: ['aiWebtoonCharacter'],
    });
    if (!logModel)
      throw new NotFoundException(
        AI_MESSAGE_CONFIG.AI_CHARACTER_TRAINING_LOG_MODEL_NOT_FOUND,
      );

    if (!logModel.modelUrl)
      throw new NotFoundException(
        AI_MESSAGE_CONFIG.AI_CHARACTER_MODEL_URL_NOT_FOUND,
      );

    if (
      logModel.aiWebtoonCharacter.status !== EAiWebtoonCharacterStatus.READY
    ) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_READY);
    }

    if (
      ![EAiWebtoonCharacterType.FLUX, EAiWebtoonCharacterType.SDXL].includes(
        logModel.aiWebtoonCharacter.type,
      )
    ) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_CHARACTER_TYPE_NOT_ALLOWED,
      );
    }

    if (logModel.aiWebtoonCharacter.modelUrl === logModel.modelUrl) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_CHARACTER_MODEL_ALREADY_APPLIED,
      );
    }

    await this.aiService.sendCharacterTrainingSyncRequest({
      modelId: logModel.aiWebtoonCharacterId.toString(),
      modelUrl: logModel.modelUrl,
      typeModel: 'LORA',
      characterType: logModel.aiWebtoonCharacter.type,
    });

    await this.aiWebtoonCharacterRepository.update(
      logModel.aiWebtoonCharacterId,
      {
        modelUrl: logModel.modelUrl,
        apiGenImageSynceds: [],
        status: EAiWebtoonCharacterStatus.SYNCING,
      },
    );

    return true;
  }

  async receiveEndTraining(id: number) {
    this.logger.log('🚀 receiveLogTraining ~ id >> ', id);

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Receive End Training - Character ${id}`,
        data: { characterId: id },
        isSuccess: true,
      },
    );

    const [character, sessionTraining, logLastTraining] = await Promise.all([
      this.aiWebtoonCharacterRepository.findOne({
        where: { id },
      }),
      this.aiWebtoonTrainingCharacterSessionRepository.findOne({
        where: {
          aiWebtoonCharacterId: id,
          status: EAiWebtoonTrainingCharacterSessionStatus.IN_PROGRESS,
        },
      }),
      this.aiWebtoonTrainingLogRepository.findOne({
        where: {
          aiWebtoonCharacterId: id,
        },
        order: {
          id: 'DESC',
        },
      }),
    ]);

    if (!character)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND);
    if (!sessionTraining)
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_CHARACTER_TRAINING_SESSION_NOT_FOUND,
      );

    character.status = EAiWebtoonCharacterStatus.SYNCING;
    sessionTraining.endDate = new Date();
    sessionTraining.status = EAiWebtoonTrainingCharacterSessionStatus.DONE;

    await Promise.all([
      this.aiWebtoonCharacterRepository.save(character),
      this.aiWebtoonTrainingCharacterSessionRepository.save(sessionTraining),
    ]);

    if (logLastTraining) {
      await this.aiWebtoonTrainingLogRepository.save({
        aiWebtoonCharacterId: character.id,
        aiWebtoonTrainingCharacterSessionId: sessionTraining.id,
        loss: logLastTraining.loss,
        percent: 0,
      });
    }

    return true;
  }

  async receiveSynced(body: ReceiveSyncedCharacterDto) {
    this.logger.log('🚀 receiveSynced >> ', JSON.stringify(body));

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Receive Synced - Character ${body.characterId} - API: ${body.apiName}`,
        data: body,
        isSuccess: true,
      },
    );

    await this.aiApiGenStateManagementService.checkIsWorkingByName(
      body.apiName,
    );

    const result = await this.aiWebtoonCharacterRepository.manager.transaction(
      'SERIALIZABLE',
      async (entityManager: EntityManager) => {
        const [character, genImageApis] = await Promise.all([
          entityManager.findOne(AiWebtoonCharacterEntity, {
            where: { id: body.characterId },
          }),
          this.aiApiGenStateManagementService.findAllByType(
            EAiApiGenStateManagementType.GEN_IMAGE_FLUX,
          ),
        ]);

        if (!character)
          throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND);

        if (character.status !== EAiWebtoonCharacterStatus.SYNCING) {
          throw new BadRequestException(
            AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_SYNCING,
          );
        }

        const apiGenImageSynceds = character.apiGenImageSynceds;
        apiGenImageSynceds.push(body.apiName);

        const apiGenImageSyncedSet = new Set(apiGenImageSynceds);
        if (
          genImageApis.every((genImageApi) =>
            apiGenImageSyncedSet.has(genImageApi.name),
          )
        ) {
          character.status = EAiWebtoonCharacterStatus.READY;
        }

        character.apiGenImageSynceds = apiGenImageSynceds;
        await entityManager.save(AiWebtoonCharacterEntity, character);

        return true;
      },
    );

    return result;
  }

  private getTestingConstants() {
    return {
      rootUuid: {
        sdxl: 'AI-TESTING-IMAGES-SDXL-',
        flux: 'AI-TESTING-IMAGES-FLUX-',
      },
      keyIsTesting: {
        sdxl: 'IS-AI-TESTING-SDXL',
        flux: 'IS-AI-TESTING-FLUX',
      },
    };
  }

  async testing(body: CharacterTestingDto) {
    const { rootUuid, keyIsTesting } = this.getTestingConstants();

    const { uuid, apiUrl, data, typeCharacter } = await this.prepareTestingData(
      body,
      rootUuid,
    );

    if (
      ![EAiWebtoonCharacterType.SDXL, EAiWebtoonCharacterType.FLUX].includes(
        typeCharacter,
      )
    ) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_CHARACTER_INVALID_TYPE,
      );
    }

    await this.checkTestingInProgress(typeCharacter, keyIsTesting);
    await this.aiService.sendTestingCharacterRequest(apiUrl, data);
    await this.cacheTestingData(
      uuid,
      body.numberOfImages,
      typeCharacter,
      keyIsTesting,
    );

    return { uuid };
  }

  private async prepareTestingData(
    body: CharacterTestingDto,
    rootUuid: Record<string, string>,
  ) {
    let uuid: string;
    let apiUrl: string;
    let data: ISendTestingDataRequest;
    let typeCharacter: EAiWebtoonCharacterType;

    if (body.type === EAiWebtoonCharacterTestedType.ONE) {
      const character = await this.detail(body.id1);
      typeCharacter = character.type;
      ({ uuid, apiUrl, data } = this.prepareSingleCharacterTestingData(
        body.model,
        character,
        body.numberOfImages,
        body.prompt,
        rootUuid,
      ));
    } else {
      const [character1, character2] = await Promise.all([
        this.detail(body.id1),
        this.detail(body.id2),
      ]);
      typeCharacter = character1.type;
      ({ uuid, apiUrl, data } = this.prepareTwoCharactersTestingData(
        body.model,
        character1,
        character2,
        body.numberOfImages,
        body.prompt,
        body.bg,
        body.promptCharacter1,
        body.promptCharacter2,
        rootUuid,
      ));
    }

    return { uuid, apiUrl, data, typeCharacter };
  }

  private prepareSingleCharacterTestingData(
    model: EAiTestingModel,
    character: any,
    numberOfImages: number,
    prompt: string,
    rootUuid: Record<string, string>,
  ) {
    const apiUrl = this.getApiUrlForCharacterType(character.type);
    const uuid = this.generateUuid(character.type, rootUuid);

    const data: ISendTestingDataRequest = {
      uuid,
      numberOfImages,
      data: [
        {
          characters: [
            {
              character: character.name,
              gender: character.gender,
              characterId: character.id,
            },
          ],
          prompt: {
            generalCharacters: prompt,
          },
        },
      ],
      ...(model ? { model } : {}),
    };

    return { uuid, apiUrl, data };
  }

  private prepareTwoCharactersTestingData(
    model: EAiTestingModel,
    character1: any,
    character2: any,
    numberOfImages: number,
    prompt: string,
    bg: string,
    promptCharacter1: string,
    promptCharacter2: string,
    rootUuid: Record<string, string>,
  ) {
    const apiUrl = this.getApiUrlForCharacterType(character1.type);
    const uuid = this.generateUuid(character1.type, rootUuid);

    const data: ISendTestingDataRequest = {
      uuid,
      numberOfImages,
      data: [
        {
          characters: [
            {
              character: character1.name,
              gender: character1.gender,
              characterId: character1.id,
            },
            {
              character: character2.name,
              gender: character2.gender,
              characterId: character2.id,
            },
          ],
          prompt: {
            bg,
            generalCharacters: prompt,
          },
          characterDesc: [
            {
              character: character1.name,
              description: promptCharacter1,
            },
            {
              character: character2.name,
              description: promptCharacter2,
            },
          ],
        },
      ],
      ...(model ? { model } : {}),
    };

    return { uuid, apiUrl, data };
  }

  private getApiUrlForCharacterType(type: EAiWebtoonCharacterType): string {
    return type === EAiWebtoonCharacterType.SDXL
      ? `${process.env.AI_TESTING_SDXL_URL}/testing-2-characters-sdxl`
      : `${process.env.AI_TESTING_FLUX_URL}/generate_image_comic_flux`;
  }

  private generateUuid(
    type: EAiWebtoonCharacterType,
    rootUuid: Record<string, string>,
  ): string {
    return type === EAiWebtoonCharacterType.SDXL
      ? rootUuid.sdxl + uuidv4()
      : rootUuid.flux + uuidv4();
  }

  private async checkTestingInProgress(
    _typeCharacter: EAiWebtoonCharacterType,
    _keyIsTesting: Record<string, string>,
  ) {
    // const check = await this.cacheService.get(
    //   typeCharacter === EAiWebtoonCharacterType.SDXL
    //     ? keyIsTesting.sdxl
    //     : keyIsTesting.flux,
    // );
    // if (check) {
    //   return BADREQUEST(
    //     'ai-webtoon-character/testing-in-progress',
    //     'Testing is currently in progress for another character.',
    //   );
    // }
  }

  private async cacheTestingData(
    _uuid: string,
    _numberOfImages: number,
    _typeCharacter: EAiWebtoonCharacterType,
    _keyIsTesting: Record<string, string>,
  ) {
    // await Promise.all([
    //   this.cacheService.set(uuid, {
    //     urls: [],
    //     numberOfImages,
    //     type: typeCharacter,
    //   }),
    //   this.cacheService.set(
    //     typeCharacter === EAiWebtoonCharacterType.SDXL
    //       ? keyIsTesting.sdxl
    //       : keyIsTesting.flux,
    //     true,
    //   ),
    // ]);
  }

  async saveTesting(body: SaveCharacterTestingDto, adminId: number) {
    if (body.type === EAiWebtoonCharacterTestedType.ONE) {
      await this.detail(body.id1);

      await this.aiWebtoonCharacterTestedRepository.save({
        aiWebtoonCharacterId1: body.id1,
        administratorId: adminId,
        prompt: body.prompt,
        images: body.images,
        type: body.type,
        // model: body.model,
      });
    } else {
      await Promise.all([this.detail(body.id1), this.detail(body.id2)]);
      await this.aiWebtoonCharacterTestedRepository.save({
        aiWebtoonCharacterId1: body.id1,
        aiWebtoonCharacterId2: body.id2,
        administratorId: adminId,
        prompt: body.prompt,
        promptCharacter1: body.promptCharacter1,
        promptCharacter2: body.promptCharacter2,
        images: body.images,
        type: body.type,
        bg: body.bg,
        // model: body.model,
      });
    }

    return true;
  }

  async listCharacterTested(dto: ListAiWebtoonCharacterDto) {
    const [items, total] = await this.aiWebtoonCharacterTestedRepository
      .createQueryBuilder('aiWtCharacterTested')
      .select([
        'aiWtCharacterTested',
        'aiWebtoonCharacter1.id',
        'aiWebtoonCharacter1.name',
        'aiWebtoonCharacter1.avatar',
        'aiWebtoonCharacter1.gender',
        'aiWebtoonCharacter2.id',
        'aiWebtoonCharacter2.name',
        'aiWebtoonCharacter2.avatar',
        'aiWebtoonCharacter2.gender',
        'administrator.id',
        'administrator.username',
        'administrator.email',
      ])
      .leftJoin(
        'aiWtCharacterTested.aiWebtoonCharacter1',
        'aiWebtoonCharacter1',
      )
      .leftJoin(
        'aiWtCharacterTested.aiWebtoonCharacter2',
        'aiWebtoonCharacter2',
      )
      .leftJoin('aiWtCharacterTested.administrator', 'administrator')
      .where('aiWebtoonCharacter1.type = :type', { type: dto.type })
      .orderBy('aiWtCharacterTested.id', 'DESC')
      .skip((dto.page - 1) * dto.limit)
      .take(dto.limit)
      .getManyAndCount();

    return {
      data: items,
      page: dto.page,
      limit: dto.limit,
      totalPages: Math.ceil(total / dto.limit),
      total,
    };
  }

  async getSamplePromptsByCharacter(id: number, dto: GetSampleTestingDto) {
    const character = await this.detail(id);

    if (character.status !== EAiWebtoonCharacterStatus.READY) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_READY);
    }

    const data: IGenerateSamplePromptsRequest = {
      countResult: dto.countResult,
      prompts: character.imagesTraineds
        ?.filter((e) => e.prompt)
        .slice(0, 100)
        .map((e) => e.prompt) as string[],
    };

    return await this.aiService.sendGenerateSamplePromptsRequest(data);
  }

  async receiveTesting(body: ReceiveTestingDto) {
    this.logger.log('🚀 receiveTesting ~ body >> ', JSON.stringify(body));

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Receive Testing - UUID ${body.uuid}`,
        data: body,
        isSuccess: true,
      },
    );

    // const _keyIsTesting = {
    //   sdxl: 'IS-AI-TESTING-SDXL',
    //   flux: 'IS-AI-TESTING-FLUX',
    // };

    // const data = await this.cacheService.get(body.uuid);
    // if (!data) {
    //   return NOTFOUND('ai-webtoon-character/uuid-not-found', 'UUID not found');
    // }

    // data.urls = body.urls;
    // await this.cacheService.set(body.uuid, data);

    // if (data.urls.length === data.numberOfImages) {
    //   await this.cacheService.del(
    //     data.type === EAiWebtoonCharacterType.SDXL
    //       ? keyIsTesting.sdxl
    //       : keyIsTesting.flux,
    //   );
    // }

    return true;
  }

  async createSamplePrompt(id: number, body: CreateSamplePromptDto) {
    const character = await this.aiWebtoonCharacterRepository.findOne({
      where: { id },
    });
    if (!character) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND);
    }

    const currentSamplePrompts = character.samplePrompts || [];

    await this.aiWebtoonCharacterRepository.update(id, {
      samplePrompts: [
        ...currentSamplePrompts,
        {
          uuid: uuidv4(),
          prompt: body.prompt,
        },
      ],
    });

    return true;
  }

  async deleteSamplePrompt(id: number, body: DeleteSamplePromptDto) {
    const character = await this.aiWebtoonCharacterRepository.findOne({
      where: { id },
    });
    if (!character) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND);
    }

    const currentSamplePrompts = character.samplePrompts || [];
    const filteredSamplePrompts = currentSamplePrompts.filter(
      (prompt) => prompt.uuid !== body.uuid,
    );

    await this.aiWebtoonCharacterRepository.update(id, {
      samplePrompts: filteredSamplePrompts,
    });

    return true;
  }

  async imagesTestingByUuid(_uuid: string) {
    // const data = await this.cacheService.get(uuid);
    // if (!data) {
    //   return NOTFOUND('ai-webtoon-character/uuid-not-found', 'UUID not found');
    // }
    // return data.urls;
  }

  async addStoryIdUsedForMultipleCharacters(
    characterIds: number[],
    storyId: number,
  ) {
    const characters = await this.aiWebtoonCharacterRepository.findBy({
      id: In(characterIds),
    });

    const charactersToUpdate = characters.map((character) => {
      const storiesIdUsed = new Set(character.storiesIdUsed);
      storiesIdUsed.add(storyId);

      return {
        id: character.id,
        storiesIdUsed: [...storiesIdUsed],
      };
    });

    await this.aiWebtoonCharacterRepository.save(charactersToUpdate);
  }

  async removeStoryIdUsedForCharacter(characterId: number, storyId: number) {
    const character = await this.detail(characterId);
    const storiesIdUsed = character.storiesIdUsed;
    const newStoriesIdUsed = storiesIdUsed.filter((id) => id !== storyId);
    await this.aiWebtoonCharacterRepository.update(characterId, {
      storiesIdUsed: newStoriesIdUsed,
    });
  }

  async delete(id: number) {
    const character = await this.detail(id);

    if (
      character.status === EAiWebtoonCharacterStatus.TRAINING ||
      character.status === EAiWebtoonCharacterStatus.SYNCING
    ) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_CHARACTER_CAN_NOT_DELETE_CHARACTER_THAT_IS_TRAINING_OR_SYNCING,
      );
    }

    if (character.storiesIdUsed.length > 0) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CHARACTER_IN_USE);
    }

    await this.aiWebtoonCharacterRepository.softDelete(id);
    return true;
  }
}
