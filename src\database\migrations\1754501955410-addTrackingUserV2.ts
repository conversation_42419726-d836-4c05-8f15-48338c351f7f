import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTrackingUserV21754501955410 implements MigrationInterface {
  name = 'AddTrackingUserV21754501955410';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`user_analytics_v2\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`date\` varchar(10) NOT NULL, \`hour\` tinyint NOT NULL, \`activeUsers\` int NOT NULL DEFAULT '0', \`newRegistrations\` int NOT NULL DEFAULT '0', \`deletedAccounts\` int NOT NULL DEFAULT '0', \`loginCount\` int NOT NULL DEFAULT '0', \`uniqueLogins\` int NOT NULL DEFAULT '0', \`adultVerifiedCount\` int NOT NULL DEFAULT '0', \`firtPurchaseCount\` int NOT NULL DEFAULT '0', \`firstPurchaseAttemptCount\` int NOT NULL DEFAULT '0', \`firstPurchaseAmount\` int NOT NULL DEFAULT '0', \`rePurchaseCount\` int NOT NULL DEFAULT '0', \`rePurchaseAttemptCount\` int NOT NULL DEFAULT '0', \`rePurchaseAmount\` int NOT NULL DEFAULT '0', \`metadata\` json NULL, \`language\` varchar(50) NOT NULL DEFAULT 'en', INDEX \`IDX_358819bfc3b012b57d1d914927\` (\`date\`), INDEX \`IDX_bed9b2a9d6a4b46140e1d70cb7\` (\`hour\`), UNIQUE INDEX \`IDX_61b43dbec29f74d4d6a11ae222\` (\`date\`, \`hour\`, \`language\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_61b43dbec29f74d4d6a11ae222\` ON \`user_analytics_v2\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_bed9b2a9d6a4b46140e1d70cb7\` ON \`user_analytics_v2\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_358819bfc3b012b57d1d914927\` ON \`user_analytics_v2\``,
    );
    await queryRunner.query(`DROP TABLE \`user_analytics_v2\``);
  }
}
