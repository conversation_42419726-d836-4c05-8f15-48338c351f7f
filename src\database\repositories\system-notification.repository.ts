import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  SystemNotificationEntity,
  NotificationStatus,
} from '../entities/system-notification.entity';

@Injectable()
export class SystemNotificationRepository extends Repository<SystemNotificationEntity> {
  constructor(
    @InjectRepository(SystemNotificationEntity)
    private repository: Repository<SystemNotificationEntity>,
  ) {
    super(repository.target, repository.manager, repository.queryRunner);
  }

  async findActiveNotifications(page: number = 1, limit: number = 10) {
    return await this.findAndCount({
      where: { status: NotificationStatus.ACTIVE },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });
  }

  async findWithViewCount(
    page: number = 1,
    limit: number = 10,
    status?: NotificationStatus,
  ) {
    const queryBuilder = this.createQueryBuilder('sn')
      .leftJoin(
        'notification_view',
        'nv',
        'nv.notificationId = sn.id AND nv.notificationType = :type',
        { type: 'system' },
      )
      .select([
        'sn.id',
        'sn.name',
        'sn.title',
        'sn.status',
        'sn.content',
        'sn.createdAt',
        'sn.updatedAt',
      ])
      .addSelect('COUNT(nv.id)', 'viewCount')
      .where('sn.deletedAt IS NULL')
      .groupBy('sn.id')
      .orderBy('sn.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    // Add status filter if provided
    if (status) {
      queryBuilder.andWhere('sn.status = :status', { status });
    }

    // Build count query with same filters
    const countQueryBuilder = this.createQueryBuilder('sn').where(
      'sn.deletedAt IS NULL',
    );

    if (status) {
      countQueryBuilder.andWhere('sn.status = :status', { status });
    }

    const [data, total] = await Promise.all([
      queryBuilder.getRawAndEntities(),
      countQueryBuilder.getCount(),
    ]);

    const results = data.entities.map((notification, index) => ({
      ...notification,
      viewCount: parseInt(data.raw[index].viewCount) || 0,
    }));

    return [results, total];
  }
}
