import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  NotEquals,
  ValidateNested,
} from 'class-validator';
import {
  EAiWebtoonCharacterGender,
  EAiWebtoonCharacterType,
} from 'src/common/ai-webtoon.enum';

export class AiChooseCharacterItemDto {
  @IsString()
  @IsOptional()
  uuid: string;

  @IsNotEmpty()
  @IsString()
  character: string;

  @IsEnum(EAiWebtoonCharacterGender)
  gender: EAiWebtoonCharacterGender;

  @IsNotEmpty()
  @IsInt()
  @Type(() => Number)
  characterId: number;

  @IsEnum(EAiWebtoonCharacterType)
  @NotEquals(EAiWebtoonCharacterType.DREAM_BOOTH)
  type: EAiWebtoonCharacterType;
}

export class ChooseCharacterDto {
  @ApiProperty({
    example: [
      {
        uuid: 'uuid',
        characterId: 1,
        character: '<PERSON>',
        gender: EAiWebtoonCharacterGender.MALE,
        type: EAiWebtoonCharacterType.FLUX,
      },
    ],
  })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => AiChooseCharacterItemDto)
  data: AiChooseCharacterItemDto[];
}
