import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from 'typeorm';
import { AdminEntity } from './admin.entity';
import { AiWebtoonCharacterEntity } from './ai-webtoon-character.entity';
import { AiWebtoonPrepareCharacterEntity } from './ai-webtoon-prepare-character.entity';
import { DefaultEntity } from './default.entity';
import { decimalTransformer } from 'src/common/utilities/transfomer.utility';

@Entity('ai_webtoon_prepare_character_generated')
export class AiWebtoonPrepareCharacterGeneratedEntity extends DefaultEntity {
  @Column({ name: 'ai_webtoon_prepare_character_id' })
  aiWebtoonPrepareCharacterId: number;

  @Column({ name: 'ai_webtoon_character_id', type: 'int', nullable: true })
  aiWebtoonCharacterId?: number | null;

  @Column({ name: 'admin_id' })
  adminId: number;

  @Column({ name: 'prompt', nullable: true, type: 'text' })
  prompt?: string | null;

  @Column({
    name: 'collection_images',
    type: 'json',
    default: () => '(JSON_ARRAY())',
  })
  collectionImages: string[];

  @Column({ name: 'cut_images', type: 'json', default: () => '(JSON_ARRAY())' })
  cutImages: string[];

  @Column({ name: 'number_of_images', default: 0 })
  numberOfImages: number;

  @Column({
    name: 'lora_strength',
    nullable: true,
    type: 'decimal',
    precision: 5,
    scale: 3,
    default: 1,
    transformer: decimalTransformer,
  })
  loraStrength?: number;

  @ManyToOne(() => AiWebtoonCharacterEntity)
  @JoinColumn({ name: 'ai_webtoon_character_id' })
  aiWebtoonCharacter: AiWebtoonCharacterEntity;

  @ManyToOne(() => AiWebtoonPrepareCharacterEntity)
  @JoinColumn({ name: 'ai_webtoon_prepare_character_id' })
  aiWebtoonPrepareCharacter: AiWebtoonPrepareCharacterEntity;

  @ManyToOne(() => AdminEntity)
  @JoinColumn({ name: 'admin_id' })
  admin: AdminEntity;
}
