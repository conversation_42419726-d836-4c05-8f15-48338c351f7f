import {
  EAiBaseModelGenImageTextImportByType,
  EAiBaseModelGenImageType,
  EAiCutChapterType,
  EAiDirectionApi,
  EAiFrameImageSizeTextImportByType,
  EAiFrameImageSizeType,
  EAiWebtoonChapterStatusGenerateImage,
  EAiWebtoonCharacterType,
  EAiWebtoonCutChapterStatusGenerateImage,
} from 'src/common/ai-webtoon.enum';

import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { AI_MESSAGE_CONFIG, MESSAGE_CONFIG } from 'src/common/message.config';

import { AiWebtoonCutChapterEntity } from 'src/database/entities/ai-webtoon-cut-chapter.entity';
import { AiWebtoonCutChapterService } from '../ai-webtoon-cut-chapter/ai-webtoon-cut-chapter.service';
import { AiWebtoonStoryService } from '../ai-webtoon-story/ai-webtoon-story.service';
// import { FileService } from '../file/file.service';
// import { SocketService } from '../socket/socket.service';
import * as moment from 'moment-timezone';
import { IAiGeneralCharacter } from 'src/common/interfaces/ai-webtoon/ai-webtoon-character.interface';
import { isValidTimeZone } from 'src/common/utilities/check.utility';
import { AiWebtoonOrderGenerateCutChapterEntity } from 'src/database/entities/ai-webtoon-order-generate-cut-chapter.entity';
import { AiWebtoonChapterRepository } from 'src/database/repositories/ai-webtoon-chapter.repository';
import { AiWebtoonCutChapterRepository } from 'src/database/repositories/ai-webtoon-cut-chapter.repository';
import { AiWebtoonOrderGenerateCutChapterRepository } from 'src/database/repositories/ai-webtoon-order-generate-cut-chapter.repository';
import { AiWebtoonSceneChapterRepository } from 'src/database/repositories/ai-webtoon-scene-chapter.repository';
import { NotificationGoogleService } from 'src/modules/notification-google/notification-google.service';
import { AiService } from '../ai-service/ai-service.service';
import {
  ListAiWebtoonChapterDto,
  ListAiWebtoonChapterSearchBy,
  ListAiWebtoonChapterSearchByTime,
} from './dto/list-ai-webtoon-chapter.dto';
import { MergePreviewImagesDto } from './dto/merge-preview-images.dto';
import { ReceiveMergePreviewImagesDto } from './dto/receive-merge-preview-images.dto';
import { UpdateBgColorDto } from './dto/update-bg-color.dto';
import { UpdateWebtoonChapterDto } from './dto/update-webtoon-chapter.dto';
import { UploadFileChapterByGoogleDriveDto } from './dto/upload-file-chapter-by-google-drive.dto';

@Injectable()
export class AiWebtoonChapterService {
  constructor(
    private readonly aiWebtoonChapterRepository: AiWebtoonChapterRepository,
    private readonly aiWebtoonStoryService: AiWebtoonStoryService,
    private readonly aiWebtoonOrderGenerateCutChapterRepository: AiWebtoonOrderGenerateCutChapterRepository,
    private readonly aiWebtoonSceneChapterRepository: AiWebtoonSceneChapterRepository,
    private readonly aiWebtoonCutChapterRepository: AiWebtoonCutChapterRepository,
    private readonly aiWebtoonCutChapterService: AiWebtoonCutChapterService,
    // private readonly httpService: HttpService,
    private readonly notificationGoogleService: NotificationGoogleService,
    private readonly aiService: AiService,
    // private readonly socketService: SocketService,
    // private readonly fileService: FileService,
  ) {}

  private readonly logger = new Logger(AiWebtoonChapterService.name);

  async list(dto: ListAiWebtoonChapterDto, timezone: string) {
    if (!isValidTimeZone(timezone)) {
      throw new BadRequestException(MESSAGE_CONFIG.TIMEZONE_INVALID);
    }

    const story = await this.aiWebtoonStoryService.detail(dto.aiWebtoonStoryId);
    const generalCharacters = story.characters;

    const query = this.aiWebtoonChapterRepository
      .createQueryBuilder('aiWebtoonChapter')
      .select([
        'aiWebtoonChapter',
        'admin.id',
        'admin.username',
        'scenes',
        'cuts',
      ])
      .leftJoin('aiWebtoonChapter.admin', 'admin')
      .leftJoin('aiWebtoonChapter.scenes', 'scenes')
      .leftJoin('scenes.cuts', 'cuts')
      .where('aiWebtoonChapter.aiWebtoonStoryId = :aiWebtoonStoryId', {
        aiWebtoonStoryId: dto.aiWebtoonStoryId,
      });

    if (dto.search && dto.searchBy) {
      if (dto.searchBy === ListAiWebtoonChapterSearchBy.TITLE) {
        query.andWhere('aiWebtoonChapter.title LIKE :title', {
          title: `%${dto.search}%`,
        });
      } else if (dto.searchBy === ListAiWebtoonChapterSearchBy.ADMIN_NAME) {
        query.andWhere('admin.username LIKE :username', {
          username: `%${dto.search}%`,
        });
      }
    }

    if (dto.searchByTime && dto.fromTime) {
      const fromTime = moment(dto.fromTime)
        .tz(timezone)
        .startOf('day')
        .utc()
        .format('YYYY-MM-DD HH:mm:ss');

      query.andWhere(
        `${
          dto.searchByTime === ListAiWebtoonChapterSearchByTime.CREATED_AT
            ? 'aiWebtoonChapter.createdAt'
            : 'aiWebtoonChapter.updatedAt'
        } >= :fromTime`,
        {
          fromTime,
        },
      );
    }

    if (dto.searchByTime && dto.toTime) {
      const toTime = moment(dto.toTime)
        .tz(timezone)
        .endOf('day')
        .utc()
        .format('YYYY-MM-DD HH:mm:ss');

      query.andWhere(
        `${
          dto.searchByTime === ListAiWebtoonChapterSearchByTime.CREATED_AT
            ? 'aiWebtoonChapter.createdAt'
            : 'aiWebtoonChapter.updatedAt'
        } <= :toTime`,
        {
          toTime,
        },
      );
    }

    const items = await query
      .orderBy(
        `aiWebtoonChapter.${dto.sortBy || 'chapter'}`,
        dto.direction || 'ASC',
      )
      .getMany();

    items.forEach((item) => {
      const cuts = item.scenes?.flatMap((scene) => scene.cuts) || [];

      const uniqueCharactersCuts =
        this.aiWebtoonStoryService.getUniqueCharactersByCuts(cuts);

      item.characters = generalCharacters.filter((gChar) =>
        uniqueCharactersCuts.some((uuid) => gChar.uuid === uuid),
      );

      item.totalCuts = cuts.length;
      item.cutsGenerated = cuts.filter(
        (element) =>
          element.statusGenerateImage ===
          EAiWebtoonCutChapterStatusGenerateImage.GENERATED,
      ).length;
      item.cutsGenerating = cuts.filter(
        (element) =>
          element.statusGenerateImage ===
            EAiWebtoonCutChapterStatusGenerateImage.GENERATING ||
          element.statusGenerateImage ===
            EAiWebtoonCutChapterStatusGenerateImage.WAITING,
      ).length;
      item.notGenerated = cuts.filter(
        (element) =>
          element.statusGenerateImage ===
          EAiWebtoonCutChapterStatusGenerateImage.NOT_GENERATED,
      ).length;

      delete item.scenes;
    });

    return items;
  }

  async listOrderGenerates(): Promise<{
    listOrderByNormal: AiWebtoonOrderGenerateCutChapterEntity[];
    listOrderByInpaint: AiWebtoonOrderGenerateCutChapterEntity[];
  }> {
    const listOrder =
      await this.aiWebtoonOrderGenerateCutChapterRepository.find({
        order: {
          createdAt: 'ASC',
        },
      });

    return {
      listOrderByNormal: listOrder.filter(
        (order) => order.cutType === EAiCutChapterType.NORMAL,
      ),
      listOrderByInpaint: listOrder.filter(
        (order) => order.cutType === EAiCutChapterType.INPAINT,
      ),
    };
  }

  async detail(id: number) {
    const chapter = await this.aiWebtoonChapterRepository.findOne({
      where: { id },
      relations: ['aiWebtoonStory', 'scenes', 'scenes.cuts'],
    });

    if (!chapter)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHAPTER_NOT_FOUND);

    try {
      const { listOrderByNormal, listOrderByInpaint } =
        await this.listOrderGenerates();

      const generalCharacters = chapter.aiWebtoonStory.characters;

      const cuts = chapter.scenes?.flatMap((scene) => scene.cuts) || [];
      const uniqueCharactersCuts =
        this.aiWebtoonStoryService.getUniqueCharactersByCuts(cuts);

      chapter.characters = generalCharacters.filter((gChar) =>
        uniqueCharactersCuts.some((uuid) => gChar.uuid === uuid),
      );
      chapter.aiWebtoonStory.characters = generalCharacters as any;
      chapter.scenes = chapter.scenes?.sort((a, b) => a.order - b.order);
      chapter.scenes?.forEach((scene) => {
        scene.cuts.sort((a, b) => a.order - b.order);
        scene.cuts.forEach((cut) => {
          // TODO:  sau thêm parse vào entity
          cut.controlNetStrengthCanny = Number(cut.controlNetStrengthCanny);
          cut.controlNetStrengthDepth = Number(cut.controlNetStrengthDepth);
          cut.cfgScale = Number(cut.cfgScale);

          if (
            cut.statusGenerateImage ===
            EAiWebtoonCutChapterStatusGenerateImage.WAITING
          ) {
            const listOrder =
              cut.type === EAiCutChapterType.NORMAL
                ? listOrderByNormal
                : listOrderByInpaint;
            const index = listOrder.findIndex(
              (order) =>
                cut.aiWebtoonChapterId === order.chapterId &&
                order.cutsId.includes(cut.id),
            );
            cut.orderWaitingNumber = index > -1 ? index + 1 : null;
            cut.orderWaitingId = index > -1 ? listOrder[index].id : null;
          }
        });
      });
      return chapter;
    } catch {
      throw new BadRequestException(MESSAGE_CONFIG.INVALID_DATA);
    }
  }

  async update(id: number, body: UpdateWebtoonChapterDto) {
    const chapter = await this.aiWebtoonChapterRepository.findOne({
      where: { id },
    });
    if (!chapter)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHAPTER_NOT_FOUND);

    chapter.title = body.title;
    await this.aiWebtoonChapterRepository.save(chapter);

    // this.socketService.emitUpdateChapter(id);
    return true;
  }

  async delete(id: number) {
    const item = await this.detail(id);

    if (
      item.statusGenerateImage ===
      EAiWebtoonChapterStatusGenerateImage.GENERATING
    ) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_CHAPTER_CANNOT_DELETE_WHILE_GENERATING,
      );
    }

    await this.aiWebtoonChapterRepository.softDelete(id);
    // this.socketService.emitUpdateChapter(id);
    return true;
  }

  async updateBgColor(id: number, body: UpdateBgColorDto) {
    const chapter = await this.aiWebtoonChapterRepository.findOne({
      where: { id },
    });
    if (!chapter)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHAPTER_NOT_FOUND);

    chapter.bgColor = body.bgColor;
    await this.aiWebtoonChapterRepository.save(chapter);

    // this.socketService.emitUpdateChapter(id);
    return true;
  }

  private validateNumber(value: any): {
    isValid: boolean;
    value?: number;
    error?: string;
  } {
    if (!value) {
      return { isValid: false, error: 'Value is required', value: undefined };
    }

    const num = Number(value);
    if (isNaN(num)) {
      return {
        isValid: false,
        error: 'Value must be a number',
        value: undefined,
      };
    }

    if (num <= 0) {
      return {
        isValid: false,
        error: 'Value must be greater than 0',
        value: undefined,
      };
    }

    return { isValid: true, value: num };
  }

  private validateWidth(value: any): {
    isValid: boolean;
    value?: number;
    error?: string;
  } {
    const numValidation = this.validateNumber(value);
    if (!numValidation.isValid || numValidation.value === undefined) {
      return numValidation;
    }

    const width = numValidation.value;

    if (width > 688) {
      return { isValid: false, error: 'Width cannot be greater than 688' };
    }

    if (width < 200) {
      return { isValid: false, error: 'Width cannot be less than 200' };
    }

    if (width % 8 !== 0) {
      return { isValid: false, error: 'Width must be divisible by 8' };
    }

    return { isValid: true, value: width };
  }

  private validateHeight(value: any): {
    isValid: boolean;
    value?: number;
    error?: string;
  } {
    const numValidation = this.validateNumber(value);
    if (!numValidation.isValid || numValidation.value === undefined) {
      return numValidation;
    }

    const height = numValidation.value;

    if (height < 200) {
      return { isValid: false, error: 'Height cannot be less than 200' };
    }

    if (height % 8 !== 0) {
      return { isValid: false, error: 'Height must be divisible by 8' };
    }

    return { isValid: true, value: height };
  }

  private validateImageSizeType(value: any): {
    isValid: boolean;
    value?: EAiFrameImageSizeType;
    error?: string;
  } {
    if (!value) {
      return { isValid: false, error: 'Image Size Type is required' };
    }

    const validTypes = Object.keys(EAiFrameImageSizeTextImportByType);
    if (!validTypes.includes(value)) {
      return {
        isValid: false,
        error: `Image Size Type must be one of: ${validTypes.join(', ')}`,
      };
    }

    return {
      isValid: true,
      value: EAiFrameImageSizeTextImportByType[value] as EAiFrameImageSizeType,
    };
  }

  private validateModelType(value: any): {
    isValid: boolean;
    value?: EAiBaseModelGenImageType;
    error?: string;
  } {
    if (!value) {
      return { isValid: false, error: 'Model is required' };
    }

    const validTypes = Object.keys(EAiBaseModelGenImageTextImportByType);
    if (!validTypes.includes(value)) {
      return {
        isValid: false,
        error: `Model must be one of: ${validTypes.join(', ')}`,
      };
    }

    return {
      isValid: true,
      value: EAiBaseModelGenImageTextImportByType[
        value
      ] as EAiBaseModelGenImageType,
    };
  }

  private validateCharacters(
    value: any,
    generalCharacters: IAiGeneralCharacter[],
    modelType: EAiBaseModelGenImageType,
  ): {
    isValid: boolean;
    value?: IAiGeneralCharacter[];
    error?: string;
  } {
    if (
      !value ||
      !value.trim() ||
      modelType === EAiBaseModelGenImageType.DREAM_BOOTH
    ) {
      return { isValid: true, value: [] };
    }

    const listCharacter: string[] = value
      .split(',')
      .map((e: string) => e.trim());
    const isSDXL = modelType === EAiBaseModelGenImageType.SDXL_4_OPT;
    const expectedType = isSDXL
      ? EAiWebtoonCharacterType.SDXL
      : EAiWebtoonCharacterType.FLUX;

    const notFoundCharacters: string[] = listCharacter.filter(
      (e: string) => !generalCharacters.find((gChar) => gChar.character === e),
    );

    if (notFoundCharacters.length) {
      return {
        isValid: false,
        error: `Characters ${notFoundCharacters.join(
          ', ',
        )} not found in general characters`,
      };
    }

    const invalidTypeCharacters: string[] = listCharacter.filter(
      (e: string) => {
        const character = generalCharacters.find(
          (gChar) => gChar.character === e,
        );
        return (
          (isSDXL && character?.type !== EAiWebtoonCharacterType.SDXL) ||
          (!isSDXL && character?.type !== EAiWebtoonCharacterType.FLUX)
        );
      },
    );

    if (invalidTypeCharacters.length > 0) {
      return {
        isValid: false,
        error: `Characters ${invalidTypeCharacters.join(
          ', ',
        )} must be ${expectedType} type for ${modelType}`,
      };
    }

    const validCharacters: IAiGeneralCharacter[] = listCharacter
      .map((characterName: string) =>
        generalCharacters.find((gChar) => gChar.character === characterName),
      )
      .filter(Boolean) as IAiGeneralCharacter[];

    return { isValid: true, value: validCharacters };
  }

  async uploadFileChapterByGoogleDrive(
    _id: number,
    _body: UploadFileChapterByGoogleDriveDto,
  ) {
    // const chapter = await this.aiWebtoonChapterRepository.findOne(id, {
    //   relations: ['aiWebtoonStory'],
    // });
    // if (!chapter) {
    //   return NOTFOUND('ai-webtoon-chapter/not-found', 'Chapter not found');
    // }
    // const generalCharacters= chapter.aiWebtoonStory
    //   .characters
    //   ? JSON.parse(chapter.aiWebtoonStory.characters)
    //   : [];
    // let fileBuffer: Buffer;
    // let fileName: string;
    // let filePath: string;
    // let mimeType: string;
    // let fileUrl: string;
    // if (body.from === UploadFileFrom.S3) {
    //   const urlParts = body.file.split('/');
    //   fileName = urlParts[urlParts.length - 1];
    //   fileUrl = body.file;
    //   const response = await firstValueFrom(
    //     this.httpService.get(body.file, { responseType: 'arraybuffer' }),
    //   );
    //   if (!response || !response.data) {
    //     return BADREQUEST(
    //       'ai-webtoon-chapter/download-file-failed',
    //       'Failed to download file from S3',
    //     );
    //   }
    //   fileBuffer = Buffer.from(response.data);
    //   mimeType =
    //     response.headers['content-type'] ||
    //     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    // } else {
    //   const downloadResult = await this.fileService.downloadFileFromGoogleDrive(
    //     body.file,
    //     EDowloadFileGoogoleDriveType.XLSX,
    //   );
    //   fileName = downloadResult.fileName;
    //   filePath = downloadResult.filePath;
    //   mimeType = downloadResult.mimeType;
    //   fileBuffer = fs.readFileSync(filePath);
    // }
    // const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
    // const sheetName = workbook.SheetNames[0];
    // const worksheet = workbook.Sheets[sheetName];
    // const rows = XLSX.utils.sheet_to_json(worksheet);
    // const validRows: CutRowImportFile[] = [];
    // const errors = [];
    // for (let i = 0; i < rows.length; i++) {
    //   const row = rows[i];
    //   const rowIndex = i + 2; // Excel rows start at 1, and we have a header row
    //   const rowErrors = [];
    //   const requiredFields = [
    //     'Scene Name',
    //     'Cut',
    //     'General Description',
    //     'Number Of Images',
    //     'Image Size Type',
    //     'Width',
    //     'Height',
    //     'Model',
    //   ];
    //   for (const field of requiredFields) {
    //     if (!row[field]) {
    //       rowErrors.push(`Row ${rowIndex}: Missing required field "${field}"`);
    //     }
    //   }
    //   if (rowErrors.length > 0) {
    //     errors.push(...rowErrors);
    //     BADREQUEST(
    //       'ai-webtoon-chapter/invalid-file',
    //       `Invalid file: ${errors.join('\n')}`,
    //     );
    //   }
    //   const cutValidation = this.validateNumber(row['Cut']);
    //   if (!cutValidation.isValid) {
    //     rowErrors.push(`Row ${rowIndex}: Cut - ${cutValidation.error}`);
    //   }
    //   const numImagesValidation = this.validateNumber(row['Number Of Images']);
    //   if (!numImagesValidation.isValid) {
    //     rowErrors.push(
    //       `Row ${rowIndex}: Number Of Images - ${numImagesValidation.error}`,
    //     );
    //   }
    //   const imageSizeTypeValidation = this.validateImageSizeType(
    //     row['Image Size Type'],
    //   );
    //   if (!imageSizeTypeValidation.isValid) {
    //     rowErrors.push(
    //       `Row ${rowIndex}: Image Size Type - ${imageSizeTypeValidation.error}`,
    //     );
    //   }
    //   const modelValidation = this.validateModelType(row['Model']);
    //   if (!modelValidation.isValid) {
    //     rowErrors.push(`Row ${rowIndex}: Model - ${modelValidation.error}`);
    //   }
    //   const widthValidation = this.validateWidth(row['Width']);
    //   if (!widthValidation.isValid) {
    //     rowErrors.push(`Row ${rowIndex}: Width - ${widthValidation.error}`);
    //   }
    //   const heightValidation = this.validateHeight(row['Height']);
    //   if (!heightValidation.isValid) {
    //     rowErrors.push(`Row ${rowIndex}: Height - ${heightValidation.error}`);
    //   }
    //   const characterValidation = this.validateCharacters(
    //     row['Character'],
    //     generalCharacters,
    //     modelValidation.value,
    //   );
    //   if (!characterValidation.isValid) {
    //     rowErrors.push(
    //       `Row ${rowIndex}: Character - ${characterValidation.error}`,
    //     );
    //   }
    //   if (rowErrors.length > 0) {
    //     errors.push(...rowErrors);
    //     continue;
    //   }
    //   validRows.push({
    //     sceneName: row['Scene Name'],
    //     cut: cutValidation.value,
    //     generalDescription: row['General Description'],
    //     fluxPrompt: row['Flux Prompt'] || '',
    //     animagineXlPrompt: row['Animagine XL 4.0 Opt Prompt'] || '',
    //     negativeDescription: row['Negative Description'] || '',
    //     negativePrompt: row['Negative Prompt'] || '',
    //     characters: characterValidation.value.map((e) => e.uuid),
    //     numberOfImages: numImagesValidation.value,
    //     imageSizeType: imageSizeTypeValidation.value,
    //     width: widthValidation.value,
    //     height: heightValidation.value,
    //     model: modelValidation.value,
    //   } as CutRowImportFile);
    // }
    // if (errors.length > 0) {
    //   return BADREQUEST(
    //     'ai-webtoon-chapter/validation-errors',
    //     errors.join('\n'),
    //   );
    // }
    // if (body.from === UploadFileFrom.S3) {
    //   chapter.file = fileUrl;
    //   chapter.fileName = fileName;
    // } else {
    //   const { listPath } = await this.fileService.uploadMultipleFile(
    //     [
    //       {
    //         buffer: fileBuffer,
    //         mimetype: mimeType,
    //         originalname: fileName,
    //         size: fileBuffer.length,
    //         fieldname: 'file',
    //         encoding: '7bit',
    //         stream: fs.createReadStream(filePath),
    //         destination: '',
    //         path: filePath,
    //         filename: fileName,
    //       },
    //     ],
    //     FOLDERS_S3.AI_FILE,
    //     true,
    //   );
    //   const fileUrl = process.env.PREFIX_S3_CLOUNDFRONT + listPath[0];
    //   chapter.file = fileUrl;
    //   chapter.fileName = fileName;
    //   fs.unlinkSync(filePath);
    // }
    // const sceneGroups: Record<string, CutRowImportFile[]> = {};
    // for (const row of validRows) {
    //   if (!sceneGroups[row.sceneName]) {
    //     sceneGroups[row.sceneName] = [];
    //   }
    //   sceneGroups[row.sceneName].push(row);
    // }
    // const p = [];
    // for (const [sceneName, cuts] of Object.entries(sceneGroups)) {
    //   let scene = await this.aiWebtoonSceneChapterRepository.findOne({
    //     where: {
    //       aiWebtoonChapterId: chapter.id,
    //       title: sceneName,
    //     },
    //   });
    //   if (!scene) {
    //     const countScene = await this.aiWebtoonSceneChapterRepository.count({
    //       aiWebtoonChapterId: chapter.id,
    //     });
    //     scene = await this.aiWebtoonSceneChapterRepository.save({
    //       aiWebtoonChapterId: chapter.id,
    //       title: sceneName,
    //       images: JSON.stringify([]),
    //       order: countScene + 1,
    //     });
    //   }
    //   const countCutByScene = await this.aiWebtoonCutChapterRepository.count({
    //     aiWebtoonSceneChapterId: scene.id,
    //   });
    //   cuts.forEach((cutData, cutIndex) => {
    //     p.push({
    //       aiWebtoonSceneChapterId: scene.id,
    //       aiWebtoonChapterId: chapter.id,
    //       type: EAiCutChapterType.NORMAL,
    //       description: cutData.generalDescription,
    //       negativeDescription: cutData.negativeDescription,
    //       generalPrompt: cutData.fluxPrompt,
    //       negativePrompt: cutData.negativePrompt,
    //       danbooruPrompt: cutData.animagineXlPrompt,
    //       isGeneratingPrompt: false,
    //       isGeneratingNegativePrompt: false,
    //       isGeneratingDanbooruPrompt: false,
    //       characters: JSON.stringify(cutData.characters),
    //       order: countCutByScene + cutIndex + 1,
    //       numberOfImages: cutData.numberOfImages,
    //       imageSizeType: cutData.imageSizeType,
    //       imageWidth: cutData.width,
    //       imageHeight: cutData.height,
    //       baseModel: cutData.model,
    //       controlNetStrengthCanny: 0.6,
    //       controlNetStrengthDepth: 0.6,
    //       cfgScale: 5,
    //       steps: 28,
    //     } as AiWebtoonCutChapterEntity);
    //   });
    // }
    // const [cutsSave] = await Promise.all([
    //   this.aiWebtoonCutChapterRepository.save(p),
    //   this.aiWebtoonChapterRepository.save(chapter),
    // ]);
    // this.socketService.emitUpdateChapter(id);
    // await this.processCutsForImageGeneration(chapter.id, cutsSave);
    // return {
    //   success: true,
    //   message: `Successfully processed ${validRows.length} rows from Excel file`,
    //   scenesCreated: Object.keys(sceneGroups).length,
    //   cutsCreated: validRows.length,
    // };
  }

  async processCutsForImageGeneration(
    chapterId: number,
    cuts: AiWebtoonCutChapterEntity[],
  ) {
    this.logger.log(
      `Processing cuts for image generation for chapter ${chapterId}`,
    );

    const readyCuts: AiWebtoonCutChapterEntity[] = [];
    const needsPromptCuts: AiWebtoonCutChapterEntity[] = [];

    for (const cut of cuts) {
      const isReadyForGeneration = this.isCutReadyForImageGeneration(cut);

      if (isReadyForGeneration) {
        readyCuts.push(cut);
      } else {
        needsPromptCuts.push(cut);
      }
    }

    this.logger.log(
      `Found ${readyCuts.length} cuts ready for image generation`,
    );
    this.logger.log(
      `Found ${needsPromptCuts.length} cuts that need prompt generation`,
    );

    if (readyCuts.length > 0) {
      await this.generateImagesForCuts(chapterId, readyCuts);
    }

    if (needsPromptCuts.length > 0) {
      await this.aiWebtoonCutChapterService.generatePromptByDescriptionByCutsImportFile(
        chapterId,
        needsPromptCuts,
      );
    }

    // this.socketService.emitUpdateChapter(chapterId);

    return {
      success: true,
      readyCutsCount: readyCuts.length,
      needsPromptCutsCount: needsPromptCuts.length,
    };
  }

  private isCutReadyForImageGeneration(
    cut: AiWebtoonCutChapterEntity,
  ): boolean {
    if (
      [
        EAiBaseModelGenImageType.SDXL_4_OPT,
        EAiBaseModelGenImageType.DREAM_BOOTH,
      ].includes(cut.baseModel) &&
      !cut.danbooruPrompt
    ) {
      return false;
    }

    if (!cut.generalPrompt) {
      return false;
    }

    if (cut.negativeDescription && !cut.negativePrompt) {
      return false;
    }
    return true;
  }

  private async generateImagesForCuts(
    chapterId: number,
    cuts: AiWebtoonCutChapterEntity[],
  ) {
    this.logger.log(`Generating images for ${cuts.length} cuts`);

    const cutsWithAllPrompts = cuts.filter((cut) => {
      return (
        !cut.isGeneratingPrompt &&
        !cut.isGeneratingNegativePrompt &&
        !cut.isGeneratingDanbooruPrompt
      );
    });

    await this.aiWebtoonCutChapterService.generateImagesByImportFile(
      chapterId,
      cutsWithAllPrompts,
    );
  }

  async mergePreviewImages(body: MergePreviewImagesDto) {
    this.logger.log(`mergePreviewImages ~ body >> ${JSON.stringify(body)}`);

    const chapter = await this.aiWebtoonChapterRepository.findOne({
      where: { id: body.chapterId },
    });

    if (!chapter) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHAPTER_NOT_FOUND);
    }

    await this.aiService.sendMergePreviewImagesRequest(body);

    return true;
  }

  async receiveMergePreviewImages(body: ReceiveMergePreviewImagesDto) {
    this.logger.log(
      `receiveMergePreviewImages ~ body >> ${JSON.stringify(body)}`,
    );

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Receive Merge Preview Images - Chapter ${body.chapterId} - ${body.images.length} Image(s)`,
        data: body,
        isSuccess: true,
      },
    );

    // this.socketService.emitMergePreviewImages(body.chapterId, body.images);
    return true;
  }
}
