import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import {
  UploadEntity,
  UploaderType,
  UploadContext,
} from '../entities/upload.entity';

@Injectable()
export class UploadRepository extends Repository<UploadEntity> {
  constructor(private dataSource: DataSource) {
    super(UploadEntity, dataSource.createEntityManager());
  }

  async findUserFiles(
    userId: number,
    context?: UploadContext,
    page: number = 1,
    limit: number = 20,
  ): Promise<[UploadEntity[], number]> {
    const queryBuilder = this.createQueryBuilder('upload')
      .where('upload.uploaderId = :userId', { userId })
      .andWhere('upload.uploaderType = :uploaderType', {
        uploaderType: UploaderType.USER,
      });

    if (context) {
      queryBuilder.andWhere('upload.uploadContext = :context', { context });
    }

    return queryBuilder
      .orderBy('upload.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();
  }

  async findAdminFiles(
    context?: UploadContext,
    page: number = 1,
    limit: number = 20,
  ): Promise<[UploadEntity[], number]> {
    const queryBuilder = this.createQueryBuilder('upload').where(
      'upload.uploaderType = :uploaderType',
      {
        uploaderType: UploaderType.ADMIN,
      },
    );

    if (context) {
      queryBuilder.andWhere('upload.uploadContext = :context', { context });
    }

    return queryBuilder
      .orderBy('upload.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();
  }

  async findUserFileById(
    uploadId: number,
    userId: number,
  ): Promise<UploadEntity | null> {
    return this.findOne({
      where: {
        id: uploadId,
        uploaderId: userId,
        uploaderType: UploaderType.USER,
      },
    });
  }

  async findAdminFileById(uploadId: number): Promise<UploadEntity | null> {
    return this.findOne({
      where: {
        id: uploadId,
        uploaderType: UploaderType.ADMIN,
      },
    });
  }

  async getUserStorageUsage(userId: number): Promise<number> {
    const result = await this.createQueryBuilder('upload')
      .select('SUM(upload.fileSize)', 'totalSize')
      .where('upload.uploaderId = :userId', { userId })
      .andWhere('upload.uploaderType = :uploaderType', {
        uploaderType: UploaderType.USER,
      })
      .getRawOne();

    return parseInt(result?.totalSize || '0');
  }

  async getUserFileCount(
    userId: number,
    context?: UploadContext,
  ): Promise<number> {
    const queryBuilder = this.createQueryBuilder('upload')
      .where('upload.uploaderId = :userId', { userId })
      .andWhere('upload.uploaderType = :uploaderType', {
        uploaderType: UploaderType.USER,
      });

    if (context) {
      queryBuilder.andWhere('upload.uploadContext = :context', { context });
    }

    return queryBuilder.getCount();
  }
}
