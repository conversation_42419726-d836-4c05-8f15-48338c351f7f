import { buildMessage, ValidateBy, ValidationOptions } from 'class-validator';

export const IS_VALID_SEED = 'isValidSeed';

/**
 * Checks if the string is a valid seed.
 * A valid seed is a string representing a number less than 18446744073709551615.
 */
export function isValidSeed(value: unknown): boolean {
  if (typeof value !== 'string') {
    return false;
  }
  if (value === '') {
    return true; // Allow empty string
  }
  if (!/^\d+$/.test(value)) {
    return false; // Must be all digits
  }
  try {
    const num = BigInt(value);
    const maxSeed = BigInt('18446744073709551615');
    return num <= maxSeed;
  } catch {
    return false; // Not a valid BigInt or other error
  }
}

/**
 * Decorator that checks if the string is a valid seed.
 * A valid seed is a string representing a number less than 18446744073709551615.
 */
export function IsValidSeed(
  validationOptions?: ValidationOptions,
): PropertyDecorator {
  return ValidateBy(
    {
      name: IS_VALID_SEED,
      validator: {
        validate: (value): boolean => isValidSeed(value),
        defaultMessage: buildMessage(
          (eachPrefix) =>
            eachPrefix +
            '$property must be a string representing a number less than 18446744073709551615',
          validationOptions,
        ),
      },
    },
    validationOptions,
  );
}
