import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import * as fs from 'fs/promises';
import * as path from 'path';
import { UPLOAD_QUEUES } from '../constants/upload-queues.constant';
import {
  ExternalUploadJobData,
  ExternalUploadJobResult,
} from '../interfaces/external-upload-job.interface';
import { SignatureGeneratorService } from '../services/signature-generator.service';
import { ExternalUploadClientService } from '../services/external-upload-client.service';
import { FileProcessingService } from '../../shared/services/file-processing.service';

@Injectable()
@Processor(UPLOAD_QUEUES.EXTERNAL_UPLOAD)
export class ExternalUploadProcessor extends WorkerHost {
  private readonly logger = new Logger(ExternalUploadProcessor.name);

  constructor(
    private signatureGenerator: SignatureGeneratorService,
    private uploadClient: ExternalUploadClientService,
    private fileProcessingService: FileProcessingService,
  ) {
    super();
  }

  async process(
    job: Job<ExternalUploadJobData>,
  ): Promise<ExternalUploadJobResult> {
    const { type, imagePath, uploadUrl, metadata } = job.data;

    this.logger.debug(
      `Processing external upload job ${job.id}: ${imagePath} -> ${uploadUrl}`,
    );

    try {
      // 1. Validate file exists and is accessible
      const fullPath = path.join(process.cwd(), imagePath.replace(/^\//, ''));
      await fs.access(fullPath, fs.constants.R_OK);

      // 2. Get file stats and metadata
      const fileStats = await fs.stat(fullPath);
      const fileName = path.basename(imagePath);

      this.logger.debug(`File ${fileName} size: ${fileStats.size} bytes`);

      // 3. Extract file metadata using existing service
      // IMPORTANT: Don't process the file with Sharp, just get basic info
      const fileMetadata = {
        originalName: fileName,
        size: fileStats.size,
        mimeType: this.getMimeType(fileName),
        uploadedAt: new Date().toISOString(),
      };

      // 4. Generate RSA signature for the file
      const signature =
        await this.signatureGenerator.generateSignatureFromPath(imagePath);
      const publicKey = this.signatureGenerator.getPublicKey();

      // 5. Prepare upload request
      const uploadRequest = {
        imagePath,
        uploadUrl,
        signature,
        publicKey,
        type,
        metadata: {
          ...metadata,
          fileMetadata,
          originalPath: imagePath,
          fileSize: fileStats.size,
          fileName,
          processedAt: new Date().toISOString(),
        },
      };

      // 6. Upload to external service
      const uploadResponse =
        await this.uploadClient.uploadToExternal(uploadRequest);

      // Validate response
      if (!uploadResponse || !uploadResponse.data.success) {
        throw new Error(
          `External service returned failure: ${JSON.stringify(uploadResponse)}`,
        );
      }

      // 7. Log success and return result
      this.logger.log(
        `Successfully processed external upload job ${job.id} for ${imagePath}`,
      );
      this.logger.debug(
        `Upload response: ${JSON.stringify(uploadResponse, null, 2)}`,
      );

      return {
        success: true,
        originalPath: imagePath,
        externalUrl:
          uploadResponse.url ||
          uploadResponse.fileUrl ||
          uploadResponse.downloadUrl ||
          uploadResponse.imageUrl,
        uploadResponse,
      };
    } catch (error) {
      this.logger.error(
        `Failed to process external upload job ${job.id} for ${imagePath}: ${error.message}`,
        error.stack,
      );

      // Re-throw error to trigger retry mechanism
      throw new Error(`External upload failed: ${error.message}`);
    }
  }

  private getMimeType(fileName: string): string {
    const ext = path.extname(fileName).toLowerCase();
    const mimeTypes: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.bmp': 'image/bmp',
      '.svg': 'image/svg+xml',
    };
    return mimeTypes[ext] || 'application/octet-stream';
  }

  // Optional: Handle job completion
  onCompleted(job: Job<ExternalUploadJobData>) {
    this.logger.log(`External upload job ${job.id} completed successfully`);

    // You could add post-processing here like:
    // - Update database records
    // - Send notifications
    // - Trigger analytics events
    // - Clean up temporary files
  }

  // Optional: Handle job failure
  onFailed(job: Job<ExternalUploadJobData>, error: Error) {
    this.logger.error(`External upload job ${job.id} failed: ${error.message}`);

    // You could add failure handling here like:
    // - Log to error tracking service
    // - Send failure notifications
    // - Update database with error status
    // - Trigger rollback procedures
  }
}
