import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { AiWebtoonStoryAiLetteringEntity } from '../entities/ai-webtoon-story-ai-lettering.entity';

@Injectable()
export class AiWebtoonStoryAiLetteringRepository extends Repository<AiWebtoonStoryAiLetteringEntity> {
  constructor(private dataSource: DataSource) {
    super(AiWebtoonStoryAiLetteringEntity, dataSource.createEntityManager());
  }
}
