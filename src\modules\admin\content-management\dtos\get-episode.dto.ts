import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional } from 'class-validator';
import { IsNotEmpty } from 'class-validator';

export class GetEpisodeDto {
  @ApiProperty({
    description: 'The ID of the content',
    example: 1,
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  contentId: number;

  @ApiProperty({
    description: 'The page number',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page: number = 1;

  @ApiProperty({
    description: 'The limit number',
    example: 10,
    required: false,
  })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  limit: number = 10;
}
