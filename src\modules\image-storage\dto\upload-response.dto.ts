import { ApiProperty } from '@nestjs/swagger';

export enum UploadStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export class UploadImageResponseDto {
  @ApiProperty({
    description: 'Upload operation success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Unique identifier for upload session',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  uploadId: string;

  @ApiProperty({
    description: 'Current upload status',
    enum: UploadStatus,
    example: UploadStatus.PROCESSING,
  })
  status: UploadStatus;

  @ApiProperty({
    description: 'Estimated processing time in seconds',
    example: 30,
    required: false,
  })
  estimatedTime?: number;

  @ApiProperty({
    description: 'Webhook URL that will receive completion notification',
    example: 'https://api.myapp.com/webhooks/image-upload',
    required: false,
  })
  webhookUrl?: string;
}

export class UploadStatusResponseDto {
  @ApiProperty({
    description: 'Upload ID',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  uploadId: string;

  @ApiProperty({
    description: 'Current upload status',
    enum: UploadStatus,
    example: UploadStatus.COMPLETED,
  })
  status: UploadStatus;

  @ApiProperty({
    description: 'Processing progress (0-100)',
    example: 85,
    required: false,
  })
  progress?: number;

  @ApiProperty({
    description: 'Final image URL (available when completed)',
    example: 'https://storage.example.com/images/episode/image_123.jpg',
    required: false,
  })
  imageUrl?: string;

  @ApiProperty({
    description: 'Thumbnail URL (available when completed)',
    example:
      'https://storage.example.com/images/episode/thumbnails/image_123_thumb.jpg',
    required: false,
  })
  thumbnailUrl?: string;

  @ApiProperty({
    description: 'Error message (available when failed)',
    example: 'Invalid signature verification',
    required: false,
  })
  error?: string;

  @ApiProperty({
    description: 'Upload creation timestamp',
    example: '2024-08-05T10:30:00.000Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Upload completion timestamp',
    example: '2024-08-05T10:30:45.000Z',
    required: false,
  })
  completedAt?: string;
}

export class ErrorDetailsDto {
  @ApiProperty({
    description: 'Error code',
    example: 'INVALID_SIGNATURE',
  })
  code: string;

  @ApiProperty({
    description: 'Error message',
    example: 'Signature verification failed',
  })
  message: string;

  @ApiProperty({
    description: 'Additional error details',
    required: false,
  })
  details?: any;
}

export class ErrorResponseDto {
  @ApiProperty({
    description: 'Operation success status',
    example: false,
  })
  success: boolean;

  @ApiProperty({
    description: 'Error details',
    type: ErrorDetailsDto,
  })
  error: ErrorDetailsDto;
}
