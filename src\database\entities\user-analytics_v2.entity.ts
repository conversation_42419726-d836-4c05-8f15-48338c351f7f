import { Entity, Column, Index } from 'typeorm';
import { DefaultEntity } from './default.entity';

export enum UserPlatform {
  PC = 'pc',
  MOBILE = 'mobile',
}

@Entity('user_analytics_v2')
@Index(['date', 'hour', 'language'], { unique: true })
export class UserAnalyticsEntityV2 extends DefaultEntity {
  @Column({ type: 'varchar', length: 10 }) // '2025/01/01'
  @Index()
  date: string;

  @Column({ type: 'tinyint' }) // 0-23
  @Index()
  hour: number;

  @Column({ type: 'int', default: 0 })
  activeUsers: number;

  @Column({ type: 'int', default: 0 })
  newRegistrations: number;

  @Column({ type: 'int', default: 0 })
  deletedAccounts: number;

  @Column({ type: 'int', default: 0 })
  loginCount: number;

  @Column({ type: 'int', default: 0 })
  uniqueLogins: number;

  @Column({ type: 'int', default: 0 })
  adultVerifiedCount: number;

  @Column({ type: 'int', default: 0 })
  firtPurchaseCount: number;

  @Column({ type: 'int', default: 0 })
  firstPurchaseAttemptCount: number;

  @Column({ type: 'int', default: 0 })
  firstPurchaseAmount: number;

  @Column({ type: 'int', default: 0 })
  rePurchaseCount: number;

  @Column({ type: 'int', default: 0 })
  rePurchaseAttemptCount: number;

  @Column({ type: 'int', default: 0 })
  rePurchaseAmount: number;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'int' })
  language: number;

  @Column({ type: 'int', default: 0 })
  viewCount: number; // Total page views
}
