import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserNotiReceipt1754454615287 implements MigrationInterface {
  name = 'UpdateUserNotiReceipt1754454615287';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`user_notification_recipient\` ADD \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)`,
    );
    await queryRunner.query(
      `ALTER TABLE \`user_notification_recipient\` ADD \`deletedAt\` datetime(6) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`user_notification_recipient\` DROP COLUMN \`deletedAt\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`user_notification_recipient\` DROP COLUMN \`updatedAt\``,
    );
  }
}
