import { DataSource, In, Repository } from 'typeorm';
import { Injectable, NotFoundException } from '@nestjs/common';
import { SettingEntity } from '../entities/setting.entity';
import { SettingGroup } from '../../common/setting.enum';
import { MESSAGE_CONFIG } from '../../common/message.config';
@Injectable()
export class SettingRepository extends Repository<SettingEntity> {
  constructor(private dataSource: DataSource) {
    super(SettingEntity, dataSource.createEntityManager());
  }

  async findOneById(id: number) {
    const setting = await this.findOne({
      where: {
        id,
      },
    });

    return setting;
  }
  async findOneByIdAndGroup(id: number, group: SettingGroup) {
    const setting = await this.findOne({
      where: {
        id,
        group,
      },
    });

    return setting;
  }

  findManyByIdsAndGroup(ids: number[], group: SettingGroup) {
    return this.find({
      where: {
        id: In(ids),
        group,
      },
    });
  }

  async findClassifications() {
    return await this.findSettingByGroup(SettingGroup.CONTENT_CLASSIFICATION);
  }

  async findDates() {
    return await this.findSettingByGroup(SettingGroup.CONTENT_DATE);
  }

  async findPaymentFormat() {
    return await this.findSettingByGroup(SettingGroup.AUTHOR_PAYMENT_FORMAT);
  }

  async findSettingByGroup(group: SettingGroup) {
    const settings = await this.find({
      where: {
        group,
      },
    });
    if (!settings) {
      throw new NotFoundException(
        MESSAGE_CONFIG.SETTING_NOT_FOUND.message + `: ${group}`,
      );
    }
    return settings;
  }
}
