import 'dotenv/config';
import { DataSource, DataSourceOptions } from 'typeorm';
const config: DataSourceOptions = {
  type: 'mysql',
  host: process.env.DB_HOST || 'db',
  port: parseInt(process.env.DB_PORT || '3306', 10),
  username: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '1',
  database: process.env.DB_DATABASE || 'nesttest',
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  migrations: [__dirname + '/../**/migrations/*{.ts,.js}'],
  migrationsTableName: 'migrations',
  synchronize: false,
};
const dataSource: DataSource = new DataSource(config);
export default dataSource;
