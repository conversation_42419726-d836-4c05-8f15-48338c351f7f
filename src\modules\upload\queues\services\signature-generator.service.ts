import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as fs from 'fs/promises';
import * as path from 'path';
import configurationGlobal from '../../../../config/configuration.global';

@Injectable()
export class SignatureGeneratorService {
  private readonly logger = new Logger(SignatureGeneratorService.name);
  private readonly privateKey: string;
  private readonly publicKey: string;

  constructor(private configService: ConfigService) {
    this.privateKey = configurationGlobal().signature.privateKey;
    this.publicKey = configurationGlobal().signature.publicKey;

    if (!this.privateKey || !this.publicKey) {
      throw new Error(
        'SIGNATURE_PRIVATE_KEY and SIGNATURE_PUBLIC_KEY must be configured in environment variables',
      );
    }

    // Convert escaped newlines if they exist
    this.privateKey = this.privateKey.replace(/\\n/g, '\n');
    this.publicKey = this.publicKey.replace(/\\n/g, '\n');

    // Validate keys on initialization
    this.validateKeysOnStartup();
  }

  /**
   * Validate keys during service initialization
   */
  private validateKeysOnStartup() {
    try {
      this.logger.log('Validating signature keys on startup...');

      if (!this.isValidPrivateKey(this.privateKey)) {
        throw new Error('Invalid private key format detected on startup');
      }

      if (!this.isValidPublicKey(this.publicKey)) {
        throw new Error('Invalid public key format detected on startup');
      }

      // Test signing and verification
      const testBuffer = Buffer.from('startup-validation-test');
      const signature = this.generateSignatureFromBuffer(testBuffer);
      const isValid = this.verifySignature(testBuffer, signature);

      if (!isValid) {
        throw new Error(
          'Key pair validation failed - signature verification test failed',
        );
      }

      this.logger.log('✅ Signature keys validated successfully');
    } catch (error) {
      this.logger.error('❌ Key validation failed on startup:', error.message);
      throw error;
    }
  }

  async generateSignatureFromPath(imagePath: string): Promise<string> {
    try {
      const fullPath = path.join(process.cwd(), imagePath.replace(/^\//, ''));

      const fileBuffer = await fs.readFile(fullPath);
      console.log(`Generating signature for file: ${imagePath}`);
      console.log(`File size: ${fileBuffer.length} bytes`);

      // Check if file is corrupted before signing
      if (fileBuffer.length < 10) {
        this.logger.warn(
          `WARNING: File ${imagePath} is suspiciously small: ${fileBuffer.length} bytes`,
        );
      }

      // Log first few bytes for debugging
      const firstBytes = fileBuffer.slice(0, Math.min(20, fileBuffer.length));
      console.log(`First bytes of file: ${firstBytes.toString('hex')}`);

      // Check if file is all null bytes
      const isCorrupted = fileBuffer
        .slice(0, Math.min(100, fileBuffer.length))
        .every((byte) => byte === 0);
      if (isCorrupted) {
        this.logger.error(
          `ERROR: File ${imagePath} appears to be corrupted (null bytes)`,
        );
      }

      // Validate private key format before using
      if (!this.isValidPrivateKey(this.privateKey)) {
        throw new Error('Invalid private key format');
      }

      const sign = crypto.createSign('SHA256');
      sign.update(fileBuffer); // Sign the file buffer, not the path
      const signature = sign.sign(this.privateKey, 'hex'); // Use hex instead of base64 for consistency

      this.logger.debug(`Generated signature for file: ${imagePath}`);
      return signature;
    } catch (error) {
      this.logger.error(
        `Failed to generate signature for ${imagePath}: ${error.message}`,
      );
      throw new Error(`Failed to generate signature: ${error.message}`);
    }
  }

  generateSignatureFromBuffer(buffer: Buffer): string {
    try {
      // Validate private key format before using
      if (!this.isValidPrivateKey(this.privateKey)) {
        throw new Error('Invalid private key format');
      }

      const sign = crypto.createSign('SHA256'); // Use consistent algorithm
      sign.update(buffer);
      return sign.sign(this.privateKey, 'hex'); // Use hex for consistency
    } catch (error) {
      this.logger.error(
        `Failed to generate signature from buffer: ${error.message}`,
      );
      throw new Error(`Failed to generate signature: ${error.message}`);
    }
  }

  getPublicKey(): string {
    return this.publicKey;
  }

  verifySignature(
    data: Buffer,
    signature: string,
    publicKey?: string,
  ): boolean {
    try {
      const keyToUse = publicKey || this.publicKey;

      // Validate public key format
      if (!this.isValidPublicKey(keyToUse)) {
        this.logger.error('Invalid public key format');
        return false;
      }

      const verify = crypto.createVerify('SHA256'); // Use consistent algorithm
      verify.update(data);
      return verify.verify(keyToUse, signature, 'hex'); // Use hex for consistency
    } catch (error) {
      this.logger.error(`Failed to verify signature: ${error.message}`);
      return false;
    }
  }

  /**
   * Validate private key format
   */
  private isValidPrivateKey(privateKey: string): boolean {
    try {
      // Check if it's a valid PEM format
      const pemRegex =
        /^-----BEGIN (RSA )?PRIVATE KEY-----[\s\S]*-----END (RSA )?PRIVATE KEY-----$/;
      if (!pemRegex.test(privateKey.trim())) {
        this.logger.error('Private key is not in valid PEM format');
        return false;
      }

      // Try to create a crypto object to validate the key
      crypto.createPrivateKey(privateKey);
      return true;
    } catch (error) {
      this.logger.error(`Private key validation failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Validate public key format
   */
  private isValidPublicKey(publicKey: string): boolean {
    try {
      // Check if it's a valid PEM format
      const pemRegex =
        /^-----BEGIN PUBLIC KEY-----[\s\S]*-----END PUBLIC KEY-----$/;
      if (!pemRegex.test(publicKey.trim())) {
        this.logger.error('Public key is not in valid PEM format');
        return false;
      }

      // Try to create a crypto object to validate the key
      crypto.createPublicKey(publicKey);
      return true;
    } catch (error) {
      this.logger.error(`Public key validation failed: ${error.message}`);
      return false;
    }
  }
}
