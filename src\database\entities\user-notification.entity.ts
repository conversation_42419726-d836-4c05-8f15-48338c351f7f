import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
} from 'typeorm';
import { UserNotificationRecipientEntity } from './user-notification-recipient.entity';

export enum NotificationStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

@Entity('user_notification')
export class UserNotificationEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ default: 'root' })
  name: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({
    type: 'bigint',
    comment: 'Unix timestamp for start display time (from 00:00)',
  })
  startTime: number;

  @Column({
    type: 'bigint',
    comment: 'Unix timestamp for end display time (until 23:59)',
  })
  endTime: number;

  @Column({
    type: 'enum',
    enum: NotificationStatus,
    default: NotificationStatus.ACTIVE,
  })
  status: NotificationStatus;

  @Column({ type: 'text' })
  content: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;

  @OneToMany(
    () => UserNotificationRecipientEntity,
    (recipient) => recipient.userNotification,
  )
  recipients: UserNotificationRecipientEntity[];
}
