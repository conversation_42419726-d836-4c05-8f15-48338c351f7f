import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import {
  UploadStatus,
  UploadStatusResponseDto,
} from '../dto/upload-response.dto';
import {
  ImageMetadata,
  UploadMetadata,
} from '../interfaces/image-storage.interface';
import configurationGlobal from '../../../config/configuration.global';

@Injectable()
export class StatusService implements OnModuleInit {
  private readonly logger = new Logger(StatusService.name);
  private readonly REDIS_PREFIX = 'image-storage:status:';
  private readonly REDIS_TTL = 24 * 60 * 60; // 24 hours
  private redis: Redis;

  constructor(private readonly configService: ConfigService) {}

  onModuleInit() {
    const redisUrl = this.configService.get(
      'imageStorage.redis.url',
      `redis://${configurationGlobal().redis.host}:${configurationGlobal().redis.port}`,
    );
    this.redis = new Redis(redisUrl);
    this.logger.log('Redis connection initialized for status tracking');
  }

  /**
   * Create initial upload status
   */
  async createUploadStatus(
    uploadId: string,
    metadata: ImageMetadata,
  ): Promise<void> {
    try {
      this.logger.log(`Creating upload status for ${uploadId}`);

      const uploadMetadata: UploadMetadata = {
        ...metadata,
        uploadId,
        status: UploadStatus.PENDING,
        progress: 0,
        createdAt: new Date().toISOString(),
      };

      // Store in Redis
      await this.redis.setex(
        `${this.REDIS_PREFIX}${uploadId}`,
        this.REDIS_TTL,
        JSON.stringify(uploadMetadata),
      );

      this.logger.log(`Upload status created for ${uploadId}`);
    } catch (error) {
      this.logger.error(
        `Failed to create upload status for ${uploadId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update upload status and progress
   */
  async updateUploadStatus(
    uploadId: string,
    status: UploadStatus,
    progress?: number,
    error?: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Updating status for ${uploadId}: ${status} (${progress || 0}%)`,
      );

      // Get existing data
      const cachedData = await this.redis.get(
        `${this.REDIS_PREFIX}${uploadId}`,
      );
      if (!cachedData) {
        this.logger.warn(`No existing status found for ${uploadId}`);
        return;
      }

      const metadata: UploadMetadata = JSON.parse(cachedData);
      metadata.status = status;
      metadata.progress = progress || 0;

      if (error) {
        metadata.error = error;
      }

      if (status === UploadStatus.COMPLETED) {
        metadata.completedAt = new Date().toISOString();
      }

      // Update Redis
      await this.redis.setex(
        `${this.REDIS_PREFIX}${uploadId}`,
        this.REDIS_TTL,
        JSON.stringify(metadata),
      );

      this.logger.log(`Status updated for ${uploadId}`);
    } catch (error) {
      this.logger.error(`Failed to update status for ${uploadId}:`, error);
      throw error;
    }
  }

  /**
   * Complete upload with final URLs
   */
  async completeUpload(
    uploadId: string,
    imageUrl: string,
    thumbnailUrl: string,
    sizes?: { small?: string; medium?: string; large?: string },
  ): Promise<void> {
    try {
      console.log(sizes);
      this.logger.log(`Completing upload ${uploadId}`);

      // Get existing data
      const cachedData = await this.redis.get(
        `${this.REDIS_PREFIX}${uploadId}`,
      );
      if (!cachedData) {
        this.logger.warn(`No existing status found for ${uploadId}`);
        return;
      }

      const metadata: UploadMetadata = JSON.parse(cachedData);
      metadata.status = UploadStatus.COMPLETED;
      metadata.progress = 100;
      metadata.imageUrl = imageUrl;
      metadata.thumbnailUrl = thumbnailUrl;
      metadata.completedAt = new Date().toISOString();

      // Update Redis with extended TTL for completed uploads
      await this.redis.setex(
        `${this.REDIS_PREFIX}${uploadId}`,
        this.REDIS_TTL * 7, // Keep completed uploads for 7 days
        JSON.stringify(metadata),
      );

      this.logger.log(`Upload completed: ${uploadId}`);
    } catch (error) {
      this.logger.error(`Failed to complete upload ${uploadId}:`, error);
      throw error;
    }
  }

  /**
   * Get upload status
   */
  async getUploadStatus(
    uploadId: string,
  ): Promise<UploadStatusResponseDto | null> {
    try {
      const cachedData = await this.redis.get(
        `${this.REDIS_PREFIX}${uploadId}`,
      );
      if (!cachedData) {
        return null;
      }

      const metadata: UploadMetadata = JSON.parse(cachedData);
      return this.mapToStatusResponse(metadata);
    } catch (error) {
      this.logger.error(`Failed to get status for ${uploadId}:`, error);
      return null;
    }
  }

  /**
   * Delete upload status (cleanup)
   */
  async deleteUploadStatus(uploadId: string): Promise<void> {
    try {
      this.logger.log(`Deleting upload status for ${uploadId}`);
      await this.redis.del(`${this.REDIS_PREFIX}${uploadId}`);
      this.logger.log(`Upload status deleted for ${uploadId}`);
    } catch (error) {
      this.logger.error(`Failed to delete status for ${uploadId}:`, error);
    }
  }

  /**
   * Get all upload statuses with pattern matching
   */
  async getUploadStatuses(
    limit: number = 100,
    status?: UploadStatus,
  ): Promise<UploadStatusResponseDto[]> {
    try {
      const pattern = `${this.REDIS_PREFIX}*`;
      const keys = await this.redis.keys(pattern);

      if (keys.length === 0) {
        return [];
      }

      // Get all values
      const values = await this.redis.mget(...keys);
      const results: UploadStatusResponseDto[] = [];

      for (const value of values) {
        if (value) {
          try {
            const metadata: UploadMetadata = JSON.parse(value);

            // Filter by status if provided
            if (status && metadata.status !== status) {
              continue;
            }

            results.push(this.mapToStatusResponse(metadata));
          } catch (parseError) {
            this.logger.warn('Failed to parse cached metadata:', parseError);
          }
        }
      }

      // Sort by creation date (newest first) and limit
      return results
        .sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
        )
        .slice(0, limit);
    } catch (error) {
      this.logger.error('Failed to get upload statuses:', error);
      return [];
    }
  }

  /**
   * Clean up old upload statuses
   */
  async cleanupOldStatuses(olderThanHours: number = 24): Promise<number> {
    try {
      const pattern = `${this.REDIS_PREFIX}*`;
      const keys = await this.redis.keys(pattern);

      if (keys.length === 0) {
        return 0;
      }

      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - olderThanHours);

      let deletedCount = 0;
      const values = await this.redis.mget(...keys);

      for (let i = 0; i < keys.length; i++) {
        const value = values[i];
        if (value) {
          try {
            const metadata: UploadMetadata = JSON.parse(value);
            const createdAt = new Date(metadata.createdAt);

            // Delete if older than cutoff and not completed
            if (
              createdAt < cutoffTime &&
              metadata.status !== UploadStatus.COMPLETED
            ) {
              await this.redis.del(keys[i]);
              deletedCount++;
            }
          } catch (parseError) {
            this.logger.warn('Failed to parse cached metadata:', parseError);
            // Delete corrupted entries
            await this.redis.del(keys[i]);
            deletedCount++;
          }
        }
      }

      this.logger.log(`Cleaned up ${deletedCount} old upload statuses`);
      return deletedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup old statuses:', error);
      return 0;
    }
  }

  /**
   * Map metadata to status response DTO
   */
  private mapToStatusResponse(
    metadata: UploadMetadata,
  ): UploadStatusResponseDto {
    return {
      uploadId: metadata.uploadId,
      status: metadata.status,
      progress: metadata.progress,
      imageUrl: metadata.imageUrl,
      thumbnailUrl: metadata.thumbnailUrl,
      error: metadata.error,
      createdAt: metadata.createdAt,
      completedAt: metadata.completedAt,
    };
  }

  /**
   * Get upload statistics from Redis
   */
  async getUploadStats(): Promise<{
    total: number;
    byStatus: Record<UploadStatus, number>;
    last24Hours: number;
  }> {
    const stats = {
      total: 0,
      byStatus: {
        [UploadStatus.PENDING]: 0,
        [UploadStatus.PROCESSING]: 0,
        [UploadStatus.COMPLETED]: 0,
        [UploadStatus.FAILED]: 0,
      } as Record<UploadStatus, number>,
      last24Hours: 0,
    };

    try {
      const pattern = `${this.REDIS_PREFIX}*`;
      const keys = await this.redis.keys(pattern);

      if (keys.length === 0) {
        return stats;
      }

      const values = await this.redis.mget(...keys);
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      for (const value of values) {
        if (value) {
          try {
            const metadata: UploadMetadata = JSON.parse(value);
            stats.total++;
            stats.byStatus[metadata.status]++;

            const createdAt = new Date(metadata.createdAt);
            if (createdAt > yesterday) {
              stats.last24Hours++;
            }
          } catch (parseError) {
            this.logger.warn('Failed to parse cached metadata:', parseError);
            // Skip corrupted entries
          }
        }
      }
    } catch (error) {
      this.logger.error('Failed to get upload stats:', error);
    }

    return stats;
  }
}
