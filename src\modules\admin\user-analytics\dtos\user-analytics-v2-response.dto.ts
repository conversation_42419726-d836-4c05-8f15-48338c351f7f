import { ApiProperty } from '@nestjs/swagger';

export class BaseAnalyticsV2StatsDto {
  @ApiProperty({
    description: 'Language (specific language code or "all")',
    example: 'en',
  })
  language: string;

  @ApiProperty({
    description: 'Active users count',
    example: 550,
  })
  activeUsers: number;

  @ApiProperty({
    description: 'New registrations count',
    example: 85,
  })
  newRegistrations: number;

  @ApiProperty({
    description: 'Deleted accounts count',
    example: 7,
  })
  deletedAccounts: number;

  @ApiProperty({
    description: 'Login count',
    example: 1100,
  })
  loginCount: number;

  @ApiProperty({
    description: 'Unique logins count',
    example: 550,
  })
  uniqueLogins: number;

  @ApiProperty({
    description: 'Adult verified count',
    example: 45,
  })
  adultVerifiedCount: number;

  @ApiProperty({
    description: 'First purchase count',
    example: 30,
  })
  firtPurchaseCount: number;

  @ApiProperty({
    description: 'First purchase attempt count',
    example: 35,
  })
  firstPurchaseAttemptCount: number;

  @ApiProperty({
    description: 'First purchase amount',
    example: 150000,
  })
  firstPurchaseAmount: number;

  @ApiProperty({
    description: 'Re-purchase count',
    example: 120,
  })
  rePurchaseCount: number;

  @ApiProperty({
    description: 'Re-purchase attempt count',
    example: 125,
  })
  rePurchaseAttemptCount: number;

  @ApiProperty({
    description: 'Re-purchase amount',
    example: 600000,
  })
  rePurchaseAmount: number;
}

export class HourlyAnalyticsV2StatsDto extends BaseAnalyticsV2StatsDto {
  @ApiProperty({
    description: 'Date (YYYY/MM/DD)',
    example: '2025/01/15',
  })
  date: string;

  @ApiProperty({
    description: 'Hour (0-23)',
    example: 14,
  })
  hour: number;
}

export class DailyAnalyticsV2StatsDto extends BaseAnalyticsV2StatsDto {
  @ApiProperty({
    description: 'Date (YYYY/MM/DD)',
    example: '2025/01/15',
  })
  date: string;
}

export class MonthlyAnalyticsV2StatsDto extends BaseAnalyticsV2StatsDto {
  @ApiProperty({
    description: 'Month (YYYY/MM)',
    example: '2025/01',
  })
  month: string;

  @ApiProperty({
    description: 'Average daily active users',
    example: 550,
  })
  avgDailyActiveUsers: number;
}

export class HourlyAnalyticsV2ResponseDto {
  @ApiProperty({
    description: 'Array of hourly analytics statistics',
    type: [HourlyAnalyticsV2StatsDto],
  })
  data: HourlyAnalyticsV2StatsDto[];

  @ApiProperty({
    description: 'Total number of records',
    example: 24,
  })
  total: number;

  @ApiProperty({
    description: 'Query date',
    example: '2025/01/15',
  })
  date: string;

  @ApiProperty({
    description: 'Language filter used',
    example: 'en',
  })
  language: string;
}

export class DailyAnalyticsV2ResponseDto {
  @ApiProperty({
    description: 'Array of daily analytics statistics',
    type: [DailyAnalyticsV2StatsDto],
  })
  data: DailyAnalyticsV2StatsDto[];

  @ApiProperty({
    description: 'Total number of records',
    example: 31,
  })
  total: number;

  @ApiProperty({
    description: 'Start date',
    example: '2025/01/15',
  })
  startDate: string;

  @ApiProperty({
    description: 'End date',
    example: '2025/03/28',
  })
  endDate: string;

  @ApiProperty({
    description: 'Language filter used',
    example: 'en',
  })
  language: string;
}

export class MonthlyAnalyticsV2ResponseDto {
  @ApiProperty({
    description: 'Array of monthly analytics statistics',
    type: [MonthlyAnalyticsV2StatsDto],
  })
  data: MonthlyAnalyticsV2StatsDto[];

  @ApiProperty({
    description: 'Total number of records',
    example: 8,
  })
  total: number;

  @ApiProperty({
    description: 'Start month',
    example: '2025/01',
  })
  startMonth: string;

  @ApiProperty({
    description: 'End month',
    example: '2025/08',
  })
  endMonth: string;

  @ApiProperty({
    description: 'Language filter used',
    example: 'en',
  })
  language: string;
}
