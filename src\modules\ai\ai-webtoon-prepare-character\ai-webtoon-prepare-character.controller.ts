import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { AiWebtoonPrepareCharacterService } from './ai-webtoon-prepare-character.service';
import { CreateAiWebtoonPrepareCharacterDto } from './dto/create-prepare-character.dto';
import { GenerateCollectionImagesDto } from './dto/generate-collection-images.dto';
import { ListWebtoonAuthorsDto } from './dto/list-prepare-character.dto';
import { ReceiveCollectionImagesDto } from './dto/receive-collection-images.dto';
import { UpdateAiWebtoonPrepareCharacterDto } from './dto/update-prepare-character.dto';
import { JwtAdminAuthGuard } from 'src/modules/admin/auth-admin/guards/jwt-admin-auth.guard';

@ApiTags('ai-webtoon-prepare-character')
@Controller('ai-webtoon-prepare-character')
export class AiWebtoonPrepareCharacterController {
  constructor(
    private readonly aiWebtoonPrepareCharacterService: AiWebtoonPrepareCharacterService,
  ) {}

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('generate-collection-images/:id')
  generateCollectionImages(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: GenerateCollectionImagesDto,
    @Req() req,
  ) {
    return this.aiWebtoonPrepareCharacterService.generateCollectionImages(
      id,
      body,
      req.user.id,
    );
  }

  @Post('/receive-collection-images')
  receiveCollectionImages(@Body() body: ReceiveCollectionImagesDto) {
    return this.aiWebtoonPrepareCharacterService.receiveCollectionImages(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('/all')
  listAll() {
    return this.aiWebtoonPrepareCharacterService.listAll();
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('/:id')
  detail(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonPrepareCharacterService.detail(id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get()
  list(@Query() dto: ListWebtoonAuthorsDto, @Req() req) {
    const timezone = (req.headers.timezone as string) || 'Asia/Bangkok';
    return this.aiWebtoonPrepareCharacterService.list(dto, timezone);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('/:id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateAiWebtoonPrepareCharacterDto,
  ) {
    return this.aiWebtoonPrepareCharacterService.update(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post()
  create(@Body() body: CreateAiWebtoonPrepareCharacterDto, @Req() req) {
    return this.aiWebtoonPrepareCharacterService.create(body, req.user.id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Delete('/:id')
  delete(@Param('id', ParseIntPipe) id: number, @Req() req) {
    return this.aiWebtoonPrepareCharacterService.delete(id, req.user.id);
  }
}
