import { Entity, Column, <PERSON>To<PERSON>ne, Join<PERSON>olumn, Index } from 'typeorm';
import { AdminEntity } from './admin.entity';
import { DefaultEntity } from './default.entity';

export enum LegalDocumentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum LegalDocumentType {
  TERMS_OF_USE = 'terms_of_use',
  PRIVACY_POLICY = 'privacy_policy',
  COOKIE_POLICY = 'cookie_policy',
}

@Entity('legal_document')
@Index(['status', 'type'])
@Index(['createdAt'])
@Index(['version'])
export class LegalDocumentEntity extends DefaultEntity {
  @Column({ length: 255 })
  title: string;

  @Column({ length: 100, unique: true })
  name: string;

  @Column({ name: 'admin_id' })
  adminId: number;

  @Column({ name: 'view_count', default: 0 })
  viewCount: number;

  @Column({
    type: 'enum',
    enum: LegalDocumentStatus,
    default: LegalDocumentStatus.ACTIVE,
  })
  status: LegalDocumentStatus;

  @Column({ type: 'text' })
  content: string;

  @Column({
    type: 'enum',
    enum: LegalDocumentType,
  })
  type: LegalDocumentType;

  @Column({ length: 50, default: '1.0' })
  version: string;

  // Relations
  @ManyToOne(() => AdminEntity)
  @JoinColumn({ name: 'admin_id' })
  admin: AdminEntity;
}
