import {
  Controller,
  Post,
  Get,
  Param,
  Body,
  UseGuards,
  Request,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { SupportService } from './services/support.service';
import { CreateQADto } from './dtos/create-qa.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SupportCategoryRepository } from '../../database/repositories/support-category.repository';

@ApiTags('Q&A')
@Controller('qa')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class QAController {
  constructor(
    private readonly supportService: SupportService,
    private readonly supportCategoryRepository: SupportCategoryRepository,
  ) {}

  @Post()
  @ApiOperation({
    summary: 'Create Q&A Question',
    description:
      'Create a new Q&A question with auto-reply if enabled (maps to POST /qa from require.md)',
  })
  @ApiResponse({
    status: 201,
    description: 'Question created successfully',
    type: Boolean,
    example: true,
  })
  @ApiResponse({
    status: 400,
    description: 'Category not found or validation error',
    example: {
      error: 'qa/category-not-found',
      message: 'Category not found',
    },
  })
  async createQuestion(
    @Body() createDto: CreateQADto,
    @Request() req: any,
  ): Promise<boolean> {
    // Map Q&A create to Support ticket creation
    const supportTicketDto = {
      subject: createDto.title,
      description: createDto.description,
      attachments: createDto.files || [],
      priority: 'MEDIUM' as any, // Default priority for Q&A
      categoryId: createDto.customerServiceCategoryId,
    };

    const result = await this.supportService.createSupportTicket(
      supportTicketDto,
      req.user.id,
    );

    // Return boolean as required by Q&A spec
    return !!result.success;
  }

  @Get()
  @ApiQuery({
    name: 'isRead',
    required: false,
    type: String,
    description:
      'Filter by read status: "true" for read questions, "false" for unread questions. If not provided, returns all questions.',
    example: 'false',
  })
  @ApiOperation({
    summary: 'Get Q&A Questions List',
    description:
      'Get list of user Q&A questions with completed responses only. Filter by isRead status: true for read questions, false for unread questions (maps to GET /qa from require.md)',
  })
  @ApiResponse({
    status: 200,
    description: 'Questions list retrieved successfully',
    example: [
      {
        id: 123,
        userId: 456,
        title: 'Payment issue with subscription',
        description: 'I was charged twice for my monthly subscription',
        files: ['https://example.com/receipt.png'],
        isRead: false,
        responseCheckTime: null,
        createdAt: '2025-01-22T09:00:00.000Z',
        updatedAt: '2025-01-22T10:30:00.000Z',
        deletedAt: null,
        res: [
          {
            id: 789,
            status: 'complete',
            response: 'We have processed your refund request...',
            files: [],
            responseCompletedTime: '2025-01-22T10:30:00.000Z',
            createdAt: '2025-01-22T10:30:00.000Z',
            admin: {
              id: 2,
              username: 'support_admin',
            },
          },
        ],
      },
    ],
  })
  async getQuestions(@Request() req: any, @Query('isRead') isRead?: string) {
    // Build filters object
    const filters: any = {};
    if (isRead !== undefined) {
      filters.isRead = isRead === 'true';
    }

    const tickets = await this.supportService.getUserTickets(
      req.user.id,
      1,
      1000, // Get all tickets for Q&A list format
      Object.keys(filters).length > 0 ? filters : undefined,
    );

    // Transform support tickets to Q&A format
    return tickets.data.map((ticket: any) => ({
      id: ticket.id,
      userId: req.user.id,
      title: ticket.subject,
      description: ticket.description || '',
      files: ticket.attachments || [],
      isRead: ticket.isRead || false,
      responseCheckTime: ticket.responseCheckTime || null,
      createdAt: ticket.createdAt,
      updatedAt: ticket.updatedAt || ticket.createdAt,
      deletedAt: ticket.deletedAt || null,
      res: ticket.responses,
    }));
  }

  @Get('check-answer/:id')
  @ApiOperation({
    summary: 'Mark Q&A Answer as Read',
    description:
      'Mark Q&A answer as read by user (maps to GET /qa/check-answer/:id from require.md)',
  })
  @ApiResponse({
    status: 200,
    description: 'Answer marked as read successfully',
    type: Boolean,
    example: true,
  })
  @ApiResponse({
    status: 400,
    description: 'No completed response available',
    example: {
      error: 'qa/not-response',
      message: 'No completed response available',
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Question not found or access denied',
    example: {
      error: 'qa/check-answer-failed',
      message: 'Question not found or access denied',
    },
  })
  async checkAnswer(
    @Param('id', ParseIntPipe) questionId: number,
    @Request() req: any,
  ): Promise<boolean> {
    try {
      const success = await this.supportService.checkTicketResponse(
        questionId,
        req.user.id,
      );
      return success;
    } catch (error) {
      // Map support errors to Q&A errors
      if (error.message?.includes('not-found')) {
        throw new Error('qa/check-answer-failed');
      }
      if (error.message?.includes('no-response')) {
        throw new Error('qa/not-response');
      }
      throw error;
    }
  }

  /**
   * Map support response status to Q&A status format
   */
  private mapResponseStatusToQA(supportStatus: string): string {
    const statusMap = {
      unanswered: 'unanswered',
      pending: 'pending',
      processing: 'processing',
      complete: 'complete',
      complete_add: 'complete_add',
      automatedReply: 'automated_reply',
    };

    return statusMap[supportStatus] || 'complete';
  }

  @Get('categories')
  @ApiOperation({
    summary: 'Get Support Categories',
    description: 'Get list of active support categories for Q&A questions',
  })
  @ApiResponse({
    status: 200,
    description: 'Support categories retrieved successfully',
    example: {
      data: [
        {
          id: 1,
          name: 'payment-billing',
          title: 'Payment & Billing',
          description: 'Issues related to payments, subscriptions, and billing',
        },
        {
          id: 2,
          name: 'content-access',
          title: 'Content Access Issues',
          description: 'Problems accessing premium content or episodes',
        },
        {
          id: 3,
          name: 'technical-support',
          title: 'Technical Support',
          description: 'Technical issues with the platform',
        },
      ],
    },
  })
  async getSupportCategories() {
    const categories =
      await this.supportCategoryRepository.findActiveCategories();

    return {
      data: categories.map((category) => ({
        id: category.id,
        name: category.name,
        title: category.title,
        description: category.description,
      })),
    };
  }
}
