import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Request,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiQuery,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAdminAuthGuard } from '../../admin/auth-admin/guards/jwt-admin-auth.guard';
import { MenuPermissionGuard } from '../../admin/auth-admin/guards/menu-permission.guard';
import { MenuPermissions } from '../../admin/auth-admin/decorators/menu-permission.decorator';
import { UploadService } from '../shared/services/upload.service';
import {
  UploaderType,
  UploadContext,
} from '../../../database/entities/upload.entity';
import {
  AdminFileUploadDto,
  AdminBulkUploadDto,
} from './dtos/admin-upload.dto';

@Controller('admin/upload')
@UseGuards(JwtAdminAuthGuard, MenuPermissionGuard)
@ApiTags('Admin Upload')
@ApiBearerAuth()
export class AdminUploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post('content-image')
  @MenuPermissions('content-management')
  @UseInterceptors(
    FileInterceptor('image', {
      limits: { fileSize: 100 * 1024 * 1024 }, // 100MB
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Upload Content Image',
    description:
      'Upload image for content management (PNG/JPEG/GIF/WebP, max 100MB)',
  })
  @ApiResponse({
    status: 201,
    description: 'Content image uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        uploadId: { type: 'number', example: 123 },
        filePath: {
          type: 'string',
          example: '/uploads/admin/content/image_123456.png',
        },
        thumbnailPath: {
          type: 'string',
          example: '/uploads/thumbnails/admin/content/thumb_image_123456.png',
        },
        publicUrl: {
          type: 'string',
          example: 'https://example.com/uploads/admin/content/image_123456.png',
        },
      },
    },
  })
  async uploadContentImage(
    @UploadedFile() file: any,
    @Request() req: any,
    @Body() dto: AdminFileUploadDto,
  ) {
    return this.uploadService.uploadFile(
      file,
      req.user.id,
      UploaderType.ADMIN,
      UploadContext.CONTENT,
      dto.description,
    );
  }

  @Post('setting-image')
  @MenuPermissions('setting-management')
  @UseInterceptors(
    FileInterceptor('image', {
      limits: { fileSize: 20 * 1024 * 1024 }, // 20MB
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Upload Setting Image',
    description: 'Upload image for platform settings (logos, banners, etc.)',
  })
  @ApiResponse({
    status: 201,
    description: 'Setting image uploaded successfully',
  })
  async uploadSettingImage(
    @UploadedFile() file: any,
    @Request() req: any,
    @Body() dto: AdminFileUploadDto,
  ) {
    return this.uploadService.uploadFile(
      file,
      req.user.id,
      UploaderType.ADMIN,
      UploadContext.SETTINGS,
      dto.description,
    );
  }

  @Post('bulk-upload')
  @MenuPermissions('content-management')
  @UseInterceptors(
    FileInterceptor('archive', {
      limits: { fileSize: 500 * 1024 * 1024 }, // 500MB
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Bulk Upload Archive',
    description:
      'Upload ZIP/RAR archive for bulk content processing (max 500MB)',
  })
  @ApiResponse({
    status: 201,
    description: 'Bulk upload processed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        uploadId: { type: 'number', example: 123 },
        filePath: { type: 'string' },
        publicUrl: { type: 'string' },
        message: {
          type: 'string',
          example:
            'Archive uploaded successfully. Processing will begin shortly.',
        },
      },
    },
  })
  async bulkUpload(
    @UploadedFile() file: any,
    @Request() req: any,
    @Body() dto: AdminBulkUploadDto,
  ) {
    const result = await this.uploadService.uploadFile(
      file,
      req.user.id,
      UploaderType.ADMIN,
      UploadContext.BULK,
      dto.description,
    );

    // TODO: Add bulk processing logic here
    // For now, just upload the archive file
    return {
      ...result,
      message: 'Archive uploaded successfully. Processing will begin shortly.',
    };
  }

  @Get('admin-files')
  @MenuPermissions('content-management')
  @ApiOperation({
    summary: 'Get Admin Files',
    description: 'Get list of files uploaded by admins',
  })
  @ApiQuery({ name: 'context', required: false, enum: UploadContext })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 20 })
  @ApiResponse({
    status: 200,
    description: 'Admin files retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              uploaderId: { type: 'number' },
              originalName: { type: 'string' },
              fileName: { type: 'string' },
              fileSize: { type: 'number' },
              mimeType: { type: 'string' },
              fileType: { type: 'string' },
              context: { type: 'string' },
              description: { type: 'string' },
              publicUrl: { type: 'string' },
              thumbnailUrl: { type: 'string' },
              createdAt: { type: 'string' },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  async getAdminFiles(
    @Query('context') context?: UploadContext,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.uploadService.getAdminFiles({
      context,
      page: page || 1,
      limit: limit || 20,
    });
  }

  @Delete(':id')
  @MenuPermissions('content-management')
  @ApiOperation({
    summary: 'Delete Admin File',
    description: 'Delete a file uploaded by admin',
  })
  @ApiResponse({
    status: 200,
    description: 'File deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'File deleted successfully' },
      },
    },
  })
  async deleteFile(@Param('id', ParseIntPipe) uploadId: number) {
    const success = await this.uploadService.deleteAdminFile(uploadId);

    if (!success) {
      return {
        success: false,
        message: 'File not found or cannot be deleted',
      };
    }

    return {
      success: true,
      message: 'File deleted successfully',
    };
  }

  // Compatibility endpoint - to replace existing uploadImage in content-management
  @Post('image')
  @MenuPermissions('content-management')
  @UseInterceptors(
    FileInterceptor('image', {
      limits: { fileSize: 100 * 1024 * 1024 }, // 100MB
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Upload Image (Legacy)',
    description:
      'Legacy endpoint for backward compatibility with existing content management',
  })
  async uploadImage(
    @UploadedFile() file: any,
    @Request() req: any,
    @Body() dto?: { description?: string },
  ) {
    const result = await this.uploadService.uploadFile(
      file,
      req.user.id,
      UploaderType.ADMIN,
      UploadContext.CONTENT,
      dto?.description,
    );

    // Return in format expected by existing code
    return result.publicUrl;
  }
}
