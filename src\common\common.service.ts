import { BadRequestException, Injectable } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { MESSAGE_CONFIG } from './message.config';
import { Request } from 'express';
import { MailerService } from '@nestjs-modules/mailer';
@Injectable()
export class CommonService {
  constructor(private readonly mailerService: MailerService) {}

  uploadImageToStorage(file: any, folder: string) {
    try {
      const folderImage = `uploads/${folder}`;
      const uploadsDir = path.join(__dirname, '../../', folderImage);
      //check extension file
      const extension = path.extname(file.originalname);
      if (
        extension !== '.png' &&
        extension !== '.jpg' &&
        extension !== '.jpeg'
      ) {
        throw new BadRequestException(MESSAGE_CONFIG.INVALID_FILE_EXTENSION);
      }

      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }

      const filePath = `/${file.originalname.replace(extension, '')}_${Date.now()}${extension}`;
      fs.writeFileSync(uploadsDir + filePath, file.buffer);

      return folderImage + filePath;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  removeImageFromStorage(filePath: string) {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  // detect platform web pc or web mobile retrun pc or mobile
  detectPlatform(req: Request) {
    const userAgent = req.headers['user-agent'];
    if (userAgent && userAgent.includes('Android')) {
      return { platform: 'mobile', userAgent: userAgent };
    }
    if (userAgent && userAgent.includes('iPhone')) {
      return { platform: 'mobile', userAgent: userAgent };
    }
    return { platform: 'pc', userAgent: userAgent };
  }

  detectBrowser(userAgent: string) {
    if (userAgent && userAgent.includes('Chrome')) {
      return 'chrome';
    }
    if (userAgent && userAgent.includes('Firefox')) {
      return 'firefox';
    }
    if (userAgent && userAgent.includes('Safari')) {
      return 'safari';
    }
    if (userAgent && userAgent.includes('Edge')) {
      return 'edge';
    }
    return 'unknown';
  }

  detectOs(userAgent: string) {
    if (userAgent && userAgent.includes('Windows')) {
      return 'windows';
    }
    if (userAgent && userAgent.includes('Macintosh')) {
      return 'macos';
    }
    if (userAgent && userAgent.includes('Linux')) {
      return 'linux';
    }
    if (userAgent && userAgent.includes('Android')) {
      return 'android';
    }
    if (userAgent && userAgent.includes('iOS')) {
      return 'ios';
    }
    if (userAgent && userAgent.includes('Chrome OS')) {
      return 'chromeos';
    }
    if (userAgent && userAgent.includes('FreeBSD')) {
      return 'freebsd';
    }
    return 'unknown';
  }

  async sendEmail(to: string, subject: string, template: string, context: any) {
    return await this.mailerService.sendMail({
      to,
      subject,
      template,
      context,
    });
  }
}
