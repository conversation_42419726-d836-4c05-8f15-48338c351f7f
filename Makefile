deploy:
	rsync -vazh --delete --exclude .git \
	--exclude .vscode \
	--exclude .idea \
	--exclude .env \
	--exclude node_modules \
	--exclude dist \
	--exclude uploads \
	. sotatek@211.110.64.88:/home/<USER>/trunk/backend/
	ssh sotatek@211.110.64.88 "cd /home/<USER>/trunk/backend/ && docker compose -f docker-compose-dev.yml up --build -d"
deploy-stg:
	rsync -vazh --delete --exclude .git \
	--exclude .vscode \
	--exclude .idea \
	--exclude .env \
	--exclude node_modules \
	--exclude dist \
	--exclude uploads \
	. sotatek@211.110.64.88:/home/<USER>/trunk/backend-stg/
	ssh sotatek@211.110.64.88 "cd /home/<USER>/trunk/backend-stg/ && docker compose -f docker-compose-stg.yml up --build -d"
deploy-prod:
	rsync -vazh --delete --exclude .git \
	--exclude .vscode \
	--exclude .idea \
	--exclude .env \
	--exclude node_modules \
	--exclude dist \
	--exclude uploads \
	. sotatek@211.110.64.88:/home/<USER>/trunk/backend-prod/
	ssh sotatek@211.110.64.88 "cd /home/<USER>/trunk/backend-prod/ && docker compose -f docker-compose-prod.yml up --build -d"

deploy-image:
	rsync -vazh --delete --exclude .git \
	--exclude .vscode \
	--exclude .idea \
	--exclude .env \
	--exclude node_modules \
	--exclude dist \
	--exclude uploads \
	. sotatek@211.110.64.89:/home/<USER>/trunk/backend/
	ssh sotatek@211.110.64.89 "cd /home/<USER>/trunk/backend/ && docker compose -f docker-compose-dev.yml up --build -d"