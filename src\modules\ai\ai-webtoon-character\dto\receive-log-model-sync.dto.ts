import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsInt,
  IsString,
  IsUrl,
  Min,
  ValidateNested,
} from 'class-validator';

export class SampleLogModelItem {
  @ApiProperty()
  @IsString()
  imgUrl: string;

  @ApiProperty()
  @IsString()
  prompt: string;
}

export class ReceiveLogModelSyncSampleDto {
  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  characterId: number;

  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  epoch: number;

  @ApiProperty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SampleLogModelItem)
  samples: SampleLogModelItem[];
}

export class UrlModelLogItem {
  @ApiProperty()
  @IsUrl()
  modelUrl: string;

  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  epoch: number;
}

export class ReceiveLogModelSyncUrlModelDto {
  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  characterId: number;

  @ApiProperty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UrlModelLogItem)
  data: UrlModelLogItem[];
}
