import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsBoolean,
  IsArray,
  IsUrl,
  IsEnum,
  MaxLength,
} from 'class-validator';
import { TicketStatus } from '../../../../database/entities/support-ticket.entity';

export class AdminRespondTicketDto {
  @ApiProperty({
    example:
      'Thank you for contacting us. We have reviewed your account and found the issue...',
    description: 'Admin response content',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(3000)
  response: string;

  @ApiProperty({
    example: ['https://example.com/solution-guide.pdf'],
    description: 'Response attachment URLs',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  attachments?: string[];

  @ApiProperty({
    example: false,
    description: 'Is this response internal (not visible to user)',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isInternal?: boolean;

  @ApiProperty({
    enum: TicketStatus,
    example: TicketStatus.IN_PROGRESS,
    description: 'Change ticket status after response',
    required: false,
  })
  @IsOptional()
  @IsEnum(TicketStatus)
  changeStatus?: TicketStatus;
}

export class AssignTicketDto {
  @ApiProperty({
    example: 2,
    description: 'Admin ID to assign ticket to',
  })
  @IsNotEmpty()
  adminId: number;
}

export class CloseTicketDto {
  @ApiProperty({
    example:
      'Issue resolved by updating user subscription settings. No further action needed.',
    description: 'Final resolution notes',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  resolutionNotes?: string;

  @ApiProperty({
    example: true,
    description: 'Send closure notification to user',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  sendNotification?: boolean;
}
