import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { SupportCategoryEntity } from '../../database/entities/support-category.entity';
import { SupportTicketEntity } from '../../database/entities/support-ticket.entity';
import { SupportResponseEntity } from '../../database/entities/support-response.entity';
import { UserEntity } from '../../database/entities/user.entity';
import { AdminEntity } from '../../database/entities/admin.entity';
import { SettingEntity } from '../../database/entities/setting.entity';
import { SettingMetadata } from '../../database/entities/setting_metadata.entity';

// Repositories
import { SupportCategoryRepository } from '../../database/repositories/support-category.repository';
import { SupportTicketRepository } from '../../database/repositories/support-ticket.repository';
import { SupportResponseRepository } from '../../database/repositories/support-response.repository';
import { UserRepository } from '../../database/repositories/user.repository';
import { AdminRepository } from '../../database/repositories/admin.repository';
import { SettingRepository } from '../../database/repositories/setting.repository';
import { SettingMetadataRepository } from '../../database/repositories/setting-metadat.repository';

// Services
import { SupportService } from './services/support.service';
import { SupportEmailService } from './services/support-email.service';
import { QAAutoReplyService } from './services/qa-auto-reply.service';

// Controllers
// import { SupportController } from './support.controller';
import { QAController } from './qa.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SupportCategoryEntity,
      SupportTicketEntity,
      SupportResponseEntity,
      UserEntity,
      AdminEntity,
      SettingEntity,
      SettingMetadata,
    ]),
  ],
  providers: [
    // Repositories
    SupportCategoryRepository,
    SupportTicketRepository,
    SupportResponseRepository,
    UserRepository,
    AdminRepository,
    SettingRepository,
    SettingMetadataRepository,

    // Services
    SupportService,
    SupportEmailService,
    QAAutoReplyService,
  ],
  controllers: [QAController],
  exports: [
    SupportService,
    SupportEmailService,
    QAAutoReplyService,
    SupportCategoryRepository,
    SupportTicketRepository,
    SupportResponseRepository,
  ],
})
export class SupportModule {}
