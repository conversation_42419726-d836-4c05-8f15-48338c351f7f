import { Is<PERSON>ptional, <PERSON>Enum, IsString, IsInt, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  LegalDocumentStatus,
  LegalDocumentType,
} from '../../../../database/entities/legal-document.entity';

export class LegalDocumentFilterDto {
  @ApiProperty({ enum: LegalDocumentStatus, required: false })
  @IsOptional()
  @IsEnum(LegalDocumentStatus)
  status?: LegalDocumentStatus;

  @ApiProperty({ enum: LegalDocumentType, required: false })
  @IsOptional()
  @IsEnum(LegalDocumentType)
  type?: LegalDocumentType;

  @ApiProperty({ description: 'Search in title and content', required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ default: 1, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ default: 20, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 20;
}
