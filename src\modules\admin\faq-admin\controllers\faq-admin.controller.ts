import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAdminAuthGuard } from '../../auth-admin/guards/jwt-admin-auth.guard';
import { FaqAdminService } from '../services/faq-admin.service';
import { CreateFaqDto } from '../dtos/create-faq.dto';
import { UpdateFaqDto } from '../dtos/update-faq.dto';
import { FaqListQueryDto } from '../dtos/faq-list-query.dto';
import { DeleteFaqDto } from '../dtos/delete-faq.dto';

@ApiTags('Admin - FAQ Management')
@ApiBearerAuth()
@Controller('admin/faq')
@UseGuards(JwtAdminAuthGuard)
export class FaqAdminController {
  constructor(private readonly faqAdminService: FaqAdminService) {}

  @Post()
  @ApiOperation({ summary: 'Create new FAQ' })
  @ApiResponse({ status: 201, description: 'FAQ created successfully' })
  async createFaq(@Body() createFaqDto: CreateFaqDto, @Req() req) {
    return this.faqAdminService.createFaq(createFaqDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get FAQ list with filters and pagination' })
  @ApiResponse({ status: 200, description: 'FAQ list retrieved successfully' })
  async getFaqList(@Query() query: FaqListQueryDto) {
    return this.faqAdminService.getFaqList(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get FAQ detail by ID' })
  @ApiResponse({
    status: 200,
    description: 'FAQ detail retrieved successfully',
  })
  async getFaqDetail(@Param('id', ParseIntPipe) id: number) {
    return this.faqAdminService.getFaqDetail(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update FAQ' })
  @ApiResponse({ status: 200, description: 'FAQ updated successfully' })
  async updateFaq(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateFaqDto: UpdateFaqDto,
  ) {
    return this.faqAdminService.updateFaq(id, updateFaqDto);
  }

  @Delete()
  @ApiOperation({ summary: 'Soft delete multiple FAQs' })
  @ApiResponse({ status: 200, description: 'FAQs deleted successfully' })
  async deleteFaqs(@Body() deleteFaqDto: DeleteFaqDto) {
    return this.faqAdminService.deleteFaqs(deleteFaqDto.ids);
  }
}
