import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AiApiGenStateManagementModule } from '../ai-api-gen-state-management/ai-api-gen-state-management.module';
import { AiServiceModule } from '../ai-service/ai-service.module';
import { NotificationGoogleModule } from 'src/modules/notification-google/notification-google.module';
// import { SocketModule } from '../socket/socket.module';
import { AiWebtoonPrepareCharacterRepository } from 'src/database/repositories/ai-webtoon-prepare-character.repository';
import { AiWebtoonCharacterRepository } from 'src/database/repositories/ai-webtoon-character.repository';
import { AiWebtoonPrepareCharacterGeneratedRepository } from 'src/database/repositories/ai-webtoon-prepare-character-generated.repository';
import { AiWebtoonCharacterModule } from '../ai-webtoon-character/ai-webtoon-character.module';
import { AiWebtoonPrepareCharacterController } from './ai-webtoon-prepare-character.controller';
import { AiWebtoonPrepareCharacterService } from './ai-webtoon-prepare-character.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AiWebtoonPrepareCharacterRepository,
      AiWebtoonCharacterRepository,
      AiWebtoonPrepareCharacterGeneratedRepository,
    ]),
    AiServiceModule,
    AiApiGenStateManagementModule,
    NotificationGoogleModule,
    AiWebtoonCharacterModule,
    // SocketModule,
  ],
  controllers: [AiWebtoonPrepareCharacterController],
  providers: [AiWebtoonPrepareCharacterService],
  exports: [AiWebtoonPrepareCharacterService],
})
export class AiWebtoonPrepareCharacterModule {}
