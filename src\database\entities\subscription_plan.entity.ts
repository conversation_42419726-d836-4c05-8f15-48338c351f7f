import { Column, Entity, Index, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { DefaultEntity } from './default.entity';
import { CommonStatus } from '../../common/status.enum';
import { SubscriptionEntity } from './subscription.entity';

@Entity('subscription_plans')
@Index(['status'])
export class SubscriptionPlanEntity extends DefaultEntity {
  @ApiProperty({
    description: 'Name of the subscription plan',
    example: 'Premium Monthly Plan',
  })
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @ApiProperty({
    description: 'Description of the subscription plan',
    example: 'Access to premium content for one month',
  })
  @Column({ type: 'varchar', length: 255 })
  description: string;

  @ApiProperty({
    description: 'Price of the subscription plan',
    example: 29.99,
  })
  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @ApiProperty({
    description: 'Discount price of the subscription plan',
    example: 19.99,
  })
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  discountPrice: number;

  @ApiProperty({
    description: 'Is popular subscription plan',
    example: true,
  })
  @Column({ type: 'boolean', default: false })
  isPopular: boolean;

  @ApiProperty({
    description: 'Duration of the subscription plan in days',
    example: 30,
  })
  @Column({ type: 'int', default: 30 })
  durationDays: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
  })
  @Column({ type: 'varchar', length: 255 })
  currency: string;

  @ApiProperty({
    description: 'Status of the subscription plan',
    enum: CommonStatus,
  })
  @Column({ type: 'enum', enum: CommonStatus, default: CommonStatus.ACTIVE })
  status: CommonStatus;

  @ApiProperty({
    description: 'Related subscriptions',
    type: () => [SubscriptionEntity],
    required: false,
  })
  @OneToMany(
    () => SubscriptionEntity,
    (subscription) => subscription.subscriptionPlan,
  )
  subscriptions: SubscriptionEntity[];
}
