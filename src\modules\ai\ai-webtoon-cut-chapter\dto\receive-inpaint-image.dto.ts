import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>A<PERSON>y,
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import { EAiBaseModelInpaintType } from 'src/common/ai-webtoon.enum';

export class ReceiveInpaintImageDto {
  @ApiProperty()
  @IsInt()
  @Min(1)
  cutId: number;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  uuid?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  image: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  imageWidth: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  imageHeight: number;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  prompt: string;

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  characterIds: number[];

  @ApiProperty()
  @IsString()
  apiName: string;

  @ApiProperty()
  @IsEnum(EAiBaseModelInpaintType)
  baseModel: EAiBaseModelInpaintType;

  @ApiProperty()
  @IsBoolean()
  isDone: boolean;
}
