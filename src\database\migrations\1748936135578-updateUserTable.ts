import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserTable1748936135578 implements MigrationInterface {
  name = 'UpdateUserTable1748936135578';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`ref\` varchar(255) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`ref\``);
  }
}
