import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as FormData from 'form-data';
import { ExternalUploadRequest } from '../interfaces/external-upload-job.interface';

@Injectable()
export class ExternalUploadClientService {
  private readonly logger = new Logger(ExternalUploadClientService.name);
  private readonly timeout: number;

  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
  ) {
    this.timeout = this.configService.get<number>(
      'EXTERNAL_UPLOAD_TIMEOUT',
      30000,
    );
  }

  async uploadToExternal(params: ExternalUploadRequest): Promise<any> {
    const { imagePath, uploadUrl, signature, publicKey, type, metadata } =
      params;

    try {
      const fullPath = path.join(process.cwd(), imagePath.replace(/^\//, ''));

      const fileBuffer = await fs.readFile(fullPath);
      const fileName = path.basename(imagePath);
      const stats = await fs.stat(fullPath);

      // Validate buffer before sending
      if (!Buffer.isBuffer(fileBuffer) || fileBuffer.length === 0) {
        throw new Error(
          `Invalid file buffer for ${fileName}: size=${fileBuffer.length}`,
        );
      }

      // Check if file content is valid (not just null bytes)
      const firstBytes = fileBuffer.slice(0, Math.min(10, fileBuffer.length));
      const isNullBytes = firstBytes.every((byte) => byte === 0);
      if (isNullBytes && fileBuffer.length > 2) {
        this.logger.warn(
          `Warning: File ${fileName} appears to contain only null bytes`,
        );
      }

      this.logger.log(
        `Uploading file ${fileName} (${stats.size} bytes, buffer: ${fileBuffer.length} bytes) to ${uploadUrl}`,
      );

      const formData = new FormData();
      formData.append('image', fileBuffer, {
        filename: fileName,
        contentType: this.getMimeType(fileName),
      });
      formData.append('signature', signature);
      formData.append('publicKey', publicKey);
      formData.append('type', type);
      formData.append('targetPath', imagePath);

      if (metadata) {
        formData.append('metadata', JSON.stringify(metadata));
      }

      this.logger.debug(
        `Uploading ${fileName} (${stats.size} bytes) to ${uploadUrl}`,
      );

      const response = await this.httpService.axiosRef.post(
        uploadUrl,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'User-Agent': 'NestJS-Upload-Client/1.0',
          },
          timeout: this.timeout,
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
        },
      );

      this.logger.log(`Successfully uploaded ${fileName} to external service`);
      return response.data;
    } catch (error) {
      if (error.response) {
        this.logger.error(
          `External upload failed with status ${error.response.status}: ${error.response.statusText}`,
          error.response.data,
        );
        throw new Error(
          `Upload failed: ${error.response.status} - ${error.response.statusText}`,
        );
      } else if (error.request) {
        this.logger.error(
          'No response received from external service',
          error.message,
        );
        throw new Error('No response from external service');
      } else {
        this.logger.error(`Request setup error: ${error.message}`);
        throw new Error(`Request error: ${error.message}`);
      }
    }
  }

  private getMimeType(fileName: string): string {
    const ext = path.extname(fileName).toLowerCase();
    const mimeTypes: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.bmp': 'image/bmp',
      '.svg': 'image/svg+xml',
    };
    return mimeTypes[ext] || 'application/octet-stream';
  }

  async validateExternalUrl(url: string): Promise<boolean> {
    try {
      const response = await this.httpService.axiosRef.head(url, {
        timeout: 5000,
      });
      return response.status === 200;
    } catch (error) {
      this.logger.warn(
        `External URL validation failed for ${url}: ${error.message}`,
      );
      return false;
    }
  }
}
