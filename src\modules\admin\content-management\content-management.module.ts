import { Module } from '@nestjs/common';
import { ContentManagementController } from './content-management.controller';
import { ContentManagementService } from './content-management.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ContentEntity } from 'src/database/entities/content.entity';
import { AuthorEntity } from 'src/database/entities/author.entity';
import { GenreEntity } from 'src/database/entities/genre.entity';
import { ContentRepository } from 'src/database/repositories/content.repository';
import { AuthorRepository } from 'src/database/repositories/author.repository';
import { GenreRepository } from 'src/database/repositories/genre.repository';
import { SettingRepository } from 'src/database/repositories/setting.repository';
import { ContentSettingRepository } from 'src/database/repositories/content-setting.repository';
import { ContentSettingEntity } from '../../../database/entities/content_setting.entity';
import { EpisodeEntity } from '../../../database/entities/episode.entity';
import { EpisodeImageEntity } from '../../../database/entities/episode_image.entity';
import { EpisodeImageRepository } from '../../../database/repositories/episode-image.repository';
import { EpisodeRepository } from '../../../database/repositories/episode.repository';
import { UserContentRepository } from '../../../database/repositories/user-content.repository';
import { ContentValidationService } from './services/content-validation.service';
import { EpisodeImageService } from './services/episode-image.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ContentEntity,
      AuthorEntity,
      GenreEntity,
      ContentSettingEntity,
      EpisodeEntity,
      EpisodeImageEntity,
    ]),
  ],
  controllers: [ContentManagementController],
  providers: [
    ContentManagementService,
    ContentRepository,
    AuthorRepository,
    GenreRepository,
    SettingRepository,
    ContentSettingRepository,
    EpisodeRepository,
    EpisodeImageRepository,
    UserContentRepository,
    ContentValidationService,
    EpisodeImageService,
  ],
  exports: [ContentManagementService],
})
export class ContentManagementModule {}
