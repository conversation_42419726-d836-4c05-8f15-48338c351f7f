import {
  EAiWebtoonBasicStatus,
  EAiWebtoonChapterStatusGenerateImage,
} from 'src/common/ai-webtoon.enum';
import { IAiGeneralCharacter } from 'src/common/interfaces/ai-webtoon/ai-webtoon-character.interface';
import {
  AfterUp<PERSON>,
  BeforeInsert,
  Column,
  Entity,
  getManager,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { AdminEntity } from './admin.entity';
import { AiWebtoonSceneChapterEntity } from './ai-webtoon-scene-chapter.entity';
import { AiWebtoonStoryEntity } from './ai-webtoon-story.entity';
import { DefaultEntity } from './default.entity';

@Entity('ai_webtoon_chapter')
export class AiWebtoonChapterEntity extends DefaultEntity {
  @Column({ name: 'ai_webtoon_story_id' })
  aiWebtoonStoryId: number;

  @Column({ name: 'admin_id' })
  adminId: number;

  @Column({ type: 'varchar' })
  title: string;

  @Column({ type: 'decimal', precision: 20, scale: 2 })
  chapter: number;

  @Column({ type: 'varchar', nullable: true })
  file: string;

  @Column({ name: 'file_name', type: 'varchar', nullable: true })
  fileName?: string;

  @Column({
    type: 'enum',
    enum: EAiWebtoonBasicStatus,
    default: EAiWebtoonBasicStatus.NOT_GENERATED,
  })
  status: EAiWebtoonBasicStatus;

  @Column({
    type: 'enum',
    enum: EAiWebtoonChapterStatusGenerateImage,
    default: EAiWebtoonChapterStatusGenerateImage.NOT_GENERATED,
  })
  statusGenerateImage: EAiWebtoonChapterStatusGenerateImage;

  @Column({ name: 'bg_color', type: 'varchar', nullable: true })
  bgColor?: string;

  @ManyToOne(() => AdminEntity)
  @JoinColumn({ name: 'admin_id' })
  admin: AdminEntity;

  @ManyToOne(() => AiWebtoonStoryEntity)
  @JoinColumn({ name: 'ai_webtoon_story_id' })
  aiWebtoonStory: AiWebtoonStoryEntity;

  @OneToMany(
    () => AiWebtoonSceneChapterEntity,
    (aiWebtoonSceneChapter) => aiWebtoonSceneChapter.chapter,
  )
  scenes?: AiWebtoonSceneChapterEntity[];

  @BeforeInsert()
  async updateAiWebtoonStory1() {
    if (this.aiWebtoonStoryId) {
      const entityManager = getManager();
      await entityManager.update(AiWebtoonStoryEntity, this.aiWebtoonStoryId, {
        latestChapterUpdatedAt: new Date(),
      });
    }
  }

  @AfterUpdate()
  async updateAiWebtoonStory2() {
    if (this.aiWebtoonStoryId) {
      const entityManager = getManager();
      await entityManager.update(AiWebtoonStoryEntity, this.aiWebtoonStoryId, {
        latestChapterUpdatedAt: new Date(),
      });
    }
  }

  characters: IAiGeneralCharacter[];
  totalCuts: number;
  cutsGenerated: number;
  cutsGenerating: number;
  notGenerated: number;
}
