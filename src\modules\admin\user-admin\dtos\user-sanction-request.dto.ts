import {
  IsDate,
  <PERSON><PERSON>num,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON>ber,
  IsOptional,
  IsString,
} from 'class-validator';
import { SanctionType } from '../../../../common/setting.enum';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class UserSanctionRequestDto {
  @ApiProperty({ description: 'User ID', example: 1 })
  @IsNotEmpty()
  @IsNumber()
  userId: number;

  @ApiProperty({ description: 'Sanction type', example: SanctionType.PERIOD })
  @IsNotEmpty()
  @Type(() => String)
  @IsEnum(SanctionType)
  type: SanctionType;

  @ApiProperty({ description: 'Sanction reason', example: 'Reason' })
  @IsNotEmpty()
  @Type(() => String)
  @IsString()
  reason: string;

  @ApiProperty({ description: 'Admin note', example: 'Admin note' })
  @IsNotEmpty()
  @Type(() => String)
  @IsString()
  adminNote: string;

  @ApiProperty({
    description: 'Start date for period sanction',
    example: '2021-01-01',
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  startDate: Date;

  @ApiProperty({
    description: 'End date for period sanction',
    example: '2021-01-01',
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  endDate: Date;
}
