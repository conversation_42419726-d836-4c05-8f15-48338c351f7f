import { registerAs } from '@nestjs/config';

export default registerAs('imageStorage', () => ({
  // Server Configuration
  port: parseInt(process.env.PORT || '3001', 10),
  nodeEnv: process.env.NODE_ENV || 'development',

  // Storage Configuration
  storagePath: process.env.IMAGE_STORAGE_PATH || './uploads/images',
  baseUrl: process.env.IMAGE_STORAGE_BASE_URL || 'http://localhost:3001/images',
  maxImageWidth: parseInt(process.env.MAX_IMAGE_WIDTH || '2048', 10),
  maxImageHeight: parseInt(process.env.MAX_IMAGE_HEIGHT || '2048', 10),

  // File Upload Limits
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '52428800', 10), // 50MB
  allowedMimeTypes: process.env.ALLOWED_MIME_TYPES?.split(',') || [
    'image/jpeg',
    'image/png',
    'image/webp',
    'image/gif',
  ],

  // Image Processing
  imageQuality: {
    jpeg: parseInt(process.env.IMAGE_QUALITY_JPEG || '85', 10),
    webp: parseInt(process.env.IMAGE_QUALITY_WEBP || '80', 10),
    thumbnail: parseInt(process.env.THUMBNAIL_QUALITY || '85', 10),
  },

  // Security
  publicKey: process.env.IMAGE_STORAGE_PUBLIC_KEY || '',
  webhookSecret:
    process.env.IMAGE_STORAGE_WEBHOOK_SECRET || 'change-in-production',

  // Webhook Configuration
  webhook: {
    timeout: parseInt(process.env.WEBHOOK_TIMEOUT || '10000', 10),
    maxRetries: parseInt(process.env.WEBHOOK_MAX_RETRIES || '3', 10),
  },

  // Queue Configuration
  queue: {
    concurrency: parseInt(process.env.QUEUE_CONCURRENCY || '5', 10),
  },

  // Redis Configuration
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    db: parseInt(process.env.REDIS_DB || '1', 10),
  },

  // Removed database configuration - using Redis only

  // Cleanup Configuration
  cleanup: {
    oldUploadsDays: parseInt(process.env.CLEANUP_OLD_UPLOADS_DAYS || '7', 10),
    cronSchedule: process.env.CLEANUP_CRON_SCHEDULE || '0 2 * * *', // 2 AM daily
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000', 10), // 1 minute
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
  },

  // CORS Configuration
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || '*',
    credentials: process.env.CORS_CREDENTIALS === 'true',
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs/image-storage.log',
  },

  // Monitoring Configuration
  monitoring: {
    sentryDsn: process.env.SENTRY_DSN || '',
    enableMetrics: process.env.ENABLE_METRICS === 'true',
  },
}));
