import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { AiWebtoonOrderGenerateCutChapterEntity } from '../entities/ai-webtoon-order-generate-cut-chapter.entity';

@Injectable()
export class AiWebtoonOrderGenerateCutChapterRepository extends Repository<AiWebtoonOrderGenerateCutChapterEntity> {
  constructor(private dataSource: DataSource) {
    super(
      AiWebtoonOrderGenerateCutChapterEntity,
      dataSource.createEntityManager(),
    );
  }
}
