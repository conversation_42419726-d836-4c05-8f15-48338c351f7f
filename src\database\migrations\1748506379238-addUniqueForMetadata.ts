import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUniqueForMetadata1748506379238 implements MigrationInterface {
  name = 'AddUniqueForMetadata1748506379238';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`IDX_04dd9f9aebd990bec84985eb39\` ON \`setting_metadata\` (\`key\`, \`settingId\`)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_04dd9f9aebd990bec84985eb39\` ON \`setting_metadata\``,
    );
  }
}
