import {
  Controller,
  Get,
  Put,
  Delete,
  Query,
  Req,
  UseGuards,
  Body,
} from '@nestjs/common';
import { UserService } from './user.service';
import {
  ApiOperation,
  ApiTags,
  ApiBearerAuth,
  ApiQuery,
  ApiResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { UpdateProfileDto } from './dtos/update-profile.dto';
import {
  DeleteAccountDto,
  AccountDeletionRequirementsDto,
  AccountDeletionResponseDto,
} from './dtos/delete-account.dto';
import {
  CheckPasswordDto,
  CheckPasswordResponseDto,
} from './dtos/check-password.dto';
import { Post } from '@nestjs/common';
import { AnalyticsQueueService } from '../analytics/services/analytics-queue.service';
import { AccessPageDto, AccessPageResponseDto } from './dtos/access-page.dto';
import { Request } from 'express';

@ApiTags('User')
@ApiBearerAuth()
@Controller('user')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly analyticsQueueService: AnalyticsQueueService,
  ) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Find user by email' })
  async findByEmail(@Query('email') email: string) {
    return await this.userService.findByEmail(email);
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get profile' })
  async getProfile(@Req() req: any) {
    return await this.userService.getProfile(req.user.id);
  }

  @Get('payment-history')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get payment history by user id' })
  @ApiQuery({ name: 'limit', type: Number, required: false, example: 10 })
  @ApiQuery({ name: 'page', type: Number, required: false, example: 1 })
  async getPaymentHistory(
    @Req() req: any,
    @Query('limit') limit: number = 10,
    @Query('page') page: number = 1,
  ) {
    return await this.userService.getPaymentHistory(req.user.id, limit, page);
  }

  @Put('profile')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Update profile' })
  async updateProfile(
    @Req() req: any,
    @Body() updateProfileDto: UpdateProfileDto,
  ) {
    return await this.userService.updateProfile(req.user.id, updateProfileDto);
  }

  @Get('devices')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get devices' })
  @ApiQuery({ name: 'limit', type: Number, required: false, example: 10 })
  @ApiQuery({ name: 'page', type: Number, required: false, example: 1 })
  async getDevices(
    @Req() req: any,
    @Query('limit') limit: number = 10,
    @Query('page') page: number = 1,
  ) {
    return await this.userService.getDevices(req.user.id, limit, page);
  }

  @Get('account/deletion-requirements')
  @ApiOperation({
    summary: 'Get account deletion requirements',
    description:
      'Returns the current requirements for account deletion (CAPTCHA, confirmation text, etc.)',
  })
  @ApiResponse({
    status: 200,
    description: 'Account deletion requirements retrieved successfully',
    type: AccountDeletionRequirementsDto,
  })
  getAccountDeletionRequirements(): AccountDeletionRequirementsDto {
    return this.userService.getAccountDeletionRequirements();
  }

  @Delete('account')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Delete user account',
    description:
      'Permanently delete user account with all associated data. Requires password confirmation and may require additional verification based on system configuration.',
  })
  @ApiResponse({
    status: 200,
    description: 'Account deletion initiated successfully',
    type: AccountDeletionResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 401, description: 'Unauthorized - invalid JWT token' })
  @ApiResponse({
    status: 403,
    description:
      'Account deletion not allowed (active subscriptions, pending payments, etc.)',
  })
  @ApiResponse({ status: 429, description: 'Too many deletion attempts' })
  async deleteAccount(
    @Req() req: any,
    @Body() deleteAccountDto: DeleteAccountDto,
  ): Promise<AccountDeletionResponseDto> {
    const data = await this.userService.deleteAccount(
      req.user.id,
      deleteAccountDto,
    );
    await this.analyticsQueueService.trackUserActivity({
      userId: null,
      activityType: 'account_deletion',
      timestamp: Date.now(),
      language: deleteAccountDto.languageId || 0, // Convert language to number (languageId)
      metadata: {
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        ip: req.ip,
      },
    });
    return data;
  }

  @Post('check-password')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Check current user password',
    description:
      "Verify if the provided password matches the current user's password",
  })
  @ApiResponse({
    status: 200,
    description: 'Password check result',
    type: CheckPasswordResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized - invalid JWT token' })
  async checkPassword(
    @Req() req: any,
    @Body() checkPasswordDto: CheckPasswordDto,
  ): Promise<CheckPasswordResponseDto> {
    return await this.userService.checkPassword(req.user.id, checkPasswordDto);
  }

  @Post('access-page')
  @ApiOperation({
    summary: 'Track page access without authentication',
    description:
      'Track page views for analytics without requiring user authentication',
  })
  @ApiResponse({
    status: 200,
    description: 'Page access tracked successfully',
    type: AccessPageResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async trackPageAccess(
    @Body() accessPageDto: AccessPageDto,
    @Req() req: Request,
  ): Promise<AccessPageResponseDto> {
    const language =
      accessPageDto.language ||
      req.headers['accept-language']?.split(',')[0]?.substring(0, 2) ||
      'en';

    await this.analyticsQueueService.trackUserActivity({
      userId: null,
      activityType: 'access_page',
      timestamp: Date.now(),
      language: parseInt(language) || 0, // Convert language to number (languageId)
      metadata: {
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        ip: req.ip,
      },
    });

    return { success: true };
  }
}
