import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsBoolean,
  IsArray,
  IsUrl,
  MaxLength,
  IsEnum,
} from 'class-validator';
import { ResponseStatus } from 'src/database/entities/support-response.entity';

export class UpdateResponseDto {
  @ApiProperty({
    example: 'Updated response with additional information...',
    description: 'Updated response content',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(3000)
  response: string;

  @ApiProperty({
    example: ['https://example.com/updated-attachment.pdf'],
    description: 'Updated attachment URLs',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  attachments?: string[];

  @ApiProperty({
    example: false,
    description: 'Update internal status',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isInternal?: boolean;

  @ApiProperty({
    description: 'Response status after answering',
    enum: ResponseStatus,
    example: ResponseStatus.COMPLETE,
  })
  @IsEnum(ResponseStatus)
  status: ResponseStatus;
}
