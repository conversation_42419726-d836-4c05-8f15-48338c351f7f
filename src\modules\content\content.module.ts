import { Modu<PERSON> } from '@nestjs/common';
import { ContentController } from './content.controller';
import { ContentRepository } from '../../database/repositories/content.repository';
import { ContentManagementModule } from '../admin/content-management/content-management.module';
import { DisplayManagementModule } from '../admin/display-management/display-management.module';
import { AnalyticsQueueModule } from '../analytics/analytics-queue.module';

@Module({
  imports: [
    ContentManagementModule,
    DisplayManagementModule,
    AnalyticsQueueModule,
  ],
  controllers: [ContentController],
  providers: [ContentRepository],
})
export class ContentModule {}
