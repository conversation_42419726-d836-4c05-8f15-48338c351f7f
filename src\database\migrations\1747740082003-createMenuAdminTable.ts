import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateMenuAdminTable1747740082003 implements MigrationInterface {
  name = 'CreateMenuAdminTable1747740082003';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`menu_admin\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active', \`adminId\` int NULL, \`menuId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`menu_admin\` ADD CONSTRAINT \`FK_16d15e6d339680ac759e07108c6\` FOREIGN KEY (\`adminId\`) REFERENCES \`admins\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`menu_admin\` ADD CONSTRAINT \`FK_6a26f7a7f40a068b9b326e466c9\` FOREIGN KEY (\`menuId\`) REFERENCES \`menu\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`menu_admin\` DROP FOREIGN KEY \`FK_6a26f7a7f40a068b9b326e466c9\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`menu_admin\` DROP FOREIGN KEY \`FK_16d15e6d339680ac759e07108c6\``,
    );
    await queryRunner.query(`DROP TABLE \`menu_admin\``);
  }
}
