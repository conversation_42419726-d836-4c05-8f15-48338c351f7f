import { Entity, Column, OneTo<PERSON>any, Unique } from 'typeorm';
import { Exclude } from 'class-transformer';
import { DefaultEntity } from './default.entity';
import { UserStatus } from 'src/common/status.enum';
import { Gender, Platform } from '../../common/common.config';
import { SanctionEntity } from './sanction.entity';
import { UserContentEntity } from './user_content.entity';
import { SessionEntity } from './session.entity';
import { SubscriptionEntity } from './subscription.entity';
import { PaymentEntity } from './payment.entity';
@Entity('users')
@Unique(['email', 'deletedAt'])
export class UserEntity extends DefaultEntity {
  @Column()
  email: string;

  @Exclude()
  @Column()
  password: string;

  @Column({ type: 'enum', enum: UserStatus, default: UserStatus.ACTIVE })
  status: UserStatus;

  @Column({ nullable: true })
  @Exclude()
  forgotPasswordToken: string;

  @Column({ nullable: true })
  @Exclude()
  forgotPasswordTokenExpiresAt: Date;

  @Column({ nullable: true })
  signUpIp: string;

  @Column({ nullable: true })
  verificationIp: string;

  @Column({ nullable: true, default: 0 })
  totalPurchases: number;

  @Column({
    type: 'enum',
    enum: Platform,
    nullable: true,
    default: Platform.PC,
  })
  platform: Platform;

  @Column({ nullable: true })
  ref: string;

  @Column({
    nullable: true,
    type: 'enum',
    enum: Object.values(Gender),
    default: Gender.OTHER,
  })
  gender: string;

  @Column({ nullable: true, default: false })
  agreeUseInfomation: boolean;

  @Column({ nullable: true, type: 'bigint' })
  agreeUseInfomationAt: number;

  @Column({ nullable: true, default: false })
  isMatureContent: boolean;

  @Column({ type: 'boolean', default: false })
  isAdultVerified: boolean;

  @OneToMany(() => SanctionEntity, (sanction) => sanction.user, {
    nullable: true,
  })
  sanctions: SanctionEntity[];

  @OneToMany(() => UserContentEntity, (userContent) => userContent.user, {
    nullable: true,
  })
  userContents: UserContentEntity[];

  @OneToMany(() => SessionEntity, (session) => session.user, {
    nullable: true,
  })
  sessions: SessionEntity[];

  @OneToMany(() => SubscriptionEntity, (subscription) => subscription.user, {
    nullable: true,
  })
  subscriptions: SubscriptionEntity[];

  @OneToMany(() => PaymentEntity, (payment) => payment.user, {
    nullable: true,
  })
  payments: PaymentEntity[];
}
