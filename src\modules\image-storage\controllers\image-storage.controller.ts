import {
  Controller,
  Post,
  Body,
  UploadedFile,
  UseInterceptors,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { ImageStorageService } from '../services/image-storage.service';
import { UploadImageDto } from '../dto/upload-image.dto';
import {
  UploadImageResponseDto,
  ErrorResponseDto,
} from '../dto/upload-response.dto';

@ApiTags('Image Storage')
@Controller('/storage/image')
export class ImageStorageController {
  private readonly logger = new Logger(ImageStorageController.name);

  constructor(private readonly imageStorageService: ImageStorageService) {}

  @Post('upload')
  @ApiOperation({
    summary: 'Upload image with signature verification',
    description:
      'Upload an image file with signature verification for security. Returns early ACK (202) for async processing.',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Image upload with metadata',
    type: UploadImageDto,
  })
  @ApiResponse({
    status: 202,
    description: 'Upload accepted and queued for processing',
    type: UploadImageResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request data or signature verification failed',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: 413,
    description: 'File too large (max 50MB)',
    type: ErrorResponseDto,
  })
  @UseInterceptors(FileInterceptor('image'))
  async uploadImage(
    @UploadedFile() file: any,
    @Body() uploadData: UploadImageDto,
  ): Promise<UploadImageResponseDto> {
    try {
      this.logger.log(`Starting image upload process`);

      // Debug file info
      console.log('=== FILE INFO DEBUG ===');
      console.log('File object:', {
        fieldname: file?.fieldname,
        originalname: file?.originalname,
        encoding: file?.encoding,
        mimetype: file?.mimetype,
        size: file?.size,
        buffer: file?.buffer
          ? `Buffer(${file.buffer.length} bytes)`
          : 'No buffer',
      });

      if (file?.buffer) {
        console.log('Buffer details:');
        console.log('- Is Buffer?', Buffer.isBuffer(file.buffer));
        console.log('- Buffer length:', file.buffer.length);
        console.log(
          '- First 20 bytes:',
          file.buffer.slice(0, 20).toString('hex'),
        );

        // Check if buffer is corrupted
        const isAllZeros = file.buffer
          .slice(0, Math.min(100, file.buffer.length))
          .every((byte) => byte === 0);
        if (isAllZeros) {
          console.error('WARNING: Buffer contains only zeros!');
        }
      }
      console.log('======================');

      // Validate file presence
      if (!file) {
        throw new HttpException(
          {
            success: false,
            error: {
              code: 'MISSING_FILE',
              message: 'Image file is required',
            },
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Validate file type
      const allowedTypes = [
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/gif',
      ];
      if (!allowedTypes.includes(file.mimetype)) {
        throw new HttpException(
          {
            success: false,
            error: {
              code: 'INVALID_IMAGE_FORMAT',
              message: 'Only JPEG, PNG, WebP, and GIF formats are allowed',
              details: { received: file.mimetype, allowed: allowedTypes },
            },
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Validate file size (50MB max)
      const maxSize = 50 * 1024 * 1024; // 50MB
      if (file.size > maxSize) {
        throw new HttpException(
          {
            success: false,
            error: {
              code: 'FILE_TOO_LARGE',
              message: 'File size exceeds 50MB limit',
              details: { size: file.size, maxSize },
            },
          },
          HttpStatus.PAYLOAD_TOO_LARGE,
        );
      }

      // Process upload

      const result = await this.imageStorageService.processUpload(
        file,
        uploadData,
      );

      this.logger.log(`Upload queued successfully: ${result.uploadId}`);

      return result;
    } catch (error) {
      this.logger.error('Upload failed:', error);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        {
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Internal server error during upload processing',
            details: error.message,
          },
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
