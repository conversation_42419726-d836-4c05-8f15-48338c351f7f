import { Column, Entity, OneToMany } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { ContentEntity } from './content.entity';

@Entity('author')
export class AuthorEntity extends DefaultEntity {
  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  email: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  bankNumber: string;

  @Column({ type: 'text', nullable: true })
  memo: string;

  @OneToMany(() => ContentEntity, (content) => content.author)
  contents: ContentEntity[];
}
