import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, Min, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { ContentStatus } from '../../../common/status.enum';

export class SearchContentDto {
  @ApiProperty({ description: 'Search keyword for title', required: false })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({ description: 'Genre ID filter', required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  genreId?: number;

  @ApiProperty({ description: 'Author ID filter', required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  authorId?: number;

  @ApiProperty({
    description: 'Content status filter',
    required: false,
    enum: ContentStatus,
  })
  @IsOptional()
  @IsEnum(ContentStatus)
  status?: ContentStatus;

  @ApiProperty({ description: 'Adult content filter', required: false })
  @IsOptional()
  @Type(() => Boolean)
  isAdult?: boolean;

  @ApiProperty({ description: 'Limit per page', default: 10 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({ description: 'Page number', default: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;
}
