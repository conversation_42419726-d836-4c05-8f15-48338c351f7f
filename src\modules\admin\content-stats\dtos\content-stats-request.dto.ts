import { IsOptional, IsString, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ContentStatsRequestDto {
  @ApiProperty({
    description: 'Start date for statistics (YYYY-MM-DD)',
    example: '2025-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    description: 'End date for statistics (YYYY-MM-DD)',
    example: '2025-01-31',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({
    description:
      'Language filter - use "all" for all languages or specific language ID',
    example: 'all',
    required: false,
  })
  @IsOptional()
  @IsString()
  language?: string;
}
