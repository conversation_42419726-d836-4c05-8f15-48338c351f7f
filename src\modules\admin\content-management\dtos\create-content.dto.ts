import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
} from 'class-validator';

import { IsString } from 'class-validator';

export class CreateContentDto {
  @ApiProperty({
    description: 'Language ID',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  languageId: number;

  @ApiProperty({
    description: 'Is Adult Content',
    example: true,
  })
  @IsNotEmpty()
  @IsBoolean()
  @Type(() => Boolean)
  isAdult: boolean;

  @ApiProperty({
    description: 'Title',
    example: 'Title',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Description',
    example: 'Description',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Genre ID',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  genreId: number;

  @ApiProperty({
    description: 'Author ID',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  authorId: number;

  @ApiProperty({
    description: 'Classification IDs',
    example: [1, 2],
  })
  @IsArray()
  @IsNotEmpty()
  classificationIds: number[];

  @ApiProperty({
    description: 'Date IDs',
    example: [1, 2],
  })
  @IsArray()
  @IsNotEmpty()
  dateIds: number[];

  @ApiProperty({
    description: 'Expected Release Date',
    example: '2025-01-01',
  })
  @IsString()
  @IsOptional()
  expectedReleaseDate: string;

  @ApiProperty({
    description: 'Author Payment Format ID',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  authorPaymentFormatId: number;

  @ApiProperty({
    description: 'Image PC',
    example: 'https://example.com/image.png',
  })
  @IsString()
  @IsNotEmpty()
  imagePc: string;

  @ApiProperty({
    description: 'Image Mobile',
    example: 'https://example.com/image.png',
  })
  @IsString()
  @IsNotEmpty()
  imageMobile: string;
}
