import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddViewcountTracking1754902605557 implements MigrationInterface {
  name = 'AddViewcountTracking1754902605557';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`user_notification_recipient\` ADD \`isRead\` tinyint NOT NULL DEFAULT 0`,
    );
    await queryRunner.query(
      `ALTER TABLE \`user_analytics_v2\` ADD \`viewCount\` int NOT NULL DEFAULT '0'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`user_analytics_v2\` DROP COLUMN \`viewCount\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`user_notification_recipient\` DROP COLUMN \`isRead\``,
    );
  }
}
