import { Entity, Column, Index } from 'typeorm';
import { DefaultEntity } from './default.entity';

@Entity('content_statistics_cache')
export class ContentStatisticsCacheEntity extends DefaultEntity {
  @Column({ type: 'int', unique: true })
  contentId: number;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'datetime' })
  releaseDate: Date;

  @Column({ type: 'varchar', length: 10 })
  type: string; // 'adult' or 'general'

  @Column({ type: 'int', default: 0 })
  episodes: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  author: string;

  @Column({ type: 'int', nullable: true })
  authorId: number;

  @Column({ type: 'varchar', length: 20 })
  series: string; // 'ongoing' or 'completed'

  @Column({ type: 'varchar', length: 255, nullable: true })
  genre: string;

  @Column({ type: 'int', nullable: true })
  genreId: number;

  @Column({ type: 'bigint', default: 0 })
  freeEpisodeRead: number;

  @Column({ type: 'bigint', default: 0 })
  paidEpisodeRead: number;

  @Column({ type: 'bigint', default: 0 })
  totalEpisodeRead: number;

  @Column({ type: 'datetime' })
  @Index()
  lastUpdated: Date;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;
}
