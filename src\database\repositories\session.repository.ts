import { DataSource, Repository } from 'typeorm';
import { Injectable, NotFoundException } from '@nestjs/common';
import { MESSAGE_CONFIG } from '../../common/message.config';
import { SessionEntity } from '../entities/session.entity';
@Injectable()
export class SessionRepository extends Repository<SessionEntity> {
  constructor(private dataSource: DataSource) {
    super(SessionEntity, dataSource.createEntityManager());
  }

  async findOneById(id: number) {
    const session = await this.findOne({ where: { id } });
    if (!session) {
      throw new NotFoundException(MESSAGE_CONFIG.SESSION_NOT_FOUND);
    }
    return session;
  }
}
