import {
  EAiWebtoonCharacterGender,
  EAiWebtoonCharacterType,
} from 'src/common/ai-webtoon.enum';

export interface IAiGeneralCharacter {
  uuid: string;
  characterId: number;
  character: string;
  gender: EAiWebtoonCharacterGender;
  type: EAiWebtoonCharacterType;
}

export interface IAiItemDataTrainCharacter {
  url: string;
  prompt?: string;
}

export interface IAiItemSamplePromptCharacter {
  uuid: string;
  prompt: string;
}
