import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateContentRankingStats1752827939215
  implements MigrationInterface
{
  name = 'CreateContentRankingStats1752827939215';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`content_ranking_stats\` (\`id\` int NOT NULL AUTO_INCREMENT, \`contentId\` int NOT NULL, \`title\` varchar(255) NULL, \`author\` varchar(100) NULL, \`authorId\` int NULL, \`genre\` varchar(100) NULL, \`genreId\` int NULL, \`type\` enum ('adult', 'general') NOT NULL DEFAULT 'general', \`series\` enum ('ongoing', 'completed') NOT NULL DEFAULT 'ongoing', \`episodes\` int NOT NULL DEFAULT '0', \`periodType\` enum ('daily', 'all_time') NOT NULL, \`date\` date NULL, \`freeEpisodeRead\` int NOT NULL DEFAULT '0', \`paidEpisodeRead\` int NOT NULL DEFAULT '0', \`totalEpisodeRead\` int NOT NULL DEFAULT '0', \`totalViews\` int NOT NULL DEFAULT '0', \`uniqueViewers\` int NOT NULL DEFAULT '0', \`totalReadTime\` bigint NOT NULL DEFAULT '0', \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), INDEX \`IDX_31cfccb3031f3c8372840123dc\` (\`contentId\`, \`date\`), INDEX \`IDX_76ba80378d10473918b49e8513\` (\`periodType\`, \`date\`, \`totalEpisodeRead\`), UNIQUE INDEX \`IDX_25d798a0be0ea11b497aba86d6\` (\`contentId\`, \`periodType\`, \`date\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content_ranking_stats\` ADD CONSTRAINT \`FK_076ca96fdf996b6cf5b0a72c179\` FOREIGN KEY (\`contentId\`) REFERENCES \`content\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content_ranking_stats\` DROP FOREIGN KEY \`FK_076ca96fdf996b6cf5b0a72c179\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_25d798a0be0ea11b497aba86d6\` ON \`content_ranking_stats\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_76ba80378d10473918b49e8513\` ON \`content_ranking_stats\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_31cfccb3031f3c8372840123dc\` ON \`content_ranking_stats\``,
    );
    await queryRunner.query(`DROP TABLE \`content_ranking_stats\``);
  }
}
