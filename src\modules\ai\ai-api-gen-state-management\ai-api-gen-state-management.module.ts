import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AiApiGenStateManagementService } from './ai-api-gen-state-management.service';
import { AiApiGenStateManagementRepository } from 'src/database/repositories/ai-api-gen-state-management.repository';

@Module({
  imports: [TypeOrmModule.forFeature([AiApiGenStateManagementRepository])],
  providers: [AiApiGenStateManagementService],
  exports: [AiApiGenStateManagementService],
})
export class AiApiGenStateManagementModule {}
