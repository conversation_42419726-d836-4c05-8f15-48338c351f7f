import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job } from 'bullmq';
import { UserActivityEventDto } from '../dtos/analytics-events.dto';
import { ANALYTICS_QUEUES } from '../constants/analytics-queues.constant';
import { UserAnalyticsEntityV2 } from '../../../database/entities/user-analytics_v2.entity';

@Injectable()
@Processor(ANALYTICS_QUEUES.USER_ANALYTICS)
export class UserAnalyticsProcessor extends WorkerHost {
  private readonly logger = new Logger(UserAnalyticsProcessor.name);

  constructor(
    @InjectRepository(UserAnalyticsEntityV2)
    private userAnalyticsRepository: Repository<UserAnalyticsEntityV2>,
  ) {
    super();
  }

  async process(job: Job<UserActivityEventDto>): Promise<void> {
    const { data } = job;
    this.logger.debug(`Processing user activity: ${JSON.stringify(data)}`);

    try {
      // Lấy thời gian theo local timezone của server
      const activityDate = new Date(data.timestamp);

      // Extract date string (format: YYYY/MM/DD)
      const year = activityDate.getFullYear();
      const month = String(activityDate.getMonth() + 1).padStart(2, '0');
      const day = String(activityDate.getDate()).padStart(2, '0');
      const date = `${year}/${month}/${day}`;

      // Extract hour (0-23)
      const hour = activityDate.getHours();

      // Lấy language từ DTO (languageId - required from payment)
      const language = data.language as number;

      // Tìm hoặc tạo record analytics với date, hour, language
      let analytics = await this.userAnalyticsRepository.findOne({
        where: { date, hour, language },
      });

      if (!analytics) {
        analytics = this.userAnalyticsRepository.create({
          date,
          hour,
          language,
          activeUsers: 0,
          newRegistrations: 0,
          deletedAccounts: 0,
          loginCount: 0,
          uniqueLogins: 0,
          adultVerifiedCount: 0,
          firtPurchaseCount: 0,
          firstPurchaseAttemptCount: 0,
          firstPurchaseAmount: 0,
          rePurchaseCount: 0,
          rePurchaseAttemptCount: 0,
          rePurchaseAmount: 0,
          metadata: {},
        });
      }

      // Update metrics based on activity type
      switch (data.activityType) {
        case 'login':
          analytics.loginCount += 1;
          analytics.activeUsers += 1;
          // Update unique logins
          if (data.userId) {
            await this.updateUniqueLogins(analytics, data.userId, date, hour);
          }
          break;
        case 'register':
          analytics.newRegistrations += 1;
          analytics.activeUsers += 1;
          break;
        case 'delete':
        case 'account_deletion':
          analytics.deletedAccounts += 1;
          break;
        case 'access_page':
          analytics.viewCount = (analytics.viewCount || 0) + 1;
          break;
        case 'first_purchase_attempt':
          analytics.firstPurchaseAttemptCount += 1;
          break;
        case 'first_purchase_success': {
          analytics.firtPurchaseCount += 1;

          // Fake 1:1 conversion to KRW for simplicity
          const firstPurchaseKRW = Number(data.metadata?.amount || 0);
          analytics.firstPurchaseAmount += firstPurchaseKRW;
          break;
        }
        case 'repurchase_attempt':
          analytics.rePurchaseAttemptCount += 1;
          break;
        case 'repurchase_success': {
          analytics.rePurchaseCount += 1;

          // Fake 1:1 conversion to KRW for simplicity
          const repurchaseKRW = Number(data.metadata?.amount || 0);
          analytics.rePurchaseAmount += repurchaseKRW;
          break;
        }
      }

      await this.userAnalyticsRepository.save(analytics);

      this.logger.debug(`Updated user analytics for ${data.activityType}`);
    } catch (error) {
      this.logger.error(
        `Error processing user activity: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private async updateUniqueLogins(
    analytics: UserAnalyticsEntityV2,
    userId: number,
    date: string,
    hour: number,
  ): Promise<void> {
    // Check if user already logged in this hour
    // Tạo timestamp range cho giờ cụ thể
    const [year, month, day] = date.split('/').map(Number);
    const startHour = new Date(year, month - 1, day, hour, 0, 0, 0);
    const endHour = new Date(year, month - 1, day, hour, 59, 59, 999);

    const existingLogin = await this.userAnalyticsRepository.query(
      `
      SELECT COUNT(*) as count 
      FROM sessions s 
      WHERE s.userId = ? 
        AND s.createdAt >= ?
        AND s.createdAt <= ?
    `,
      [userId, startHour, endHour],
    );

    if (existingLogin[0].count === 0) {
      analytics.uniqueLogins += 1;
    }
  }
}
