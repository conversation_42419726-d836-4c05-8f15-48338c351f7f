import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPaymentTable1751874327055 implements MigrationInterface {
  name = 'AddPaymentTable1751874327055';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`subscription_plans\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`name\` varchar(255) NOT NULL, \`description\` varchar(255) NOT NULL, \`price\` decimal(10,2) NOT NULL, \`duration_month\` int NOT NULL, \`currency\` varchar(255) NOT NULL, \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active', INDEX \`IDX_87110746c82cc585441962630c\` (\`status\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`payments\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`amount\` decimal(10,2) NOT NULL, \`currency\` varchar(255) NOT NULL, \`status\` enum ('pending', 'success', 'failed', 'cancelled', 'expired', 'refunded') NOT NULL DEFAULT 'pending', \`method\` varchar(255) NULL, \`idempotencyKey\` varchar(255) NULL, \`providerTransactionId\` varchar(255) NULL, \`providerTransactionStatus\` varchar(255) NULL, \`subscriptionId\` int NULL, INDEX \`IDX_eabdca3774d4f3a2ca17c9d1b0\` (\`providerTransactionId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`subscriptions\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`currency\` varchar(255) NOT NULL, \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active', \`startedAt\` bigint NOT NULL, \`expiresAt\` bigint NOT NULL, \`autoRenew\` tinyint NOT NULL DEFAULT 0, \`isTrial\` tinyint NOT NULL DEFAULT 0, \`trialDays\` int NOT NULL, \`userId\` int NULL, \`subscriptionPlanId\` int NULL, INDEX \`IDX_fbdba4e2ac694cf8c9cecf4dc8\` (\`userId\`), INDEX \`IDX_df76e81987c46fdacee7c6dcdc\` (\`expiresAt\`, \`status\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payments\` ADD CONSTRAINT \`FK_2017d0cbfdbfec6b1b388e6aa08\` FOREIGN KEY (\`subscriptionId\`) REFERENCES \`subscriptions\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`subscriptions\` ADD CONSTRAINT \`FK_fbdba4e2ac694cf8c9cecf4dc84\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`subscriptions\` ADD CONSTRAINT \`FK_6edc2c85d604e65c9a135d564e1\` FOREIGN KEY (\`subscriptionPlanId\`) REFERENCES \`subscription_plans\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`subscriptions\` DROP FOREIGN KEY \`FK_6edc2c85d604e65c9a135d564e1\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`subscriptions\` DROP FOREIGN KEY \`FK_fbdba4e2ac694cf8c9cecf4dc84\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payments\` DROP FOREIGN KEY \`FK_2017d0cbfdbfec6b1b388e6aa08\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_df76e81987c46fdacee7c6dcdc\` ON \`subscriptions\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_fbdba4e2ac694cf8c9cecf4dc8\` ON \`subscriptions\``,
    );
    await queryRunner.query(`DROP TABLE \`subscriptions\``);
    await queryRunner.query(
      `DROP INDEX \`IDX_eabdca3774d4f3a2ca17c9d1b0\` ON \`payments\``,
    );
    await queryRunner.query(`DROP TABLE \`payments\``);
    await queryRunner.query(
      `DROP INDEX \`IDX_87110746c82cc585441962630c\` ON \`subscription_plans\``,
    );
    await queryRunner.query(`DROP TABLE \`subscription_plans\``);
  }
}
