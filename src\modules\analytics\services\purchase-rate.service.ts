import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserEntity } from '../../../database/entities/user.entity';
import { PaymentEntity } from '../../../database/entities/payment.entity';
import { PaymentStatus } from '../../../common/status.enum';

export interface PurchaseRateData {
  totalRegisteredUsers: number;
  firstPurchaseCount: number;
  firstPurchaseRate: number;
  repurchaseCount: number;
  repurchaseRate: number;
  totalPurchaseCount: number;
  totalPurchaseRate: number;
}

@Injectable()
export class PurchaseRateService {
  constructor(
    @InjectRepository(UserEntity)
    private userRepository: Repository<UserEntity>,
    @InjectRepository(PaymentEntity)
    private paymentRepository: Repository<PaymentEntity>,
  ) {}

  /**
   * Calculate purchase rates for a specific time period
   * @param startDate - Start date for calculation
   * @param endDate - End date for calculation
   * @returns Purchase rate data
   */
  async calculatePurchaseRates(
    startDate?: Date,
    endDate?: Date,
  ): Promise<PurchaseRateData> {
    // Get total registered users (up to endDate if specified)
    const totalRegisteredUsers = await this.getTotalRegisteredUsers(endDate);

    // Get first purchase count
    const firstPurchaseCount = await this.getFirstPurchaseCount(
      startDate,
      endDate,
    );

    // Get repurchase count
    const repurchaseCount = await this.getRepurchaseCount(startDate, endDate);

    // Calculate rates
    const firstPurchaseRate =
      totalRegisteredUsers > 0
        ? (firstPurchaseCount / totalRegisteredUsers) * 100
        : 0;

    const repurchaseRate =
      totalRegisteredUsers > 0
        ? (repurchaseCount / totalRegisteredUsers) * 100
        : 0;

    const totalPurchaseCount = firstPurchaseCount + repurchaseCount;
    const totalPurchaseRate =
      totalRegisteredUsers > 0
        ? (totalPurchaseCount / totalRegisteredUsers) * 100
        : 0;

    return {
      totalRegisteredUsers,
      firstPurchaseCount,
      firstPurchaseRate: Math.round(firstPurchaseRate * 100) / 100,
      repurchaseCount,
      repurchaseRate: Math.round(repurchaseRate * 100) / 100,
      totalPurchaseCount,
      totalPurchaseRate: Math.round(totalPurchaseRate * 100) / 100,
    };
  }

  /**
   * Get total registered users count
   * @param endDate - Count users registered up to this date
   * @returns Total registered users count
   */
  private async getTotalRegisteredUsers(endDate?: Date): Promise<number> {
    const query = this.userRepository.createQueryBuilder('user');

    if (endDate) {
      query.where('user.createdAt <= :endDate', { endDate });
    }

    return await query.getCount();
  }

  /**
   * Get first purchase count - users who made their first successful payment
   * @param startDate - Start date for payment filter
   * @param endDate - End date for payment filter
   * @returns First purchase count
   */
  private async getFirstPurchaseCount(
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    const query = this.paymentRepository
      .createQueryBuilder('payment')
      .leftJoin('payment.user', 'user')
      .where('payment.status = :status', { status: PaymentStatus.SUCCESS })
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('MIN(p.createdAt)')
          .from(PaymentEntity, 'p')
          .where('p.user = payment.user')
          .andWhere('p.status = :status', { status: PaymentStatus.SUCCESS })
          .getQuery();
        return `payment.createdAt = (${subQuery})`;
      });

    if (startDate) {
      query.andWhere('payment.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('payment.createdAt <= :endDate', { endDate });
    }

    return await query.getCount();
  }

  /**
   * Get repurchase count - users who made 2nd+ successful payments
   * @param startDate - Start date for payment filter
   * @param endDate - End date for payment filter
   * @returns Repurchase count
   */
  private async getRepurchaseCount(
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    const query = this.paymentRepository
      .createQueryBuilder('payment')
      .leftJoin('payment.user', 'user')
      .where('payment.status = :status', { status: PaymentStatus.SUCCESS })
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('MIN(p.createdAt)')
          .from(PaymentEntity, 'p')
          .where('p.user = payment.user')
          .andWhere('p.status = :status', { status: PaymentStatus.SUCCESS })
          .getQuery();
        return `payment.createdAt > (${subQuery})`;
      });

    if (startDate) {
      query.andWhere('payment.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('payment.createdAt <= :endDate', { endDate });
    }

    return await query.getCount();
  }

  /**
   * Get purchase rate for a specific user
   * @param userId - User ID to check
   * @returns Whether user has made first purchase, repurchase, or none
   */
  async getUserPurchaseStatus(userId: number): Promise<{
    hasFirstPurchase: boolean;
    hasRepurchase: boolean;
    totalPurchases: number;
  }> {
    const successfulPayments = await this.paymentRepository.count({
      where: {
        user: { id: userId },
        status: PaymentStatus.SUCCESS,
      },
      order: { createdAt: 'ASC' },
    });

    return {
      hasFirstPurchase: successfulPayments > 0,
      hasRepurchase: successfulPayments > 1,
      totalPurchases: successfulPayments,
    };
  }

  /**
   * Get real-time purchase rate for analytics dashboard
   * @returns Current purchase rate data
   */
  async getRealTimePurchaseRates(): Promise<PurchaseRateData> {
    return await this.calculatePurchaseRates();
  }

  /**
   * Get purchase rate for a specific time period (today, this week, this month)
   * @param period - Time period: 'today', 'week', 'month'
   * @returns Purchase rate data for the period
   */
  async getPurchaseRateByPeriod(
    period: 'today' | 'week' | 'month',
  ): Promise<PurchaseRateData> {
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      default:
        startDate = new Date(0); // All time
    }

    return await this.calculatePurchaseRates(startDate, now);
  }
}
