import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsNumber,
  IsIn,
  IsDateString,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class ContentStatisticsQueryDto {
  @ApiProperty({
    required: false,
    description: 'Filter by content type',
    enum: ['adult', 'general'],
  })
  @IsOptional()
  @IsIn(['adult', 'general'])
  type?: string;

  @ApiProperty({ required: false, description: 'Filter by author name' })
  @IsOptional()
  @IsString()
  author?: string;

  @ApiProperty({ required: false, description: 'Filter by genre name' })
  @IsOptional()
  @IsString()
  genre?: string;

  @ApiProperty({
    required: false,
    description: 'Filter by series status',
    enum: ['ongoing', 'completed'],
  })
  @IsOptional()
  @IsIn(['ongoing', 'completed'])
  series?: string;

  @ApiProperty({
    required: false,
    description: 'Time period for ranking (default: alltime)',
    enum: ['alltime', '7d', '30d', '90d'],
  })
  @IsOptional()
  @IsIn(['alltime', '7d', '30d', '90d'])
  period?: string = 'alltime';

  @ApiProperty({
    required: false,
    default: 20,
    description: 'Number of items per page',
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  limit?: number = 20;

  @ApiProperty({
    required: false,
    default: 0,
    description: 'Number of items to skip',
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  offset?: number = 0;

  @ApiProperty({
    required: false,
    description: 'Start date for analytics filtering (YYYY-MM-DD format)',
    example: '2025-01-01',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    required: false,
    description: 'End date for analytics filtering (YYYY-MM-DD format)',
    example: '2025-01-31',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;
}

export class ContentStatisticsResponseDto {
  @ApiProperty({ description: 'Content ID' })
  contentId: number;

  @ApiProperty({ description: 'Content title' })
  title: string;

  @ApiProperty({ description: 'Content release date' })
  releaseDate: Date;

  @ApiProperty({ description: 'Content type', enum: ['adult', 'general'] })
  type: string;

  @ApiProperty({ description: 'Total number of episodes' })
  episodes: number;

  @ApiProperty({ description: 'Author name' })
  author: string;

  @ApiProperty({ description: 'Series status', enum: ['ongoing', 'completed'] })
  series: string;

  @ApiProperty({ description: 'Genre name' })
  genre: string;

  @ApiProperty({ description: 'Total views of free episodes' })
  freeEpisodeRead: number;

  @ApiProperty({ description: 'Total views of paid episodes' })
  paidEpisodeRead: number;

  @ApiProperty({ description: 'Total views of all episodes' })
  totalEpisodeRead: number;

  @ApiProperty({ description: 'Last cache update time' })
  lastUpdated: Date;
}

export class ContentStatisticsListResponseDto {
  @ApiProperty({ type: [ContentStatisticsResponseDto] })
  data: ContentStatisticsResponseDto[];

  @ApiProperty({ description: 'Total number of items' })
  total: number;

  @ApiProperty({ description: 'Current page limit' })
  limit: number;

  @ApiProperty({ description: 'Current page offset' })
  offset: number;

  @ApiProperty({ description: 'Last cache update time' })
  lastUpdated: Date;

  @ApiProperty({ description: 'Cache age in seconds' })
  cacheAge: number;
}

export class CacheInfoResponseDto {
  @ApiProperty({ description: 'Total number of cached content items' })
  totalCachedContent: number;

  @ApiProperty({ description: 'Last cache update time' })
  lastUpdated: Date;

  @ApiProperty({ description: 'Cache age in seconds' })
  cacheAge: number;
}

export class QueueStatusResponseDto {
  @ApiProperty({ description: 'Number of waiting jobs' })
  waiting: number;

  @ApiProperty({ description: 'Number of active jobs' })
  active: number;

  @ApiProperty({ description: 'Number of completed jobs' })
  completed: number;

  @ApiProperty({ description: 'Number of failed jobs' })
  failed: number;

  @ApiProperty({ description: 'Repeatable jobs configuration' })
  repeatableJobs: any[];
}

export class DateRangeAnalyticsResponseDto {
  @ApiProperty({ description: 'Content ID' })
  contentId: number;

  @ApiProperty({ description: 'Content title' })
  title: string;

  @ApiProperty({ description: 'Author name' })
  author: string;

  @ApiProperty({ description: 'Genre name' })
  genre: string;

  @ApiProperty({ description: 'Content type', enum: ['adult', 'general'] })
  type: string;

  @ApiProperty({ description: 'Total views in date range' })
  totalViews: number;

  @ApiProperty({ description: 'Unique viewers in date range' })
  uniqueViewers: number;

  @ApiProperty({ description: 'Total read time in milliseconds' })
  totalReadTime: number;

  @ApiProperty({ description: 'Average read time per view in milliseconds' })
  averageReadTime: number;

  @ApiProperty({ description: 'Number of episodes viewed' })
  episodesViewed: number;

  @ApiProperty({ description: 'Daily breakdown of analytics' })
  dailyBreakdown: DailyAnalyticsDto[];
}

export class DailyAnalyticsDto {
  @ApiProperty({ description: 'Date' })
  date: string;

  @ApiProperty({ description: 'Views on this date' })
  views: number;

  @ApiProperty({ description: 'Unique viewers on this date' })
  uniqueViewers: number;

  @ApiProperty({ description: 'Read time on this date in milliseconds' })
  readTime: number;
}

export class DateRangeAnalyticsListResponseDto {
  @ApiProperty({ type: [DateRangeAnalyticsResponseDto] })
  data: DateRangeAnalyticsResponseDto[];

  @ApiProperty({ description: 'Total number of items' })
  total: number;

  @ApiProperty({ description: 'Current page limit' })
  limit: number;

  @ApiProperty({ description: 'Current page offset' })
  offset: number;

  @ApiProperty({ description: 'Start date of the query' })
  startDate: string;

  @ApiProperty({ description: 'End date of the query' })
  endDate: string;

  @ApiProperty({ description: 'Query execution time' })
  executedAt: Date;
}
