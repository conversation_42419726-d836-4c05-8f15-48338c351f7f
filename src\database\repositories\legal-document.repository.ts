import { Injectable } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import {
  LegalDocumentEntity,
  LegalDocumentStatus,
  LegalDocumentType,
} from '../entities/legal-document.entity';

@Injectable()
export class LegalDocumentRepository extends Repository<LegalDocumentEntity> {
  constructor(private dataSource: DataSource) {
    super(LegalDocumentEntity, dataSource.createEntityManager());
  }

  // Admin queries
  async findAdminList(
    page: number = 1,
    limit: number = 20,
    filters?: {
      status?: LegalDocumentStatus;
      type?: LegalDocumentType;
      search?: string;
    },
  ): Promise<[LegalDocumentEntity[], number]> {
    const queryBuilder = this.createQueryBuilder('legal')
      .leftJoinAndSelect('legal.admin', 'admin')
      .where('legal.deletedAt IS NULL');

    // Apply filters
    if (filters?.status) {
      queryBuilder.andWhere('legal.status = :status', {
        status: filters.status,
      });
    }

    if (filters?.type) {
      queryBuilder.andWhere('legal.type = :type', {
        type: filters.type,
      });
    }

    if (filters?.search) {
      queryBuilder.andWhere(
        '(legal.title LIKE :search OR legal.content LIKE :search)',
        {
          search: `%${filters.search}%`,
        },
      );
    }

    queryBuilder
      .orderBy('legal.createdAt', 'DESC')
      .addOrderBy('legal.version', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    return queryBuilder.getManyAndCount();
  }

  async findByIdWithAdmin(id: number): Promise<LegalDocumentEntity | null> {
    return this.createQueryBuilder('legal')
      .leftJoinAndSelect('legal.admin', 'admin')
      .where('legal.id = :id', { id })
      .andWhere('legal.deletedAt IS NULL')
      .getOne();
  }

  async softDeleteMany(ids: number[]): Promise<void> {
    await this.createQueryBuilder()
      .softDelete()
      .where('id IN (:...ids)', { ids })
      .execute();
  }

  // User queries
  async findActiveByType(
    type: LegalDocumentType,
  ): Promise<LegalDocumentEntity[]> {
    return this.createQueryBuilder('legal')
      .where('legal.status = :status', { status: LegalDocumentStatus.ACTIVE })
      .andWhere('legal.type = :type', { type })
      .andWhere('legal.deletedAt IS NULL')
      .orderBy('legal.createdAt', 'DESC')
      .addOrderBy('legal.version', 'DESC')
      .getMany();
  }

  async findActiveByName(name: string): Promise<LegalDocumentEntity | null> {
    return this.createQueryBuilder('legal')
      .where('legal.name = :name', { name })
      .andWhere('legal.status = :status', {
        status: LegalDocumentStatus.ACTIVE,
      })
      .andWhere('legal.deletedAt IS NULL')
      .getOne();
  }

  async incrementViewCount(id: number): Promise<void> {
    await this.createQueryBuilder()
      .update(LegalDocumentEntity)
      .set({ viewCount: () => 'view_count + 1' })
      .where('id = :id', { id })
      .execute();
  }

  // Versioning
  async findLatestVersion(
    type: LegalDocumentType,
  ): Promise<LegalDocumentEntity | null> {
    return this.createQueryBuilder('legal')
      .where('legal.type = :type', { type })
      .andWhere('legal.status = :status', {
        status: LegalDocumentStatus.ACTIVE,
      })
      .andWhere('legal.deletedAt IS NULL')
      .orderBy('legal.version', 'DESC')
      .addOrderBy('legal.createdAt', 'DESC')
      .getOne();
  }

  async checkNameExists(
    name: string,
    excludeId?: number,
  ): Promise<LegalDocumentEntity | null> {
    const queryBuilder = this.createQueryBuilder('legal')
      .where('legal.name = :name', { name })
      .andWhere('legal.deletedAt IS NULL');

    if (excludeId) {
      queryBuilder.andWhere('legal.id != :excludeId', { excludeId });
    }

    return queryBuilder.getOne();
  }
}
