import {
  EAiBaseModelGenImageType,
  EAiBaseModelInpaintType,
  EAiBubbleType,
  EAiFrameImageSizeType,
  EAiTextAlignment,
  EAiTypeActionByNormalCut,
  EAiWebtoonStoryBorderType,
} from 'src/common/ai-webtoon.enum';
import { IAiGeneralCharacter } from './ai-webtoon-character.interface';

export interface IAiItemImageSketchCutChapter {
  image: string;
  imageWidth: number;
  imageHeight: number;
}

export interface IAiCoordinate {
  head: number[];
  mid: number[];
  bubble: number[];
}

export interface IAiItemDialogueBubble {
  characterUuid?: string;
  content: string;
  contentJson: string;
  coordinate: IAiCoordinate;
  bubbleType?: EAiBubbleType;
  bubbleBorderColor?: string;
  bubbleBackgroundColor?: string;
  bubbleWidth?: number | 'auto';
  bubbleHeight?: number | 'auto';
  fontFamily?: string;
  fontSize?: number;
  fontStyle?: string;
  fontWeight?: string;
  bubbleFontColor?: string;
}

export interface IAiItemEffect {
  image: string;
  rotate?: number;
  width: number;
  height: number;
  x: number;
  y: number;
  scaleX?: number;
  scaleY?: number;
  zIndex?: number;
}

export interface IAiItemText {
  text: string;
  textJson?: string;
  rotate?: number;
  scale?: number;
  fontFamily?: string;
  fontSize?: number;
  fontStyle?: string;
  fontWeight?: string;
  fontColor?: string;
  x: number;
  y: number;
  scaleX?: number;
  scaleY?: number;
  zIndex?: number;
  textAlign?: EAiTextAlignment;
}

export interface IAiItemImageCenterPoint {
  x: number;
  y: number;
}

export interface IAiEditConfig {
  image?: string;
  left?: number;
  top?: number;
  width?: number;
  height?: number;
  frameWidth?: number;
  frameHeight?: number;
  scaleX?: number;
  scaleY?: number;
  angle?: number;
  cropX?: number;
  cropY?: number;
  cropWidth?: number;
  cropHeight?: number;
  imageCenterPoint?: IAiItemImageCenterPoint;
}

export interface IAiBorderConfig {
  borderType: EAiWebtoonStoryBorderType;
  borderColor: string;
  borderWeight: number;
}

export interface IAiOriginalConfigCutChapter {
  info?: string;
  description?: string;
  negativeDescription?: string;
  generalPrompt?: string;
  negativePrompt?: string;
  danbooruPrompt?: string;
  imageSizeType: EAiFrameImageSizeType;
  imageWidth: number;
  imageHeight: number;
  numberOfImages: number;
  characters: IAiGeneralCharacter[];
  baseModel: EAiBaseModelGenImageType | EAiBaseModelInpaintType;
  controlNetStrengthCanny: number;
  controlNetStrengthDepth: number;
  sketchImage?: string | null;
  depthImage?: string | null;
  seed?: string;
  cfgScale?: number;
  steps?: number;
}

export interface IItemImageNormalCutChapter {
  type: EAiTypeActionByNormalCut;
  image: string;
  rootImage: string;
  finishedImage: string;
  editImage: string;
  imageWidth: number;
  imageHeight: number;
  frameWidthImageContainer: number;
  frameHeightImageContainer: number;
  seed?: string | null;
  sampler?: string | null;
  steps?: number | null;
  cfgScale?: number | null;
  baseModel?: EAiBaseModelGenImageType | EAiBaseModelInpaintType;
  bubbles: IAiItemDialogueBubble[];
  effects: IAiItemEffect[];
  texts: IAiItemText[];
  editConfig?: IAiEditConfig;
  borderConfig?: IAiBorderConfig;
  originalConfig?: IAiOriginalConfigCutChapter;
  createdAt: Date;
}

export interface IItemImageInpaint {
  image: string;
  prompt?: string;
  imageWidth: number;
  imageHeight: number;
  frameWidthImageContainer: number;
  frameHeightImageContainer: number;
  characters: string[];
  baseModel: EAiBaseModelInpaintType;
  bubbles: IAiItemDialogueBubble[];
  effects: IAiItemEffect[];
  texts: IAiItemText[];
  editConfig?: IAiEditConfig;
  createdAt: Date;
}

export interface CutRowImportFile {
  sceneName: string;
  cut: number;
  generalDescription: string;
  fluxPrompt: string;
  animagineXlPrompt: string;
  negativeDescription: string;
  negativePrompt: string;
  characters: string[];
  numberOfImages: number;
  imageSizeType: EAiFrameImageSizeType;
  width: number;
  height: number;
  model: EAiBaseModelGenImageType;
}
