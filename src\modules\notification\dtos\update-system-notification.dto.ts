import { ApiProperty } from '@nestjs/swagger';
import { IsEnum } from 'class-validator';
import { NotificationStatus } from '../../../database/entities/system-notification.entity';

export class UpdateSystemNotificationDto {
  @ApiProperty({
    enum: NotificationStatus,
    example: NotificationStatus.INACTIVE,
    description: 'New notification status',
  })
  @IsEnum(NotificationStatus)
  status: NotificationStatus;
}
