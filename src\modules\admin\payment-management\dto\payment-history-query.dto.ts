import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsOptional,
  IsInt,
  Min,
  Max,
  IsString,
  IsEnum,
  IsDateString,
} from 'class-validator';

export enum PurchaseType {
  ALL = 'All',
  FIRST_PURCHASE = 'firstPurchase',
  RE_PURCHASE = 'rePurchase',
}

export enum ProductType {
  ALL = 'All',
  SUBSCRIPTION = 'Subscription',
  COIN = 'Coin',
}

export enum PaymentPlatform {
  ALL = 'All',
  MOBILE = 'Mobile',
  PC = 'PC',
}

export enum PaymentMethodType {
  ALL = 'All',
  CREDIT_CARD = 'creditCard',
  PAYPAL = 'Paypal',
}

export enum PaymentLanguage {
  ALL = 'All',
  KOREAN = 'Korean',
  ENGLISH = 'English',
}

export enum PaymentHistoryStatus {
  ALL = 'All',
  NORMAL = 'Normal',
  CANCELLED = 'Cancelled',
}

export enum SearchType {
  ID = 'ID',
  REF = 'REF',
  PURCHASE_IP = 'purchaseIp',
  TRANSACTION_ID = 'transactionId',
}

export class PaymentHistoryQueryDto {
  @ApiPropertyOptional({
    description: 'Page number',
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Items per page',
    default: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({
    description: 'Start date filter (YYYY-MM-DD)',
    example: '2025-01-01',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date filter (YYYY-MM-DD)',
    example: '2025-01-31',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Purchase type filter',
    enum: PurchaseType,
    default: PurchaseType.ALL,
  })
  @IsOptional()
  @IsEnum(PurchaseType)
  purchaseType?: PurchaseType = PurchaseType.ALL;

  @ApiPropertyOptional({
    description: 'Product type filter',
    enum: ProductType,
    default: ProductType.ALL,
  })
  @IsOptional()
  @IsEnum(ProductType)
  productType?: ProductType = ProductType.ALL;

  @ApiPropertyOptional({
    description: 'Platform filter',
    enum: PaymentPlatform,
    default: PaymentPlatform.ALL,
  })
  @IsOptional()
  @IsEnum(PaymentPlatform)
  platform?: PaymentPlatform = PaymentPlatform.ALL;

  @ApiPropertyOptional({
    description: 'Payment method filter',
    enum: PaymentMethodType,
    default: PaymentMethodType.ALL,
  })
  @IsOptional()
  @IsEnum(PaymentMethodType)
  paymentType?: PaymentMethodType = PaymentMethodType.ALL;

  @ApiPropertyOptional({
    description: 'Language ID filter',
    example: 1,
  })
  @IsOptional()
  language?: number;

  @ApiPropertyOptional({
    description: 'Payment status filter',
    enum: PaymentHistoryStatus,
    default: PaymentHistoryStatus.ALL,
  })
  @IsOptional()
  @IsEnum(PaymentHistoryStatus)
  paymentStatus?: PaymentHistoryStatus = PaymentHistoryStatus.ALL;

  @ApiPropertyOptional({
    description: 'Email filter - filter payments by user email',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.trim())
  email?: string;

  @ApiPropertyOptional({
    description: 'Search type - specify what field to search by',
    enum: SearchType,
    default: SearchType.ID,
  })
  @IsOptional()
  @IsEnum(SearchType)
  searchType?: SearchType = SearchType.ID;

  @ApiPropertyOptional({
    description:
      'Search value based on search type (ID, REF, Purchase IP, or Transaction ID)',
    example: '12345',
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.trim())
  search?: string;
}
