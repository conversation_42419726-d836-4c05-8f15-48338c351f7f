export class ContentViewEventDto {
  userId: number;
  contentId: number;
  episodeId?: number;
  viewTime: number; // timestamp
  readDuration?: number; // milliseconds
  platform?: string;
  metadata?: Record<string, any>;
}

export class UserActivityEventDto {
  userId?: number | null; // Optional for anonymous events
  activityType:
    | 'login'
    | 'register'
    | 'delete'
    | 'faq_view'
    | 'account_deletion'
    | 'legal_document_view'
    | 'file_upload'
    | 'file_delete'
    | 'access_page'
    | 'first_purchase_attempt'
    | 'first_purchase_success'
    | 'repurchase_attempt'
    | 'repurchase_success';
  timestamp: number;
  platform?: string;
  language?: number; // Language setting ID
  metadata?: Record<string, any>;
}

export class PaymentEventDto {
  userId: number;
  paymentId: number;
  amount: number;
  currency: string;
  amountKRW?: number;
  status: 'pending' | 'success' | 'failed';
  paymentType: 'first_purchase' | 'repurchase' | 'subscription';
  platform?: string;
  language?: string;
  region?: string;
  exchangeRate?: number;
  exchangeRateDate?: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

export class PaymentAttemptEventDto {
  userId: number;
  paymentId: number;
  amount: number;
  currency: string;
  amountKRW?: number;
  paymentType: 'first_purchase' | 'repurchase' | 'subscription';
  platform?: string;
  language?: string;
  region?: string;
  exchangeRate?: number;
  exchangeRateDate?: string;
  timestamp: number;
  metadata?: Record<string, any>;
}
