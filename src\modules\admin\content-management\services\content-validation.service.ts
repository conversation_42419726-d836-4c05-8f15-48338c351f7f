import { BadRequestException, Injectable } from '@nestjs/common';
import { ContentRepository } from 'src/database/repositories/content.repository';
import { AuthorRepository } from 'src/database/repositories/author.repository';
import { GenreRepository } from 'src/database/repositories/genre.repository';
import { SettingRepository } from 'src/database/repositories/setting.repository';
import { EpisodeRepository } from 'src/database/repositories/episode.repository';
import { MESSAGE_CONFIG } from 'src/common/message.config';
import { SettingGroup } from 'src/common/setting.enum';
import { AuthorEntity } from 'src/database/entities/author.entity';
import { GenreEntity } from 'src/database/entities/genre.entity';
import { ContentEntity } from 'src/database/entities/content.entity';
import { SettingEntity } from 'src/database/entities/setting.entity';
import { EpisodeEntity } from 'src/database/entities/episode.entity';

export interface SettingValidationRequest {
  ids: number[];
  group: SettingGroup;
  required: boolean;
}

export interface AuthorValidationResult {
  nameExists: AuthorEntity | null;
  emailExists: AuthorEntity | null;
}

@Injectable()
export class ContentValidationService {
  constructor(
    private readonly contentRepository: ContentRepository,
    private readonly authorRepository: AuthorRepository,
    private readonly genreRepository: GenreRepository,
    private readonly settingRepository: SettingRepository,
    private readonly episodeRepository: EpisodeRepository,
  ) {}

  async validateAuthorUniqueness(
    name: string,
    email: string,
    excludeId?: number,
  ): Promise<AuthorValidationResult> {
    const queryBuilder = this.authorRepository
      .createQueryBuilder('author')
      .where('author.name = :name OR author.email = :email', { name, email })
      .select(['author.id', 'author.name', 'author.email']);

    if (excludeId) {
      queryBuilder.andWhere('author.id != :excludeId', { excludeId });
    }

    const existingAuthors = await queryBuilder.getMany();

    const nameExists = existingAuthors.find((a) => a.name === name) || null;
    const emailExists = existingAuthors.find((a) => a.email === email) || null;

    return { nameExists, emailExists };
  }

  async validateAuthorExists(authorId: number): Promise<AuthorEntity> {
    const author = await this.authorRepository.findOneById(authorId);
    if (!author) {
      throw new BadRequestException(MESSAGE_CONFIG.AUTHOR_NOT_FOUND);
    }
    return author;
  }

  async validateGenreExists(genreId: number): Promise<GenreEntity> {
    const genre = await this.genreRepository.findOneById(genreId);
    if (!genre) {
      throw new BadRequestException(MESSAGE_CONFIG.GENRE_NOT_FOUND);
    }
    return genre;
  }

  async validateContentExists(contentId: number): Promise<ContentEntity> {
    const content = await this.contentRepository.findOneBy({ id: contentId });
    if (!content) {
      throw new BadRequestException(MESSAGE_CONFIG.CONTENT_NOT_FOUND);
    }
    return content;
  }

  async validateEpisodeExists(episodeId: number): Promise<EpisodeEntity> {
    const episode = await this.episodeRepository.findOneBy({ id: episodeId });
    if (!episode) {
      throw new BadRequestException(MESSAGE_CONFIG.EPISODE_NOT_FOUND);
    }
    return episode;
  }

  async validateSettingsExist(
    settingRequests: SettingValidationRequest[],
  ): Promise<Map<SettingGroup, SettingEntity[]>> {
    const allSettingIds = settingRequests.flatMap((req) => req.ids);
    const allGroups = [...new Set(settingRequests.map((req) => req.group))];

    if (allSettingIds.length === 0) {
      return new Map();
    }

    const allSettings = await this.settingRepository
      .createQueryBuilder('setting')
      .where('setting.id IN (:...settingIds)', { settingIds: allSettingIds })
      .andWhere('setting.group IN (:...groups)', { groups: allGroups })
      .getMany();

    const resultMap = new Map<SettingGroup, SettingEntity[]>();

    // Validate each group
    for (const request of settingRequests) {
      const settingsForGroup = allSettings.filter(
        (setting) =>
          request.ids.includes(setting.id) &&
          setting.group === (request.group as string),
      );

      if (request.required && settingsForGroup.length !== request.ids.length) {
        const foundIds = settingsForGroup.map((s) => s.id);
        const missingIds = request.ids.filter((id) => !foundIds.includes(id));
        throw new BadRequestException(
          `${MESSAGE_CONFIG.SETTING_NOT_FOUND.message}: ${request.group}. Missing IDs: ${missingIds.join(', ')}`,
        );
      }

      if (settingsForGroup.length > 0) {
        resultMap.set(request.group, settingsForGroup);
      }
    }

    return resultMap;
  }

  async validateSingleSettingExists(
    settingId: number,
    group: SettingGroup,
  ): Promise<SettingEntity> {
    const setting = await this.settingRepository.findOneByIdAndGroup(
      settingId,
      group,
    );
    if (!setting) {
      throw new BadRequestException(
        `${MESSAGE_CONFIG.SETTING_NOT_FOUND.message}: ${group}`,
      );
    }
    return setting;
  }

  async batchValidateEntities(validationPromises: {
    author?: Promise<AuthorEntity>;
    genre?: Promise<GenreEntity>;
    content?: Promise<ContentEntity>;
    episode?: Promise<EpisodeEntity>;
    settings?: Promise<Map<SettingGroup, SettingEntity[]>>;
  }): Promise<{
    author?: AuthorEntity;
    genre?: GenreEntity;
    content?: ContentEntity;
    episode?: EpisodeEntity;
    settings?: Map<SettingGroup, SettingEntity[]>;
  }> {
    const promises: Promise<any>[] = [];
    const keys: string[] = [];

    Object.entries(validationPromises).forEach(([key, promise]) => {
      if (promise !== undefined) {
        promises.push(promise);
        keys.push(key);
      }
    });

    if (promises.length === 0) {
      return {};
    }

    const results = await Promise.all(promises);
    const resultMap: any = {};

    keys.forEach((key, index) => {
      resultMap[key] = results[index];
    });

    return resultMap;
  }
}
