import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserTable1748935385236 implements MigrationInterface {
  name = 'UpdateUserTable1748935385236';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`signUpIp\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`verificationIp\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`signInIp\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`restrictedDate\` datetime NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`totalPurchases\` int NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`platform\` enum ('pc', 'mobile') NULL DEFAULT 'pc'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`platform\``);
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP COLUMN \`totalPurchases\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP COLUMN \`restrictedDate\``,
    );
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`signInIp\``);
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP COLUMN \`verificationIp\``,
    );
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`signUpIp\``);
  }
}
