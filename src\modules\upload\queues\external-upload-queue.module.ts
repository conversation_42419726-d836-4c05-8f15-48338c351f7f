import { Global, Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { UPLOAD_QUEUES } from './constants/upload-queues.constant';
import { SignatureGeneratorService } from './services/signature-generator.service';
import { ExternalUploadClientService } from './services/external-upload-client.service';
import { ExternalUploadQueueService } from './services/external-upload-queue.service';
import { ExternalUploadProcessor } from './processors/external-upload.processor';
import { FileProcessingService } from '../shared/services/file-processing.service';
@Global()
@Module({
  imports: [
    ConfigModule,
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    BullModule.registerQueue({
      name: UPLOAD_QUEUES.EXTERNAL_UPLOAD,
      defaultJobOptions: {
        removeOnComplete: 10,
        removeOnFail: 5,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    }),
  ],
  providers: [
    SignatureGeneratorService,
    ExternalUploadClientService,
    ExternalUploadQueueService,
    ExternalUploadProcessor,
    FileProcessingService,
  ],
  exports: [ExternalUploadQueueService, SignatureGeneratorService],
})
export class ExternalUploadQueueModule {}
