import { Entity, Column, Index, ManyToOne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { ExchangeRateEntity } from './exchange-rate.entity';

export enum PaymentType {
  FIRST_PURCHASE = 'first_purchase',
  REPURCHASE = 'repurchase',
  SUBSCRIPTION = 'subscription',
}

export enum UserPlatform {
  PC = 'pc',
  MOBILE = 'mobile',
}

@Entity('payment_analytics')
@Index(['date', 'hour', 'paymentType', 'platform', 'language', 'region'], {
  unique: true,
})
export class PaymentAnalyticsEntity extends DefaultEntity {
  @Column({ type: 'date' })
  @Index()
  date: Date;

  @Column({ type: 'tinyint', default: 0 })
  hour: number;

  @Column({ type: 'enum', enum: PaymentType })
  paymentType: PaymentType;

  @Column({ type: 'enum', enum: UserPlatform, nullable: true })
  platform: UserPlatform;

  @Column({ type: 'varchar', length: 3, default: 'USD' })
  currency: string;

  @Column({ type: 'int', default: 0 })
  totalRequests: number;

  @Column({ type: 'int', default: 0 })
  successfulPayments: number;

  @Column({ type: 'int', default: 0 })
  failedPayments: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalAmount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalAmountKRW: number;

  @Column({ type: 'int', default: 0 })
  uniqueUsers: number;

  @Column({ type: 'int', default: 0 })
  paymentAttempts: number;

  @Column({ type: 'varchar', length: 5, nullable: true })
  language: string;

  @Column({ type: 'varchar', length: 5, nullable: true })
  region: string;

  @Column({ nullable: true })
  exchangeRateId?: number;

  @ManyToOne(() => ExchangeRateEntity, { eager: false })
  @JoinColumn({ name: 'exchangeRateId' })
  exchangeRate: ExchangeRateEntity;

  @Column({ type: 'int', default: 0 })
  totalRegisteredUsers: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  firstPurchaseRate: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  repurchaseRate: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  totalPurchaseRate: number;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;
}
