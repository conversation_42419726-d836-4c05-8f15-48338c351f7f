const crypto = require('crypto');
const fs = require('fs');

/**
 * Generate RSA key pair for signature verification
 */
function generateKeyPair() {
  console.log('🔑 Generating RSA key pair for signature verification...');
  
  try {
    const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
      modulusLength: 2048,
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
      }
    });

    console.log('✅ Key pair generated successfully!');
    
    // Save to files
    fs.writeFileSync('private_key.pem', privateKey);
    fs.writeFileSync('public_key.pem', publicKey);
    
    console.log('💾 Keys saved to:');
    console.log('  - private_key.pem');
    console.log('  - public_key.pem');
    
    // Show environment variables format
    console.log('\n📝 Add these to your .env file:');
    console.log('SIGNATURE_PRIVATE_KEY="' + privateKey.replace(/\n/g, '\\n') + '"');
    console.log('SIGNATURE_PUBLIC_KEY="' + publicKey.replace(/\n/g, '\\n') + '"');
    
    // Test the keys
    testKeys(privateKey, publicKey);
    
  } catch (error) {
    console.error('❌ Failed to generate key pair:', error.message);
  }
}

/**
 * Test the generated keys by signing and verifying
 */
function testKeys(privateKey, publicKey) {
  console.log('\n🧪 Testing generated keys...');
  
  try {
    const testData = Buffer.from('test message for signature');
    
    // Sign the data
    const sign = crypto.createSign('SHA256');
    sign.update(testData);
    const signature = sign.sign(privateKey, 'hex');
    
    // Verify the signature
    const verify = crypto.createVerify('SHA256');
    verify.update(testData);
    const isValid = verify.verify(publicKey, signature, 'hex');
    
    if (isValid) {
      console.log('✅ Key pair test successful!');
      console.log('📋 Test signature:', signature.substring(0, 50) + '...');
    } else {
      console.log('❌ Key pair test failed - signature verification failed');
    }
    
  } catch (error) {
    console.log('❌ Key pair test failed:', error.message);
  }
}

/**
 * Validate existing keys from environment
 */
function validateExistingKeys() {
  console.log('🔍 Validating existing keys from environment...');
  
  require('dotenv').config();
  
  const privateKey = process.env.SIGNATURE_PRIVATE_KEY;
  const publicKey = process.env.SIGNATURE_PUBLIC_KEY;
  
  if (!privateKey || !publicKey) {
    console.log('❌ Keys not found in environment variables');
    console.log('Run with --generate to create new keys');
    return;
  }
  
  // Validate private key format
  const privateKeyRegex = /^-----BEGIN (RSA )?PRIVATE KEY-----[\s\S]*-----END (RSA )?PRIVATE KEY-----$/;
  const publicKeyRegex = /^-----BEGIN PUBLIC KEY-----[\s\S]*-----END PUBLIC KEY-----$/;
  
  // Convert escaped newlines
  const cleanPrivateKey = privateKey.replace(/\\n/g, '\n');
  const cleanPublicKey = publicKey.replace(/\\n/g, '\n');
  
  console.log('🔐 Private key format check:', privateKeyRegex.test(cleanPrivateKey) ? '✅' : '❌');
  console.log('🔓 Public key format check:', publicKeyRegex.test(cleanPublicKey) ? '✅' : '❌');
  
  try {
    crypto.createPrivateKey(cleanPrivateKey);
    console.log('🔐 Private key crypto validation: ✅');
  } catch (error) {
    console.log('🔐 Private key crypto validation: ❌', error.message);
  }
  
  try {
    crypto.createPublicKey(cleanPublicKey);
    console.log('🔓 Public key crypto validation: ✅');
  } catch (error) {
    console.log('🔓 Public key crypto validation: ❌', error.message);
  }
  
  // Test signing and verification
  testKeys(cleanPrivateKey, cleanPublicKey);
}

/**
 * Debug signature generation with specific file
 */
function debugSignature(filePath) {
  console.log(`🐛 Debugging signature generation for: ${filePath}`);
  
  require('dotenv').config();
  
  const privateKey = process.env.SIGNATURE_PRIVATE_KEY;
  
  if (!privateKey) {
    console.log('❌ SIGNATURE_PRIVATE_KEY not found in environment');
    return;
  }
  
  const cleanPrivateKey = privateKey.replace(/\\n/g, '\n');
  
  try {
    const fileBuffer = fs.readFileSync(filePath);
    console.log(`📄 File size: ${fileBuffer.length} bytes`);
    console.log(`📄 File first 32 bytes:`, fileBuffer.subarray(0, 32).toString('hex'));
    
    // Test signature generation
    const sign = crypto.createSign('SHA256');
    sign.update(fileBuffer);
    const signature = sign.sign(cleanPrivateKey, 'hex');
    
    console.log('✅ Signature generated successfully!');
    console.log('📋 Signature:', signature);
    
  } catch (error) {
    console.log('❌ Debug failed:', error.message);
    console.log('Stack trace:', error.stack);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.includes('--generate')) {
  generateKeyPair();
} else if (args.includes('--validate')) {
  validateExistingKeys();
} else if (args.includes('--debug') && args[1]) {
  debugSignature(args[1]);
} else {
  console.log('🔑 Signature Key Management Tool');
  console.log('');
  console.log('Usage:');
  console.log('  node scripts/generate-signature-keys.js --generate   Generate new key pair');
  console.log('  node scripts/generate-signature-keys.js --validate   Validate existing keys');
  console.log('  node scripts/generate-signature-keys.js --debug <file>   Debug signature for specific file');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/generate-signature-keys.js --generate');
  console.log('  node scripts/generate-signature-keys.js --validate');
  console.log('  node scripts/generate-signature-keys.js --debug uploads/test.jpg');
}