import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional } from 'class-validator';

export class FilterAuthorDto {
  @ApiProperty({
    description: 'Search by name',
    required: false,
  })
  name?: string;

  @ApiProperty({
    description: 'Date filter',
    required: false,
    enum: ['createdAt'],
  })
  dateFilter?: string;

  @ApiProperty({
    description: 'Date filter start',
    required: false,
    example: '2025-01-01',
  })
  dateFilterStart?: string;

  @ApiProperty({
    description: 'Date filter end',
    required: false,
    example: '2025-01-01',
  })
  dateFilterEnd?: string;

  @ApiProperty({
    description: 'Limit number',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number;

  @ApiProperty({
    description: 'Page number',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number;
}
