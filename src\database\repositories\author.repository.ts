import { DataSource, Repository } from 'typeorm';
import { Injectable, NotFoundException } from '@nestjs/common';
import { AuthorEntity } from '../entities/author.entity';
import { MESSAGE_CONFIG } from '../../common/message.config';
@Injectable()
export class AuthorRepository extends Repository<AuthorEntity> {
  constructor(private dataSource: DataSource) {
    super(AuthorEntity, dataSource.createEntityManager());
  }

  async findOneById(id: number) {
    const author = await this.findOne({ where: { id } });
    if (!author) {
      throw new NotFoundException(MESSAGE_CONFIG.AUTHOR_NOT_FOUND);
    }
    return author;
  }
}
