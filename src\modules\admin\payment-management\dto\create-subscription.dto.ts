import {
  <PERSON>NotEmpty,
  IsString,
  IsNumber,
  IsEnum,
  IsOptional,
  IsBoolean,
  Min,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CommonStatus } from '../../../../common/status.enum';

export class CreateSubscriptionDto {
  @ApiProperty({
    description: 'Currency code for the subscription',
    example: 'USD',
  })
  @IsNotEmpty()
  @IsString()
  currency: string;

  @ApiPropertyOptional({
    description: 'Status of the subscription',
    enum: CommonStatus,
    default: CommonStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(CommonStatus)
  status?: CommonStatus;

  @ApiProperty({
    description: 'Timestamp when subscription starts',
    example: 1640995200000,
    minimum: 0,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  startedAt: number;

  @ApiProperty({
    description: 'Timestamp when subscription expires',
    example: 1643673600000,
    minimum: 0,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  expiresAt: number;

  @ApiPropertyOptional({
    description: 'Whether subscription auto-renews',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  autoRenew?: boolean;

  @ApiPropertyOptional({
    description: 'Whether this is a trial subscription',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isTrial?: boolean;

  @ApiPropertyOptional({
    description: 'Number of trial days',
    example: 7,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  trialDays?: number;

  @ApiProperty({
    description: 'ID of the user who owns the subscription',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  userId: number;

  @ApiProperty({
    description: 'ID of the subscription plan',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  subscriptionPlanId: number;
}
