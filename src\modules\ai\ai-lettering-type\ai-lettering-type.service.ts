import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { AI_MESSAGE_CONFIG } from 'src/common/message.config';
import { AiLetteringTypeRepository } from 'src/database/repositories/ai-lettering-type.repository';
import { AiLetteringRepository } from 'src/database/repositories/ai-lettering.repository';
import { CreateAiLetteringTypeDto } from './dto/create-ai-lettering-type.dto';
import { SortAiLetteringTypeDto } from './dto/sort-ai-lettering-type.dto';

@Injectable()
export class AiLetteringTypeService {
  constructor(
    private readonly aiLetteringTypeRepository: AiLetteringTypeRepository,
    private readonly aiLetteringRepository: AiLetteringRepository,
  ) {}
  private readonly logger = new Logger(AiLetteringTypeService.name);

  async create(dto: CreateAiLetteringTypeDto) {
    const { title } = dto;
    const check = await this.aiLetteringTypeRepository.findOne({
      where: { title },
    });
    if (check)
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_LETTERING_TYPE_EXISTS);

    const count = await this.aiLetteringTypeRepository.count();
    await this.aiLetteringTypeRepository.save({
      title,
      priority: count + 1,
      isDefault: false,
    });

    return true;
  }

  async sort(dto: SortAiLetteringTypeDto) {
    const { ids } = dto;
    const items = await this.aiLetteringTypeRepository.find();

    ids.forEach((id, index) => {
      const item = items.find((e) => e.id === id);
      if (!item)
        throw new NotFoundException(
          AI_MESSAGE_CONFIG.AI_LETTERING_TYPE_NOT_FOUND,
        );
      item.priority = index + 1;
    });

    await this.aiLetteringTypeRepository.save(items);
    return true;
  }

  async list() {
    const items = await this.aiLetteringTypeRepository.find({
      order: { priority: 'ASC' },
    });
    return items;
  }

  async delete(id: number) {
    const type = await this.aiLetteringTypeRepository.findOne({
      where: { id },
    });
    if (!type)
      throw new NotFoundException(
        AI_MESSAGE_CONFIG.AI_LETTERING_TYPE_NOT_FOUND,
      );

    if (type.isDefault)
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_LETTERING_TYPE_CANNOT_DELETE_DEFAULT,
      );

    const check = await this.aiLetteringRepository.findOne({
      where: { aiLetteringTypeId: id },
    });

    if (check)
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_LETTERING_TYPE_IN_USE);

    await this.aiLetteringTypeRepository.softDelete(id);
    return true;
  }
}
