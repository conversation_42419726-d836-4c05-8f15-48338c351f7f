import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { SupportCategoryEntity } from '../../../database/entities/support-category.entity';
import { SupportTicketEntity } from '../../../database/entities/support-ticket.entity';
import { SupportResponseEntity } from '../../../database/entities/support-response.entity';
import { AdminEntity } from '../../../database/entities/admin.entity';

// Repositories
import { SupportCategoryRepository } from '../../../database/repositories/support-category.repository';
import { SupportTicketRepository } from '../../../database/repositories/support-ticket.repository';
import { SupportResponseRepository } from '../../../database/repositories/support-response.repository';
import { AdminRepository } from '../../../database/repositories/admin.repository';

// Services
import { SupportAdminService } from './services/support-admin.service';
import { SupportCategoryAdminService } from './services/support-category-admin.service';

// Controllers
// import { SupportAdminController } from './support-admin.controller';
import { SupportCategoryAdminController } from './support-category-admin.controller';
import { QAAdminController } from './qa-admin.controller';
import { MenuModule } from '../menu/menu.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SupportCategoryEntity,
      SupportTicketEntity,
      SupportResponseEntity,
      AdminEntity,
    ]),
    MenuModule,
  ],
  providers: [
    // Repositories
    SupportCategoryRepository,
    SupportTicketRepository,
    SupportResponseRepository,
    AdminRepository,

    // Services
    SupportAdminService,
    SupportCategoryAdminService,
  ],
  controllers: [
    // SupportAdminController,
    SupportCategoryAdminController,
    QAAdminController,
  ],
  exports: [SupportAdminService, SupportCategoryAdminService],
})
export class SupportAdminModule {}
