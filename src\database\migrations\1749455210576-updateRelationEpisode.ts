import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateRelationEpisode1749455210576 implements MigrationInterface {
  name = 'UpdateRelationEpisode1749455210576';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`episode_image\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`path\` varchar(255) NOT NULL, \`group\` varchar(255) NOT NULL, \`size\` int NOT NULL, \`episodeId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`episode\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`episodeNumber\` int NOT NULL, \`episodeString\` varchar(255) NOT NULL, \`title\` varchar(255) NOT NULL, \`expectedReleaseDate\` datetime NOT NULL, \`image\` varchar(255) NOT NULL, \`nonUserViewing\` tinyint NOT NULL, \`bgColor\` varchar(255) NOT NULL, \`contentId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode_image\` ADD CONSTRAINT \`FK_31579da78526ba9a1ae8f3bd648\` FOREIGN KEY (\`episodeId\`) REFERENCES \`episode\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode\` ADD CONSTRAINT \`FK_563dce75071b20e970195e459a3\` FOREIGN KEY (\`contentId\`) REFERENCES \`content\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`episode\` DROP FOREIGN KEY \`FK_563dce75071b20e970195e459a3\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode_image\` DROP FOREIGN KEY \`FK_31579da78526ba9a1ae8f3bd648\``,
    );
    await queryRunner.query(`DROP TABLE \`episode\``);
    await queryRunner.query(`DROP TABLE \`episode_image\``);
  }
}
