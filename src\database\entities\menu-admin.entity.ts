import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { AdminEntity } from './admin.entity';
import { MenuEntity } from './menu.entity';
import { MenuAdminStatus } from '../../common/status.enum';

@Entity('menu_admin')
export class MenuAdminEntity extends DefaultEntity {
  @ManyToOne(() => AdminEntity)
  @JoinColumn({ name: 'adminId' })
  admin: AdminEntity;

  @ManyToOne(() => MenuEntity)
  @JoinColumn({ name: 'menuId' })
  menu: MenuEntity;

  @Column({
    type: 'enum',
    enum: MenuAdminStatus,
    default: MenuAdminStatus.ACTIVE,
  })
  status: MenuAdminStatus;
}
