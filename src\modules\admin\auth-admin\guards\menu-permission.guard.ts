import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { MENU_PERMISSIONS_KEY } from '../decorators/menu-permission.decorator';
import { MenuService } from '../../menu/menu.service';
import { AdminRole } from '../../../../common/role.enum';
@Injectable()
export class MenuPermissionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private menuService: MenuService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredMenus = this.reflector.getAllAndOverride<string[]>(
      MENU_PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    const { user } = context.switchToHttp().getRequest();

    if (user.role === AdminRole.SUPER_ADMIN) return true;
    if (!user) return false;

    const menuAdmin = await this.menuService.getMenuAdmin(
      user.id,
      requiredMenus,
    );

    if (menuAdmin.length === 0) return false;

    return true;
  }
}
