import {
  UploadContext,
  UploaderType,
} from '../../../database/entities/upload.entity';

export interface UploadContextConfig {
  maxFileSize: number;
  allowedTypes: string[];
  maxFiles: number;
  generateThumbnail: boolean;
  folder: string;
}

export interface UploadResult {
  success: boolean;
  uploadId: number;
  filePath: string;
  thumbnailPath?: string | null;
  publicUrl: string;
}

export interface ValidationResult {
  valid: boolean;
  warnings: string[];
}

export const UPLOAD_CONFIG = {
  // Base settings
  baseUploadPath: 'uploads',
  maxConcurrentUploads: 5,
  thumbnailSize: { width: 300, height: 300 },

  // User contexts
  [UploaderType.USER]: {
    [UploadContext.PROFILE]: {
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedTypes: ['image/png', 'image/jpeg', 'image/jpg'],
      maxFiles: 1, // Only one profile picture
      generateThumbnail: true,
      folder: 'users/{userId}/profile',
    } as UploadContextConfig,

    [UploadContext.SUPPORT]: {
      maxFileSize: 25 * 1024 * 1024, // 25MB
      allowedTypes: [
        'image/png',
        'image/jpeg',
        'image/jpg',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
      ],
      maxFiles: 10,
      generateThumbnail: true,
      folder: 'users/{userId}/support',
    } as UploadContextConfig,

    [UploadContext.GENERAL]: {
      maxFileSize: 10 * 1024 * 1024, // 10MB
      allowedTypes: [
        'image/png',
        'image/jpeg',
        'image/jpg',
        'application/pdf',
        'text/plain',
      ],
      maxFiles: 20,
      generateThumbnail: false,
      folder: 'users/{userId}/general',
    } as UploadContextConfig,
  },

  // Admin contexts
  [UploaderType.ADMIN]: {
    [UploadContext.CONTENT]: {
      maxFileSize: 100 * 1024 * 1024, // 100MB
      allowedTypes: [
        'image/png',
        'image/jpeg',
        'image/jpg',
        'image/gif',
        'image/webp',
      ],
      maxFiles: 1000,
      generateThumbnail: true,
      folder: 'admin/content',
    } as UploadContextConfig,

    [UploadContext.SETTINGS]: {
      maxFileSize: 20 * 1024 * 1024, // 20MB
      allowedTypes: ['image/png', 'image/jpeg', 'image/jpg'],
      maxFiles: 100,
      generateThumbnail: true,
      folder: 'admin/settings',
    } as UploadContextConfig,

    [UploadContext.BULK]: {
      maxFileSize: 500 * 1024 * 1024, // 500MB
      allowedTypes: ['application/zip', 'application/x-rar-compressed'],
      maxFiles: 10,
      generateThumbnail: false,
      folder: 'admin/bulk',
    } as UploadContextConfig,
  },
};

export const getUploadConfig = (
  uploaderType: UploaderType,
  context: UploadContext,
): UploadContextConfig => {
  return UPLOAD_CONFIG[uploaderType][context];
};

export const getAllowedMimeTypes = (
  uploaderType: UploaderType,
  context: UploadContext,
): string[] => {
  return getUploadConfig(uploaderType, context).allowedTypes;
};

export const getMaxFileSize = (
  uploaderType: UploaderType,
  context: UploadContext,
): number => {
  return getUploadConfig(uploaderType, context).maxFileSize;
};
