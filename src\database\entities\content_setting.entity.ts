import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { ContentEntity } from './content.entity';
import { SettingEntity } from './setting.entity';
import { SettingStatus } from '../../common/status.enum';
import { ContentSettingType } from '../../common/setting.enum';

@Entity('content_setting')
export class ContentSettingEntity extends DefaultEntity {
  @ManyToOne(() => ContentEntity, (content) => content.id)
  @JoinColumn()
  content: ContentEntity;

  @ManyToOne(() => SettingEntity, (setting) => setting.id)
  @JoinColumn()
  setting: SettingEntity;

  @Column({
    type: 'enum',
    enum: SettingStatus,
    nullable: false,
    default: SettingStatus.ACTIVE,
  })
  status: SettingStatus;

  @Column({
    type: 'enum',
    enum: ContentSettingType,
    nullable: false,
    default: ContentSettingType.CLASSIFICATION,
  })
  type: ContentSettingType;
}
