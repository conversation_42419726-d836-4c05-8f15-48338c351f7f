import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { UPLOAD_QUEUES } from '../constants/upload-queues.constant';
import { ExternalUploadJobData } from '../interfaces/external-upload-job.interface';

@Injectable()
export class ExternalUploadQueueService {
  private readonly logger = new Logger(ExternalUploadQueueService.name);

  constructor(
    @InjectQueue(UPLOAD_QUEUES.EXTERNAL_UPLOAD)
    private externalUploadQueue: Queue,
  ) {}

  async queueExternalUpload(data: ExternalUploadJobData): Promise<string> {
    try {
      this.logger.debug(
        `Queueing external upload: ${data.imagePath} -> ${data.uploadUrl}`,
      );

      const job = await this.externalUploadQueue.add(
        'upload-to-external',
        data,
        {
          priority: 1,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnComplete: 10,
          removeOnFail: 5,
          delay: 0,
        },
      );

      this.logger.log(`External upload job queued with ID: ${job.id}`);
      return job.id as string;
    } catch (error) {
      this.logger.error(`Failed to queue external upload: ${error.message}`);
      throw new Error(`Failed to queue external upload: ${error.message}`);
    }
  }

  async queueBatchExternalUpload(
    uploads: ExternalUploadJobData[],
  ): Promise<string[]> {
    try {
      const jobs = uploads.map((data, index) => ({
        name: 'upload-to-external',
        data,
        opts: {
          priority: 1,
          attempts: 3,
          backoff: { type: 'exponential', delay: 2000 },
          removeOnComplete: 10,
          removeOnFail: 5,
          delay: index * 100, // Slight delay to prevent overwhelming
        },
      }));

      const bulkJobs = await this.externalUploadQueue.addBulk(jobs);
      const jobIds = bulkJobs.map((job) => job.id as string);

      this.logger.log(`Queued ${jobIds.length} external upload jobs`);
      return jobIds;
    } catch (error) {
      this.logger.error(
        `Failed to queue batch external upload: ${error.message}`,
      );
      throw new Error(
        `Failed to queue batch external upload: ${error.message}`,
      );
    }
  }

  async getJobStatus(jobId: string) {
    try {
      const job = await this.externalUploadQueue.getJob(jobId);
      if (!job) {
        return { status: 'not_found' };
      }

      const state = await job.getState();
      return {
        status: state,
        progress: job.progress,
        data: job.data,
        result: job.returnvalue,
        error: job.failedReason,
        attempts: job.attemptsMade,
        timestamp: job.timestamp,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get job status for ${jobId}: ${error.message}`,
      );
      throw new Error(`Failed to get job status: ${error.message}`);
    }
  }

  async cancelJob(jobId: string): Promise<boolean> {
    try {
      const job = await this.externalUploadQueue.getJob(jobId);
      if (!job) {
        return false;
      }

      await job.remove();
      this.logger.log(`Cancelled external upload job: ${jobId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to cancel job ${jobId}: ${error.message}`);
      return false;
    }
  }

  async getQueueStats() {
    try {
      const waiting = await this.externalUploadQueue.getWaiting();
      const active = await this.externalUploadQueue.getActive();
      const completed = await this.externalUploadQueue.getCompleted();
      const failed = await this.externalUploadQueue.getFailed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
      };
    } catch (error) {
      this.logger.error(`Failed to get queue stats: ${error.message}`);
      throw new Error(`Failed to get queue stats: ${error.message}`);
    }
  }
}
