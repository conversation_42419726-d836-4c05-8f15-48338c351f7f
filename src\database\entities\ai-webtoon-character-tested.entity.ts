import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';

import { EAiWebtoonCharacterTestedType } from 'src/common/ai-webtoon.enum';
import { AiWebtoonCharacterEntity } from './ai-webtoon-character.entity';
import { DefaultEntity } from './default.entity';
import { AdminEntity } from './admin.entity';

@Entity('ai_webtoon_character_tested')
export class AiWebtoonCharacterTestedEntity extends DefaultEntity {
  @Column({ name: 'ai_webtoon_character_id_1', type: 'int', nullable: true })
  aiWebtoonCharacterId1?: number;

  @Column({ name: 'ai_webtoon_character_id_2', type: 'int', nullable: true })
  aiWebtoonCharacterId2?: number;

  @Column({ name: 'admin_id' })
  adminId: number;

  @Column({ nullable: true, type: 'text' })
  prompt?: string;

  @Column({ nullable: true, type: 'text' })
  bg?: string;

  @Column({ name: 'prompt_character_1', nullable: true, type: 'text' })
  promptCharacter1?: string;

  @Column({ name: 'prompt_character_2', nullable: true, type: 'text' })
  promptCharacter2?: string;

  @Column({ type: 'json', default: () => '(JSON_ARRAY())' })
  images: string[];

  @Column({
    type: 'enum',
    enum: EAiWebtoonCharacterTestedType,
    default: EAiWebtoonCharacterTestedType.ONE,
  })
  type: EAiWebtoonCharacterTestedType;

  @ManyToOne(() => AiWebtoonCharacterEntity)
  @JoinColumn({ name: 'ai_webtoon_character_id_1' })
  aiWebtoonCharacter1?: AiWebtoonCharacterEntity;

  @ManyToOne(() => AiWebtoonCharacterEntity)
  @JoinColumn({ name: 'ai_webtoon_character_id_2' })
  aiWebtoonCharacter2?: AiWebtoonCharacterEntity;

  @ManyToOne(() => AdminEntity)
  @JoinColumn({ name: 'admin_id' })
  admin: AdminEntity;
}
