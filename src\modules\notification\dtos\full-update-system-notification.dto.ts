import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, MaxLength } from 'class-validator';
import { NotificationStatus } from '../../../database/entities/system-notification.entity';

export class FullUpdateSystemNotificationDto {
  @ApiProperty({
    description: 'Notification name',
    example: 'system_maintenance',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    description: 'Notification title',
    example: 'System Maintenance Notice',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  title?: string;

  @ApiProperty({
    description: 'Notification content (HTML allowed)',
    example: '<p>System will be under maintenance from 2PM to 4PM.</p>',
    required: false,
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({
    enum: NotificationStatus,
    example: NotificationStatus.ACTIVE,
    description: 'Notification status',
    required: false,
  })
  @IsOptional()
  @IsEnum(NotificationStatus)
  status?: NotificationStatus;
}
