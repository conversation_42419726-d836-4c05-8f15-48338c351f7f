import { Injectable } from '@nestjs/common';
import { AnalyticsQueueService } from '../../analytics/services/analytics-queue.service';

/**
 * Example integration for Content Analytics tracking
 * This shows how to integrate analytics tracking in content-related operations
 */
@Injectable()
export class ContentAnalyticsIntegrationExample {
  constructor(private readonly analyticsQueueService: AnalyticsQueueService) {}

  /**
   * Example: Track content view when user views content detail
   * This should be called after successful content view
   */
  async trackContentView(
    userId: number,
    contentId: number,
    episodeId?: number,
    platform?: string,
  ): Promise<void> {
    await this.analyticsQueueService.trackContentView({
      userId,
      contentId,
      episodeId,
      viewTime: Date.now(),
      platform: platform || 'pc',
      metadata: {
        source: 'content_detail',
        userAgent: 'browser-info',
      },
    });
  }

  /**
   * Example: Track content view with read duration
   * This should be called when user finishes reading
   */
  async trackContentViewWithDuration(
    userId: number,
    contentId: number,
    episodeId: number,
    readDuration: number,
    platform?: string,
  ): Promise<void> {
    await this.analyticsQueueService.trackContentView({
      userId,
      contentId,
      episodeId,
      viewTime: Date.now(),
      readDuration,
      platform: platform || 'pc',
      metadata: {
        source: 'episode_reading',
        completed: true,
      },
    });
  }

  /**
   * Example: Batch track multiple content views
   * Useful for recently watched content updates
   */
  async batchTrackContentViews(
    userId: number,
    contentViews: Array<{
      contentId: number;
      episodeId?: number;
      viewTime: number;
      readDuration?: number;
    }>,
    platform?: string,
  ): Promise<void> {
    const events = contentViews.map((view) => ({
      userId,
      contentId: view.contentId,
      episodeId: view.episodeId,
      viewTime: view.viewTime,
      readDuration: view.readDuration,
      platform: platform || 'pc',
      metadata: {
        source: 'batch_update',
      },
    }));

    await this.analyticsQueueService.batchTrackContentViews(events);
  }
}

/**
 * Example integration in ContentController
 *
 * @Controller('content')
 * export class ContentController {
 *   constructor(
 *     private readonly contentManagementService: ContentManagementService,
 *     private readonly analyticsQueueService: AnalyticsQueueService,
 *   ) {}
 *
 *   @Get('detail/:id')
 *   @UseGuards(OptionalJwtAuthGuard)
 *   async getContentDetail(@Param('id') id: number, @Req() req: any) {
 *     const result = await this.contentManagementService.getContentDetailForUser(
 *       id,
 *       req?.user?.userId,
 *     );
 *
 *     // Track content view analytics
 *     if (req?.user?.userId) {
 *       await this.analyticsQueueService.trackContentView({
 *         userId: req.user.id,
 *         contentId: id,
 *         viewTime: Date.now(),
 *         platform: req.headers['user-agent']?.includes('Mobile') ? 'mobile' : 'pc',
 *         metadata: {
 *           source: 'content_detail',
 *           userAgent: req.headers['user-agent'],
 *         },
 *       });
 *     }
 *
 *     return result;
 *   }
 *
 *   @Post('recently-watched')
 *   @UseGuards(JwtAuthGuard)
 *   async addRecentlyWatched(
 *     @Body() dto: MultiRecentlyWatchedContentDto,
 *     @Req() req: any,
 *   ) {
 *     const result = await this.contentManagementService.addRecentlyWatchedContent(
 *       req.user.id,
 *       dto,
 *     );
 *
 *     // Track content view analytics
 *     const events = dto.contents.map(content => ({
 *       userId: req.user.id,
 *       contentId: content.contentId,
 *       episodeId: content.episodeId,
 *       viewTime: Date.now(),
 *       platform: req.headers['user-agent']?.includes('Mobile') ? 'mobile' : 'pc',
 *       metadata: {
 *         source: 'recently_watched',
 *       },
 *     }));
 *
 *     await this.analyticsQueueService.batchTrackContentViews(events);
 *
 *     return result;
 *   }
 * }
 */
