import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeAdminTable1750836926846 implements MigrationInterface {
  name = 'ChangeAdminTable1750836926846';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_ccd95cb5ae13f3646470c5e650\` ON \`admins\``,
    );
    await queryRunner.query(
      `CREATE TABLE \`sessions\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`ipAddress\` varchar(255) NOT NULL, \`userAgent\` varchar(255) NULL, \`platform\` varchar(255) NULL, \`browser\` varchar(255) NULL, \`refreshToken\` varchar(255) NULL, \`refreshTokenExpiresAt\` int NULL, \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active', \`userId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`sessions\` ADD CONSTRAINT \`FK_57de40bc620f456c7311aa3a1e6\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`sessions\` DROP FOREIGN KEY \`FK_57de40bc620f456c7311aa3a1e6\``,
    );
    await queryRunner.query(`DROP TABLE \`sessions\``);
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`IDX_ccd95cb5ae13f3646470c5e650\` ON \`admins\` (\`username\`, \`email\`)`,
    );
  }
}
