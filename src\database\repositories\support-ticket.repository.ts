import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  SupportTicketEntity,
  TicketStatus,
} from '../entities/support-ticket.entity';
import { ResponseStatus } from '../entities/support-response.entity';

@Injectable()
export class SupportTicketRepository extends Repository<SupportTicketEntity> {
  constructor(
    @InjectRepository(SupportTicketEntity)
    private repository: Repository<SupportTicketEntity>,
  ) {
    super(repository.target, repository.manager, repository.queryRunner);
  }

  async findUserTickets(
    userId: number,
    page: number = 1,
    limit: number = 10,
    filters?: {
      status?: TicketStatus;
      category?: string;
      search?: string;
      isRead?: boolean;
    },
  ) {
    const queryBuilder = this.createQueryBuilder('ticket')
      .leftJoinAndSelect('ticket.category', 'category')
      .leftJoinAndSelect(
        'ticket.responses',
        'response',
        'response.status IN (:...completedStatuses)',
        {
          completedStatuses: [
            ResponseStatus.COMPLETE,
            ResponseStatus.COMPLETE_ADD,
            ResponseStatus.AUTOMATED_REPLY,
          ],
        },
      )
      .leftJoinAndSelect('response.admin', 'admin')
      .where('ticket.userId = :userId', { userId })
      .andWhere('ticket.deletedAt IS NULL');

    if (filters?.status) {
      queryBuilder.andWhere('ticket.status = :status', {
        status: filters.status,
      });
    }

    if (filters?.category) {
      queryBuilder.andWhere('category.name = :categoryName', {
        categoryName: filters.category,
      });
    }

    if (filters?.search) {
      queryBuilder.andWhere(
        '(ticket.subject LIKE :search OR ticket.description LIKE :search)',
        { search: `%${filters.search}%` },
      );
    }

    if (filters?.isRead !== undefined) {
      queryBuilder.andWhere('ticket.isRead = :isRead', {
        isRead: filters.isRead,
      });
    }

    return await queryBuilder
      .orderBy('ticket.createdAt', 'DESC')
      .addOrderBy('response.createdAt', 'ASC')
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();
  }

  async findUserTicketById(ticketId: number, userId: number) {
    return await this.createQueryBuilder('ticket')
      .leftJoinAndSelect('ticket.category', 'category')
      .leftJoinAndSelect(
        'ticket.responses',
        'response',
        'response.status IN (:...completedStatuses)',
        {
          completedStatuses: [
            ResponseStatus.COMPLETE,
            ResponseStatus.COMPLETE_ADD,
            ResponseStatus.AUTOMATED_REPLY,
          ],
        },
      )
      .leftJoinAndSelect('response.admin', 'admin')
      .where('ticket.id = :ticketId', { ticketId })
      .andWhere('ticket.userId = :userId', { userId })
      .andWhere('ticket.deletedAt IS NULL')
      .orderBy('response.createdAt', 'ASC')
      .getOne();
  }

  async findAdminTickets(
    page: number = 1,
    limit: number = 10,
    filters?: {
      status?: TicketStatus;
      priority?: string;
      category?: string;
      assignedTo?: number | 'unassigned' | 'me';
      dateType?: 'created' | 'updated' | 'resolved';
      fromDate?: string;
      toDate?: string;
      searchType?: 'subject' | 'description' | 'ticket_number' | 'user_email';
      keyword?: string;
      isRead?: boolean;
    },
  ) {
    const queryBuilder = this.createQueryBuilder('ticket')
      .leftJoinAndSelect('ticket.category', 'category')
      .leftJoinAndSelect('ticket.user', 'user')
      .leftJoinAndSelect('ticket.assignedAdmin', 'assignedAdmin')
      .leftJoinAndSelect('ticket.responses', 'response')
      .leftJoinAndSelect('response.admin', 'responseAdmin')
      .where('ticket.deletedAt IS NULL');

    // Status filter
    if (filters?.status) {
      queryBuilder.andWhere('ticket.status = :status', {
        status: filters.status,
      });
    }

    // Priority filter
    if (filters?.priority) {
      queryBuilder.andWhere('ticket.priority = :priority', {
        priority: filters.priority,
      });
    }

    // Category filter
    if (filters?.category) {
      queryBuilder.andWhere('category.name = :categoryName', {
        categoryName: filters.category,
      });
    }

    // Assignment filter
    if (filters?.assignedTo === 'unassigned') {
      queryBuilder.andWhere('ticket.assignedAdminId IS NULL');
    } else if (typeof filters?.assignedTo === 'number') {
      queryBuilder.andWhere('ticket.assignedAdminId = :assignedTo', {
        assignedTo: filters.assignedTo,
      });
    }

    // Date filters
    if (filters?.fromDate && filters?.toDate) {
      const dateField =
        filters.dateType === 'updated'
          ? 'ticket.updatedAt'
          : 'ticket.createdAt';
      queryBuilder.andWhere(`${dateField} BETWEEN :fromDate AND :toDate`, {
        fromDate: filters.fromDate,
        toDate: filters.toDate,
      });
    }

    // Search filter
    if (filters?.keyword && filters?.searchType) {
      switch (filters.searchType) {
        case 'subject':
          queryBuilder.andWhere('ticket.subject LIKE :keyword', {
            keyword: `%${filters.keyword}%`,
          });
          break;
        case 'description':
          queryBuilder.andWhere('ticket.description LIKE :keyword', {
            keyword: `%${filters.keyword}%`,
          });
          break;
        case 'ticket_number':
          queryBuilder.andWhere('ticket.ticketNumber LIKE :keyword', {
            keyword: `%${filters.keyword}%`,
          });
          break;
        case 'user_email':
          queryBuilder.andWhere('user.email LIKE :keyword', {
            keyword: `%${filters.keyword}%`,
          });
          break;
      }
    }

    // isRead filter
    if (filters?.isRead !== undefined) {
      queryBuilder.andWhere('ticket.isRead = :isRead', {
        isRead: filters.isRead,
      });
    }

    return await queryBuilder
      .orderBy('ticket.createdAt', 'DESC')
      .addOrderBy('response.createdAt', 'ASC')
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();
  }

  async generateTicketNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const count = await this.createQueryBuilder('ticket')
      .where('YEAR(ticket.createdAt) = :year', { year })
      .getCount();

    const nextNumber = (count + 1).toString().padStart(4, '0');
    return `SUP-${year}-${nextNumber}`;
  }

  async findWithCompletedResponses(ticketId: number, userId: number) {
    const ticket = await this.findUserTicketById(ticketId, userId);

    if (ticket) {
      // Check if user has unread responses
      const hasCompletedResponse =
        ticket.responses && ticket.responses.length > 0;
      return { ticket, hasCompletedResponse };
    }

    return { ticket: null, hasCompletedResponse: false };
  }
}
