import { Injectable } from '@nestjs/common';
import { ExternalUploadQueueService } from '../queues/services/external-upload-queue.service';
import { UploadContext } from '../../../database/entities/upload.entity';

/**
 * Example usage of External Upload Queue in different modules
 * This shows how other modules can integrate with the external upload functionality
 */

@Injectable()
export class ContentManagementExampleService {
  constructor(private externalUploadQueue: ExternalUploadQueueService) {}

  /**
   * Upload content cover image to external CDN
   */
  async uploadContentCover(
    imagePath: string,
    contentId: number,
  ): Promise<string> {
    const jobId = await this.externalUploadQueue.queueExternalUpload({
      type: UploadContext.CONTENT,
      imagePath: '/uploads/admin/content/cover_1234567_abc.jpg',
      uploadUrl: 'https://cdn-service.com/api/upload',
      metadata: {
        contentId,
        description: 'Content cover image',
        category: 'cover',
      },
    });

    return jobId;
  }

  /**
   * Batch upload episode images
   */
  async batchUploadEpisodeImages(
    episodes: Array<{ imagePath: string; episodeId: number }>,
  ): Promise<string[]> {
    const uploads = episodes.map((episode) => ({
      type: UploadContext.CONTENT,
      imagePath: episode.imagePath,
      uploadUrl: 'https://cdn-service.com/api/upload',
      metadata: {
        episodeId: episode.episodeId,
        category: 'episode',
        batchUpload: true,
      },
    }));

    return await this.externalUploadQueue.queueBatchExternalUpload(uploads);
  }
}

@Injectable()
export class UserProfileExampleService {
  constructor(private externalUploadQueue: ExternalUploadQueueService) {}

  /**
   * Upload user profile picture to external storage
   */
  async uploadProfilePicture(
    imagePath: string,
    userId: number,
  ): Promise<string> {
    const jobId = await this.externalUploadQueue.queueExternalUpload({
      type: UploadContext.PROFILE,
      imagePath: `/uploads/users/${userId}/profile/avatar_${Date.now()}.jpg`,
      uploadUrl: 'https://storage-service.com/api/user-avatars',
      metadata: {
        userId,
        description: 'User profile picture',
        category: 'avatar',
      },
    });

    return jobId;
  }
}

@Injectable()
export class SettingsExampleService {
  constructor(private externalUploadQueue: ExternalUploadQueueService) {}

  /**
   * Upload app settings images (logos, banners, etc.)
   */
  async uploadSettingsImage(
    imagePath: string,
    settingType: string,
  ): Promise<string> {
    const jobId = await this.externalUploadQueue.queueExternalUpload({
      type: UploadContext.SETTINGS,
      imagePath,
      uploadUrl: 'https://static-assets.com/api/settings-upload',
      metadata: {
        settingType,
        description: `App settings ${settingType}`,
        category: 'settings',
      },
    });

    return jobId;
  }
}

/**
 * Job Status Monitoring Example
 */
@Injectable()
export class UploadMonitoringService {
  constructor(private externalUploadQueue: ExternalUploadQueueService) {}

  async monitorUploadJob(jobId: string) {
    try {
      const status = await this.externalUploadQueue.getJobStatus(jobId);

      switch (status.status) {
        case 'waiting':
          console.log(`Job ${jobId} is waiting in queue`);
          break;
        case 'active':
          console.log(
            `Job ${jobId} is being processed, progress: ${status.progress}%`,
          );
          break;
        case 'completed':
          console.log(`Job ${jobId} completed successfully:`, status.result);
          break;
        case 'failed':
          console.log(`Job ${jobId} failed:`, status.error);
          break;
        case 'not_found':
          console.log(`Job ${jobId} not found`);
          break;
      }

      return status;
    } catch (error) {
      console.error(`Error monitoring job ${jobId}:`, error);
      throw error;
    }
  }

  async getQueueStatistics() {
    const stats = await this.externalUploadQueue.getQueueStats();
    console.log('External Upload Queue Statistics:', stats);
    return stats;
  }
}

/**
 * Module Integration Example
 *
 * To use External Upload Queue in your module:
 *
 * 1. Import UploadModule in your module:
 *
 * @Module({
 *   imports: [UploadModule],
 *   providers: [YourService],
 * })
 * export class YourModule {}
 *
 * 2. Inject ExternalUploadQueueService in your service:
 *
 * @Injectable()
 * export class YourService {
 *   constructor(
 *     private externalUploadQueue: ExternalUploadQueueService,
 *   ) {}
 * }
 *
 * 3. Environment Variables needed:
 *
 * EXTERNAL_UPLOAD_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----..."
 * EXTERNAL_UPLOAD_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----..."
 * EXTERNAL_UPLOAD_TIMEOUT=30000
 */
