import { Injectable } from '@nestjs/common';
import { SocketGateway } from './socket.gateway';
import { EAiGenPromptByDescriptionType } from 'src/common/ai-webtoon.enum';

@Injectable()
export class SocketService {
  constructor(private readonly socketGateway: SocketGateway) { }

  async emitUpdateChapter(chapterId: number): Promise<T> {
    await this.socketGateway.emitUpdateChapter(chapterId);
  }

  emitSyntheticData(syntheticDataId: number) {
    this.socketGateway.emitSyntheticData(syntheticDataId);
  }

  emitPromptByDescription(
    cutId: number,
    typePrompt: EAiGenPromptByDescriptionType,
    prompt: string,
  ): void {
    this.socketGateway.emitPromptByDescription(cutId, typePrompt, prompt);
  }

  emitMergePreviewImages(chapterId: number, images: string[]): void {
    this.socketGateway.emitMergePreviewImages(chapterId, images);
  }
}
