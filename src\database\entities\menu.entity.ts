import { Column, Entity, OneToMany } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { MenuStatus } from '../../common/status.enum';
import { MenuAdminEntity } from './menu-admin.entity';

@Entity('menu')
export class MenuEntity extends DefaultEntity {
  @Column({ unique: true })
  key: string;

  @Column()
  label: string;

  @Column()
  icon: string;

  @Column()
  route: string;

  @Column({ default: 0 })
  order: number;

  @Column({ type: 'enum', enum: MenuStatus, default: MenuStatus.ACTIVE })
  status: MenuStatus;

  @Column({ default: null })
  parentId: number;

  @OneToMany(() => MenuAdminEntity, (menu) => menu.menu)
  menuAdmin: MenuAdminEntity[];
}
