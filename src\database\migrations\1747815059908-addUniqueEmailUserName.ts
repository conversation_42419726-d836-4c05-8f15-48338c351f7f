import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUniqueEmailUserName1747815059908 implements MigrationInterface {
  name = 'AddUniqueEmailUserName1747815059908';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`IDX_ccd95cb5ae13f3646470c5e650\` ON \`admins\` (\`username\`, \`email\`)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_ccd95cb5ae13f3646470c5e650\` ON \`admins\``,
    );
  }
}
