import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsArray,
  IsEnum,
  MaxLength,
} from 'class-validator';

export enum ImageStorageType {
  PROFILE = 'profile',
  CONTENT = 'content',
  SUPPORT = 'support',
  SETTINGS = 'settings',
  GENERAL = 'general',
  BULK = 'bulk',
}

export class UploadImageDto {
  @ApiProperty({
    description: 'Image file',
    type: 'string',
    format: 'binary',
  })
  image: any;

  @ApiProperty({
    description: 'Hex-encoded signature of image buffer',
    example: 'a1b2c3d4e5f6...',
  })
  @IsString()
  signature: string;

  @ApiProperty({
    description: 'Public key for signature verification',
    example: '-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----',
  })
  @IsString()
  publicKey: string;

  @ApiProperty({
    description: 'Storage type to determine directory structure',
    enum: ImageStorageType,
    example: ImageStorageType.PROFILE,
  })
  @IsEnum(ImageStorageType)
  type: ImageStorageType;

  @ApiProperty({
    description:
      'Target path where the image should be saved (relative to storage root)',
    example: 'episodes/season1/episode001.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  targetPath: string;

  @ApiProperty({
    description: 'Webhook URL to notify on completion',
    example: 'https://api.myapp.com/webhooks/image-upload',
  })
  @IsString()
  @IsOptional()
  webhookUrl?: string;

  @ApiProperty({
    description: 'Original filename',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  filename?: string;

  @ApiProperty({
    description: 'Image tags',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'Image description',
    required: false,
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiProperty({
    description: 'Album ID for grouping',
    required: false,
  })
  @IsOptional()
  @IsString()
  albumId?: string;
}
