import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { SettingStatus } from '../../../../common/status.enum';

export class LanguageFilterDto {
  @ApiProperty({
    description: 'Language id (optional)',
    required: false,
  })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  id: number;

  @ApiProperty({
    description: 'Status (optional)',
    enum: SettingStatus,
    required: false,
  })
  @IsString()
  @IsOptional()
  status: string;
}
