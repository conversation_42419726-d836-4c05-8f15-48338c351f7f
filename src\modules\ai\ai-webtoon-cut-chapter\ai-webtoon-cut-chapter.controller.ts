import { Api<PERSON><PERSON>erAuth, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { AiWebtoonCutChapterService } from './ai-webtoon-cut-chapter.service';
import { CreateCutDto } from './dto/create-cut.dto';

import {
  UpdateNormalCutsDto,
  UpdateSelectBgImageInpaintCutDto,
} from './dto/update-cut.dto';
import { GenerateImages3Dto } from './dto/generate-images.dto';
import { ReceiveGenerateImageDto } from './dto/receive-generate-image.dto';
import { InpaintImageDto } from './dto/inpaint-image.dto';
import { ReceiveInpaintImageDto } from './dto/receive-inpaint-image.dto';
import { SelectImageByIndexDto } from './dto/select-image-by-index.dto';
import { UpdateEditImageDto } from './dto/update-edit-image.dto';
import {
  AddImageCutNormalDto,
  AddSketchImagesDto,
} from './dto/add-image-cut-normal.dto';
import { StopGenerateImagesDto } from './dto/stop-generate-images.dto';
import { GenerateSketchImageDto } from './dto/generate-sketch-image.dto';
import { CombineImagesDto } from './dto/combine-image.dto';
import {
  GeneratePromptDto,
  ReceiveGeneratePromptByDescriptionDto,
} from './dto/generate-prompt-by-description.dto';
import { UpdateOrderByPreviewCutsDto } from './dto/update-order-by-preview-cuts.dto';
import { UnselectSketchImagesDto } from './dto/unselect-sketch-image-by-index';
import { SelectSketchImageByIndexDto } from './dto/select-sketch-image-by-index.dto';
import { JwtAdminAuthGuard } from 'src/modules/admin/auth-admin/guards/jwt-admin-auth.guard';

@ApiTags('ai-webtoon-cut-chapter')
@Controller('ai-webtoon-cut-chapter')
export class AiWebtoonCutChapterController {
  constructor(
    private readonly aiWebtoonCutChapterService: AiWebtoonCutChapterService,
  ) {}

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('normal-cuts')
  updateNormalCuts(@Body() body: UpdateNormalCutsDto) {
    return this.aiWebtoonCutChapterService.updateNormalCuts(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('select-bg-image-inpaint/:id')
  updateSelectBgImageInpaintCut(
    @Body() body: UpdateSelectBgImageInpaintCutDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonCutChapterService.updateSelectBgImageInpaintCut(
      id,
      body,
    );
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Delete('delete-bg-image-inpaint/:id')
  deleteBgImageInpaintCut(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonCutChapterService.deleteBgImageInpaintCut(id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('select-image-by-index/:id')
  selectImageByIndex(
    @Body() body: SelectImageByIndexDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonCutChapterService.selectImageByIndex(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Delete('delete-image-by-index/:id')
  deleteImageByIndex(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: SelectImageByIndexDto,
  ) {
    return this.aiWebtoonCutChapterService.deleteImageByIndex(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('generate-images/:chapterId')
  generateImages(
    @Body() body: GenerateImages3Dto,
    @Param('chapterId', ParseIntPipe) chapterId: number,
  ) {
    return this.aiWebtoonCutChapterService.generateImages(chapterId, body);
  }

  @Post('/receive-generate-image')
  receiveGenerateImage(@Body() body: ReceiveGenerateImageDto) {
    return this.aiWebtoonCutChapterService.receiveGenerateImage(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('stop-generate-images')
  stopGenerateImages(@Body() body: StopGenerateImagesDto) {
    return this.aiWebtoonCutChapterService.stopGenerateImages(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('inpaint-image-by-cut-normal/:id')
  inpaintImageByCutNormal(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: InpaintImageDto,
  ) {
    return this.aiWebtoonCutChapterService.inpaintImageByCutNormal(id, body);
  }

  @Post('/receive-inpaint-image-by-normal-cut')
  receiveInpaintImageByNormalCut(@Body() body: ReceiveInpaintImageDto) {
    return this.aiWebtoonCutChapterService.receiveInpaintImageByNormalCut(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/inpaint-image/:id')
  inpaintImage(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: InpaintImageDto,
  ) {
    return this.aiWebtoonCutChapterService.inpaintImage(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('stop-inpaint-image/:id')
  stopInpaintImage(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonCutChapterService.stopInpaintImage(id);
  }

  @Post('/receive-inpaint-image')
  receiveInpaintImage(@Body() body: ReceiveInpaintImageDto) {
    return this.aiWebtoonCutChapterService.receiveInpaintImage(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('generate-sketch-image')
  generateSketchImage(@Body() body: GenerateSketchImageDto) {
    return this.aiWebtoonCutChapterService.generateSketchImage(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('sketch-inpaint-image/:id')
  sketchInpaintImage(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: InpaintImageDto,
  ) {
    return this.aiWebtoonCutChapterService.sketchInpaintImage(id, body);
  }

  @Post('/receive-sketch-inpaint-image')
  receiveSketchInpaintImage(@Body() body: ReceiveInpaintImageDto) {
    return this.aiWebtoonCutChapterService.receiveSketchInpaintImage(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('stop-sketch-inpaint-image/:uuid')
  stopSketchInpaintImage(@Param('uuid') uuid: string) {
    return this.aiWebtoonCutChapterService.stopSketchInpaintImage(uuid);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('get-sketch-inpaint-image/:uuid')
  getSketchInpaintImage(@Param('uuid') uuid: string) {
    return this.aiWebtoonCutChapterService.getSketchInpaintImage(uuid);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('add-image-cut-normal/:id')
  addImageCutNormal(
    @Body() body: AddImageCutNormalDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonCutChapterService.addImageCutNormal(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('edit-image/:id')
  editImage(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateEditImageDto,
  ) {
    return this.aiWebtoonCutChapterService.editImage(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('update-preview-cut/:id')
  updatePreviewCut(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonCutChapterService.updatePreviewCut(id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('select-sketch-image-by-index/:id')
  selectSketchImageByIndex(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: SelectSketchImageByIndexDto,
  ) {
    return this.aiWebtoonCutChapterService.selectSketchImageByIndex(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('unselect-sketch-image-by-index/:id')
  unselectSketchImageByIndex(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UnselectSketchImagesDto,
  ) {
    return this.aiWebtoonCutChapterService.unselectSketchImageByIndex(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('add-sketch-image/:id')
  addSketchImage(
    @Body() body: AddSketchImagesDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonCutChapterService.addSketchImage(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Delete('delete-sketch-image/:id')
  deleteSketchImage(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: SelectSketchImageByIndexDto,
  ) {
    return this.aiWebtoonCutChapterService.deleteSketchImage(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('combine-images')
  combineImages(@Body() body: CombineImagesDto) {
    return this.aiWebtoonCutChapterService.combineImages(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Delete('delete-order-generate-image/:aiWebtoonOrderGenerateCutChapterId')
  deleteOrderGenerateImage(
    @Param('aiWebtoonOrderGenerateCutChapterId', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonCutChapterService.deleteOrderGenerateImage(id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('generate-prompt-by-description/:id')
  generatePromptByDescription(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: GeneratePromptDto,
  ) {
    return this.aiWebtoonCutChapterService.generatePromptByDescription(
      id,
      body,
    );
  }

  @Post('/receive-generate-prompt-by-description')
  receiveGeneratePromptByDescription(
    @Body() body: ReceiveGeneratePromptByDescriptionDto,
  ) {
    return this.aiWebtoonCutChapterService.receiveGeneratePromptByDescription(
      body,
    );
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('update-order-by-preview-cuts/:chapterId')
  updateOrderByPreviewCuts(
    @Body() body: UpdateOrderByPreviewCutsDto,
    @Param('chapterId', ParseIntPipe) chapterId: number,
  ) {
    return this.aiWebtoonCutChapterService.updateOrderByPreviewCuts(
      chapterId,
      body,
    );
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/:sceneId')
  createCutByScene(
    @Body() body: CreateCutDto,
    @Param('sceneId', ParseIntPipe) sceneId: number,
  ) {
    return this.aiWebtoonCutChapterService.createCutByScene(sceneId, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Delete('/:id')
  deleteCut(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonCutChapterService.deleteCut(id);
  }
}
