import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  Min,
  ValidateNested,
} from 'class-validator';

export class ItemDataTrainCharacterDto {
  @IsNotEmpty()
  @IsString()
  @IsUrl()
  url: string;

  @IsString()
  prompt: string;
}

export class CharacterTrainingDto {
  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  id: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  descriptionPrompt: string;

  @ApiProperty({
    example: [
      {
        url: '',
        prompt: '',
      },
    ],
  })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => ItemDataTrainCharacterDto)
  data: ItemDataTrainCharacterDto[];
}
