import { DataSource, Like, Repository } from 'typeorm';
import { BadRequestException, Injectable } from '@nestjs/common';
import { AdminEntity } from '../entities/admin.entity';
import { AdminRole } from '../../common/role.enum';
import { plainToInstance } from 'class-transformer';
import { SearchAdminDto } from '../../modules/admin/auth-admin/dtos/search.dto';
import { AdminStatus } from '../../common/status.enum';
@Injectable()
export class AdminRepository extends Repository<AdminEntity> {
  constructor(private dataSource: DataSource) {
    super(AdminEntity, dataSource.createEntityManager());
  }

  async findByEmail(email: string) {
    return this.findOne({ where: { email } });
  }

  async findByForgotPasswordToken(token: string) {
    return this.findOne({ where: { forgotPasswordToken: token } });
  }

  async createAdmin(
    email: string,
    username: string,
    role: string,
    token: string,
    expiresIn: Date,
  ) {
    try {
      const admin = new AdminEntity();
      admin.forgotPasswordToken = token;
      admin.forgotPasswordTokenExpiresAt = expiresIn;
      admin.email = email;
      admin.username = username;
      admin.status = AdminStatus.INACTIVE;
      admin.role = role as AdminRole;

      const savedAdmin = await this.save(admin);
      return plainToInstance(AdminEntity, savedAdmin);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async findByUsername(username: string) {
    return this.findOne({ where: { username } });
  }

  async searchAdmin(searchDto: SearchAdminDto) {
    const { search } = searchDto;
    return this.find({
      where: {
        username: Like(`%${search}%`),
        email: Like(`%${search}%`),
      },
    });
  }
}
