import {
  EAiCutChapterType,
  EAiFrameImageGenerateType,
} from 'src/common/ai-webtoon.enum';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { AiWebtoonChapterEntity } from './ai-webtoon-chapter.entity';
import { DefaultEntity } from './default.entity';
import {
  ISendGenerateCutImagesRequest,
  ISendInpaintRequest,
} from 'src/common/interfaces/ai-webtoon/ai-send-request.interface';

@Entity('ai_webtoon_order_generate_cut_chapter')
export class AiWebtoonOrderGenerateCutChapterEntity extends DefaultEntity {
  @Column({ name: 'chapter_id' })
  chapterId: number;

  @Column({
    name: 'cuts_id',
    type: 'json',
    default: () => '(JSON_ARRAY())',
  })
  cutsId: number[];

  @Column({
    type: 'enum',
    enum: EAiCutChapterType,
    default: EAiCutChapterType.NORMAL,
  })
  type: EAiCutChapterType;

  @Column({
    name: 'cut_type',
    type: 'enum',
    enum: EAiCutChapterType,
    default: EAiCutChapterType.NORMAL,
  })
  cutType: EAiCutChapterType;

  @Column({
    name: 'image_generate_type',
    type: 'enum',
    enum: EAiFrameImageGenerateType,
    default: EAiFrameImageGenerateType.ONE,
  })
  imageGenerateType: EAiFrameImageGenerateType;

  @Column({
    type: 'json',
    default: () => '(JSON_OBJECT())',
  })
  data: ISendGenerateCutImagesRequest | ISendInpaintRequest;

  @Column({
    name: 'api_gen_name',
    type: 'varchar',
    nullable: true,
  })
  apiGenName?: string | null;

  @ManyToOne(() => AiWebtoonChapterEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'chapter_id' })
  chapter: AiWebtoonChapterEntity;
}
