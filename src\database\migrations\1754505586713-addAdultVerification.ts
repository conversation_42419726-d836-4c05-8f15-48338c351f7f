import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAdultVerification1754505586713 implements MigrationInterface {
  name = 'AddAdultVerification1754505586713';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`isAdultVerified\` tinyint NOT NULL DEFAULT 0`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP COLUMN \`isAdultVerified\``,
    );
  }
}
