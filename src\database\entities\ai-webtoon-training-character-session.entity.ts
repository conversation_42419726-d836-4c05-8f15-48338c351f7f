import { EAiWebtoonTrainingCharacterSessionStatus } from 'src/common/ai-webtoon.enum';
import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm';
import { AiWebtoonCharacterEntity } from './ai-webtoon-character.entity';
import { AiWebtoonTrainingLogEntity } from './ai-webtoon-training-log.entity';
import { DefaultEntity } from './default.entity';
import { IAiItemDataTrainCharacter } from 'src/common/interfaces/ai-webtoon/ai-webtoon-character.interface';

@Entity('ai_webtoon_training_character_session')
export class AiWebtoonTrainingCharacterSessionEntity extends DefaultEntity {
  @Column({ name: 'ai_webtoon_character_id' })
  aiWebtoonCharacterId: number;

  @Column({ type: 'json', default: () => '(JSON_ARRAY())' })
  images: IAiItemDataTrainCharacter[];

  @Column({ type: 'varchar', nullable: true })
  url?: string | null;

  @Column({
    type: 'enum',
    enum: EAiWebtoonTrainingCharacterSessionStatus,
    default: EAiWebtoonTrainingCharacterSessionStatus.IN_PROGRESS,
  })
  status: EAiWebtoonTrainingCharacterSessionStatus;

  @Column({ name: 'end_date', type: 'datetime', nullable: true })
  endDate?: Date | null;

  @Column({ name: 'description_prompt', type: 'text', nullable: true })
  descriptionPrompt?: string | null;

  @ManyToOne(
    () => AiWebtoonCharacterEntity,
    (aiWebtoonCharacter) => aiWebtoonCharacter.session,
  )
  @JoinColumn({ name: 'ai_webtoon_character_id' })
  aiWebtoonCharacter: AiWebtoonCharacterEntity;

  @OneToMany(
    () => AiWebtoonTrainingLogEntity,
    (aiWebtoonTrainingLog) => aiWebtoonTrainingLog.aiTrainingSession,
  )
  logs: AiWebtoonTrainingLogEntity[];

  countImage: number;
  loss: number | null;
  percent: number | null;
}
