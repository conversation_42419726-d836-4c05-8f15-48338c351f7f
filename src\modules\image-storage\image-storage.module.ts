import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { MulterModule } from '@nestjs/platform-express';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { memoryStorage } from 'multer';

import { ImageStorageController } from './controllers/image-storage.controller';
import { StatusController } from './controllers/status.controller';
import { ImageStorageService } from './services/image-storage.service';
import { SignatureService } from './services/signature.service';
import { StorageService } from './services/storage.service';
import { ImageProcessingService } from './services/image-processing.service';
import { WebhookService } from './services/webhook.service';
import { StatusService } from './services/status.service';
import { ImageStorageProcessor } from './processors/image-storage.processor';
import configurationGlobal from '../../config/configuration.global';

@Module({
  imports: [
    ConfigModule,
    HttpModule,
    BullModule.forRoot({
      connection: {
        host: configurationGlobal().redis.host,
        port: configurationGlobal().redis.port,
      },
    }),
    BullModule.registerQueue({
      name: 'image-storage',
    }),
    MulterModule.register({
      storage: memoryStorage(),
      limits: {
        fileSize: 50 * 1024 * 1024, // 50MB
      },
    }),
  ],
  controllers: [ImageStorageController, StatusController],
  providers: [
    ImageStorageService,
    SignatureService,
    StorageService,
    ImageProcessingService,
    WebhookService,
    StatusService,
    ImageStorageProcessor,
  ],
  exports: [ImageStorageService, StatusService],
})
export class ImageStorageModule {}
