import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeSessionTable1751008135808 implements MigrationInterface {
  name = 'ChangeSessionTable1751008135808';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`sessions\` ADD \`uuid\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`sessions\` ADD \`lastUpdateTimestamp\` int NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`sessions\` DROP COLUMN \`lastUpdateTimestamp\``,
    );
    await queryRunner.query(`ALTER TABLE \`sessions\` DROP COLUMN \`uuid\``);
  }
}
