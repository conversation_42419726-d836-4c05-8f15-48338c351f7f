import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { AdminRepository } from '../../../database/repositories/admin.repository';
import { BadRequestException } from '@nestjs/common';
import { AdminLoginDto } from './dtos/login.dto';
import { CreateAdminDto } from './dtos/create-admin.dto';
import { SearchAdminDto } from './dtos/search.dto';
import { MESSAGE_CONFIG } from '../../../common/message.config';
import { CommonService } from '../../../common/common.service';
import configuration from '../../../config/configuration.global';
import { AdminEntity } from '../../../database/entities/admin.entity';
import * as moment from 'moment';
import { ForgotPasswordDto } from '../../auth/dtos/forgot-password.dto';
import { ResetPasswordDto } from '../../auth/dtos/reset-password.dto';
import { AdminStatus } from '../../../common/status.enum';
import { plainToInstance } from 'class-transformer';
@Injectable()
export class AuthAdminService {
  constructor(
    private jwtService: JwtService,
    private adminRepository: AdminRepository,
    private commonService: CommonService,
  ) {}

  async validateUser(username: string, password: string) {
    const user = await this.adminRepository.findByUsername(username);
    if (
      user &&
      (await bcrypt.compare(password, user.password)) &&
      user.status === AdminStatus.ACTIVE
    ) {
      return user;
    }
    return null;
  }

  async login(loginDto: AdminLoginDto) {
    const payload = {
      username: loginDto.username,
      password: loginDto.password,
    };
    const user = await this.validateUser(payload.username, payload.password);
    if (!user) {
      throw new UnauthorizedException(MESSAGE_CONFIG.INVALID_CREDENTIALS);
    }

    const userData = {
      id: user.id,
      email: user.email,
      username: user.username,
      role: user.role,
    };
    return {
      access_token: this.jwtService.sign(userData),
    };
  }

  async createAdmin(createAdminDto: CreateAdminDto) {
    try {
      const user = await this.adminRepository.findByEmail(createAdminDto.email);
      if (user) {
        throw new BadRequestException(MESSAGE_CONFIG.EMAIL_ALREADY_EXISTS);
      }

      const userByUsername = await this.adminRepository.findByUsername(
        createAdminDto.username,
      );
      if (userByUsername) {
        throw new BadRequestException(MESSAGE_CONFIG.USERNAME_ALREADY_EXISTS);
      }

      const configExpiresIn = configuration().auth.forgotPassword.expiresIn;
      const token = this.jwtService.sign(
        { email: createAdminDto.email },
        { expiresIn: configExpiresIn },
      );
      const expiresIn = moment().add(configExpiresIn, 'hours').toDate();

      const admin = await this.adminRepository.createAdmin(
        createAdminDto.email,
        createAdminDto.username,
        createAdminDto.role,
        token,
        expiresIn,
      );

      await this.commonService.sendEmail(
        createAdminDto.email,
        'Admin Created',
        'forgot-password',
        {
          url: `${configuration().url.admin}/set-password/${admin.forgotPasswordToken}`,
          urlLogo: `${configuration().url.admin}/logo.png`,
        },
      );

      return {
        message: MESSAGE_CONFIG.ADMIN_CREATED_SUCCESSFULLY,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async searchAdmin(searchDto: SearchAdminDto) {
    return await this.adminRepository.searchAdmin(searchDto);
  }

  async checkForgotPasswordToken(token: string) {
    const user = await this.adminRepository.findByForgotPasswordToken(token);

    if (!user) {
      throw new BadRequestException(
        MESSAGE_CONFIG.FORGOT_PASSWORD_TOKEN_NOT_FOUND,
      );
    }
    const tokenDecode = this.jwtService.decode(token);
    if (moment(user.forgotPasswordTokenExpiresAt).isBefore(moment())) {
      throw new BadRequestException(MESSAGE_CONFIG.TOKEN_EXPIRED);
    }
    if (tokenDecode.email !== user.email) {
      throw new BadRequestException(MESSAGE_CONFIG.INVALID_TOKEN);
    }
    return plainToInstance(AdminEntity, user);
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    try {
      const user = await this.adminRepository.findByEmail(
        forgotPasswordDto.email,
      );
      if (!user) {
        throw new BadRequestException(MESSAGE_CONFIG.USER_NOT_FOUND);
      }

      if (user.forgotPasswordToken) {
        if (moment(user.forgotPasswordTokenExpiresAt).isAfter(moment())) {
          throw new BadRequestException(
            MESSAGE_CONFIG.FORGOT_PASSWORD_TOKEN_ALREADY_EXISTS,
          );
        }
      }
      const configExpiresIn = configuration().auth.forgotPassword.expiresIn;

      const token = this.jwtService.sign(
        { email: user.email },
        { expiresIn: configExpiresIn },
      );
      const expiresIn = moment().add(configExpiresIn, 'hours').toDate();
      user.forgotPasswordToken = token;
      user.forgotPasswordTokenExpiresAt = expiresIn;
      user.status = AdminStatus.ACTIVE;
      await this.commonService.sendEmail(
        user.email,
        'Forgot Password',
        'forgot-password',
        {
          url: `${configuration().url.admin}/reset-password/${token}`,
          urlLogo: `${configuration().url.admin}/logo.png`,
        },
      );
      await this.adminRepository.save(user);

      return {
        message: MESSAGE_CONFIG.FORGOT_PASSWORD_MAIL_SENT,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    try {
      const user = await this.adminRepository.findByForgotPasswordToken(
        resetPasswordDto.token,
      );
      if (!user) {
        throw new BadRequestException(
          MESSAGE_CONFIG.FORGOT_PASSWORD_TOKEN_NOT_FOUND,
        );
      }
      if (user.forgotPasswordToken !== resetPasswordDto.token) {
        throw new BadRequestException(MESSAGE_CONFIG.INVALID_TOKEN);
      }
      if (moment(user.forgotPasswordTokenExpiresAt).isBefore(moment())) {
        throw new BadRequestException(MESSAGE_CONFIG.TOKEN_EXPIRED);
      }

      user.password = await bcrypt.hash(resetPasswordDto.password, 10);
      user.forgotPasswordToken = '';
      user.status = AdminStatus.ACTIVE;
      user.forgotPasswordTokenExpiresAt = new Date();
      await this.adminRepository.save(user);
      return {
        message: MESSAGE_CONFIG.PASSWORD_RESET_SUCCESSFULLY,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
