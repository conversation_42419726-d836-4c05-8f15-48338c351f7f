import { Entity, Column, ManyTo<PERSON>ne, JoinColumn, Index } from 'typeorm';
import { UserEntity } from './user.entity';
import { DefaultEntity } from './default.entity';

@Entity('account_deletion_audit')
@Index(['userId'])
@Index(['deletionId'])
@Index(['createdAt'])
export class AccountDeletionAuditEntity extends DefaultEntity {
  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'deletion_id', type: 'varchar', length: 36 })
  deletionId: string;

  @Column({ type: 'text', nullable: true })
  reason: string;

  @Column({ name: 'ip_address', type: 'varchar', length: 45, nullable: true })
  ipAddress: string;

  @Column({ name: 'user_agent', type: 'text', nullable: true })
  userAgent: string;

  @Column({
    name: 'deletion_status',
    type: 'enum',
    enum: ['INITIATED', 'COMPLETED', 'CANCELLED'],
    default: 'INITIATED',
  })
  deletionStatus: 'INITIATED' | 'COMPLETED' | 'CANCELLED';

  @Column({ name: 'scheduled_deletion_at', type: 'datetime', nullable: true })
  scheduledDeletionAt: Date;

  @Column({ name: 'completed_at', type: 'datetime', nullable: true })
  completedAt: Date;

  // Relations
  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;
}
