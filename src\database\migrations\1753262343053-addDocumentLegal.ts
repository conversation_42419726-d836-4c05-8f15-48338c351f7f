import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDocumentLegal1753262343053 implements MigrationInterface {
  name = 'AddDocumentLegal1753262343053';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`legal_document\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`title\` varchar(255) NOT NULL, \`name\` varchar(100) NOT NULL, \`admin_id\` int NOT NULL, \`view_count\` int NOT NULL DEFAULT '0', \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active', \`content\` text NOT NULL, \`type\` enum ('terms_of_use', 'privacy_policy') NOT NULL, \`version\` varchar(50) NOT NULL DEFAULT '1.0', INDEX \`IDX_4f139b5bb9176aa3dd9fa7fa1e\` (\`version\`), INDEX \`IDX_4a87ed083dc61e9a777ef6d5db\` (\`createdAt\`), INDEX \`IDX_73484cd8756e4a121b4ce26b6b\` (\`status\`, \`type\`), UNIQUE INDEX \`IDX_087019706dfb62b0ef54458305\` (\`name\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`legal_document\` ADD CONSTRAINT \`FK_79bbe957a06db5fa8e57fccf2aa\` FOREIGN KEY (\`admin_id\`) REFERENCES \`admins\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`legal_document\` DROP FOREIGN KEY \`FK_79bbe957a06db5fa8e57fccf2aa\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_087019706dfb62b0ef54458305\` ON \`legal_document\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_73484cd8756e4a121b4ce26b6b\` ON \`legal_document\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_4a87ed083dc61e9a777ef6d5db\` ON \`legal_document\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_4f139b5bb9176aa3dd9fa7fa1e\` ON \`legal_document\``,
    );
    await queryRunner.query(`DROP TABLE \`legal_document\``);
  }
}
