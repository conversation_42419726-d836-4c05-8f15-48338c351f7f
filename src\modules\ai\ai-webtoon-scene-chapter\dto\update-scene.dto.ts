import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { EAiFrameImageSizeType } from 'src/common/ai-webtoon.enum';

export class UpdateSceneDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  negativePrompt?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  prompt?: string;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  numberOfImages?: number;

  @ApiProperty({ enum: EAiFrameImageSizeType, required: false })
  @IsEnum(EAiFrameImageSizeType)
  @IsOptional()
  imageSizeType?: EAiFrameImageSizeType;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  width?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  height?: number;
}
