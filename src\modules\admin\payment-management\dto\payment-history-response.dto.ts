import { ApiProperty } from '@nestjs/swagger';

export class PaymentHistoryItemDto {
  @ApiProperty({
    description: 'Record sequence number',
    example: 1,
  })
  no: number;

  @ApiProperty({
    description: 'Purchase type',
    example: 'First Purchase',
  })
  purchaseType: string;

  @ApiProperty({
    description: 'Product type',
    example: 'Subscription',
  })
  productType: string;

  @ApiProperty({
    description: 'Reference code or referral link',
    example: 'REF123',
  })
  ref: string;

  @ApiProperty({
    description: 'Payment ID, usually user email',
    example: '<EMAIL>',
  })
  paymentId: string;

  @ApiProperty({
    description: 'Payment amount',
    example: 29.99,
  })
  paymentAmount: number;

  @ApiProperty({
    description: 'User registration date',
    example: '2025-01-15T10:30:00.000Z',
  })
  signUpDate: string;

  @ApiProperty({
    description: 'Purchase date',
    example: '2025-01-15T14:30:00.000Z',
  })
  purchaseDate: string;

  @ApiProperty({
    description: 'Transaction platform',
    example: 'Mobile',
  })
  platform: string;

  @ApiProperty({
    description: 'Payment method',
    example: 'Credit Card',
  })
  paymentType: string;

  @ApiProperty({
    description: 'User language',
    example: 'English',
  })
  language: string;

  @ApiProperty({
    description: 'Payment IP address',
    example: '***********',
  })
  paymentIp: string;

  @ApiProperty({
    description: 'Unique transaction code',
    example: 'TXN_123456789',
  })
  transactionId: string;

  @ApiProperty({
    description: 'Payment status',
    example: 'Normal',
  })
  paymentStatus: string;
}

export class PaymentHistoryResponseDto {
  @ApiProperty({
    description: 'Array of payment records',
    type: [PaymentHistoryItemDto],
  })
  payments: PaymentHistoryItemDto[];

  @ApiProperty({
    description: 'Total number of records',
    example: 150,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Items per page',
    example: 20,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 8,
  })
  totalPages: number;

  @ApiProperty({
    description: 'Total payment amount for all filtered records',
    example: 4497.5,
  })
  totalAmount: number;
}
