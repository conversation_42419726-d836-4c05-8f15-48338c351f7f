import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { AI_MESSAGE_CONFIG } from 'src/common/message.config';
import { AiLetteringLanguageRepository } from 'src/database/repositories/ai-lettering-language.repository';
import { AiLetteringRepository } from 'src/database/repositories/ai-lettering.repository';
import { CreateAiLetteringLanguageDto } from './dto/create-ai-lettering-language.dto';
import { SortAiLetteringLanguageDto } from './dto/sort-ai-lettering-language.dto';

@Injectable()
export class AiLetteringLanguageService {
  constructor(
    private readonly aiLetteringLanguageRepository: AiLetteringLanguageRepository,
    private readonly aiLetteringRepository: AiLetteringRepository,
  ) {}
  private readonly logger = new Logger(AiLetteringLanguageService.name);

  async create(dto: CreateAiLetteringLanguageDto) {
    const { title } = dto;
    const check = await this.aiLetteringLanguageRepository.findOne({
      where: { title },
    });
    if (check)
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_LETTERING_LANGUAGE_EXISTS,
      );

    const count = await this.aiLetteringLanguageRepository.count();
    await this.aiLetteringLanguageRepository.save({
      title,
      priority: count + 1,
      isDefault: false,
    });

    return true;
  }

  async sort(dto: SortAiLetteringLanguageDto) {
    const { ids } = dto;
    const items = await this.aiLetteringLanguageRepository.find();

    ids.forEach((id, index) => {
      const item = items.find((e) => e.id === id);
      if (!item)
        throw new NotFoundException(
          AI_MESSAGE_CONFIG.AI_LETTERING_LANGUAGE_NOT_FOUND,
        );
      item.priority = index + 1;
    });

    await this.aiLetteringLanguageRepository.save(items);
    return true;
  }

  async list() {
    const items = await this.aiLetteringLanguageRepository.find({
      order: { priority: 'ASC' },
    });
    return items;
  }

  async delete(id: number) {
    const language = await this.aiLetteringLanguageRepository.findOne({
      where: { id },
    });
    if (!language)
      throw new NotFoundException(
        AI_MESSAGE_CONFIG.AI_LETTERING_LANGUAGE_NOT_FOUND,
      );

    if (language.isDefault)
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_LETTERING_LANGUAGE_CANNOT_DELETE_DEFAULT,
      );

    const check = await this.aiLetteringRepository.findOne({
      where: { aiLetteringLanguageId: id },
    });

    if (check)
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_LETTERING_LANGUAGE_IN_USE,
      );

    await this.aiLetteringLanguageRepository.softDelete(id);
    return true;
  }
}
