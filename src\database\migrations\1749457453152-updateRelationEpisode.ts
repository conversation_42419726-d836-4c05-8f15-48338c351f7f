import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateRelationEpisode1749457453152 implements MigrationInterface {
  name = 'UpdateRelationEpisode1749457453152';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`episode_image\` DROP COLUMN \`group\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode_image\` DROP COLUMN \`size\``,
    );
    await queryRunner.query(`ALTER TABLE \`episode\` DROP COLUMN \`image\``);
    await queryRunner.query(
      `ALTER TABLE \`episode\` ADD \`thumbnail\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode\` CHANGE \`episodeNumber\` \`episodeNumber\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode\` CHANGE \`episodeString\` \`episodeString\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode\` CHANGE \`nonUserViewing\` \`nonUserViewing\` tinyint NOT NULL DEFAULT 0`,
    );
    await queryRunner.query(`ALTER TABLE \`episode\` DROP COLUMN \`bgColor\``);
    await queryRunner.query(
      `ALTER TABLE \`episode\` ADD \`bgColor\` enum ('#000000', '#ffffff') NULL DEFAULT '#ffffff'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`episode\` DROP COLUMN \`bgColor\``);
    await queryRunner.query(
      `ALTER TABLE \`episode\` ADD \`bgColor\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode\` CHANGE \`nonUserViewing\` \`nonUserViewing\` tinyint NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode\` CHANGE \`episodeString\` \`episodeString\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode\` CHANGE \`episodeNumber\` \`episodeNumber\` int NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode\` DROP COLUMN \`thumbnail\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode\` ADD \`image\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode_image\` ADD \`size\` int NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode_image\` ADD \`group\` varchar(255) NOT NULL`,
    );
  }
}
