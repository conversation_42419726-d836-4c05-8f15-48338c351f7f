import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum SupportedCurrency {
  USD = 'USD',
  EUR = 'EUR',
  JPY = 'JPY',
  GBP = 'GBP',
  CNY = 'CNY',
  KRW = 'KRW',
}

export enum SupportedLanguage {
  EN = 'en',
  KO = 'ko',
  JA = 'ja',
  DE = 'de',
  ZH = 'zh',
}

export enum SupportedRegion {
  US = 'US',
  KR = 'KR',
  JP = 'JP',
  DE = 'DE',
  CN = 'CN',
  UK = 'UK',
}

@Entity('exchange_rates')
@Index('idx_exchange_rates_date_currency', ['date', 'currency'])
@Index(
  'idx_exchange_rates_lookup',
  ['date', 'currency', 'language', 'region'],
  {
    unique: true,
  },
)
export class ExchangeRateEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'date', comment: 'Exchange rate effective date' })
  @Index('idx_exchange_rates_date')
  date: Date;

  @Column({
    type: 'enum',
    enum: SupportedCurrency,
    comment: 'Source currency code',
  })
  currency: SupportedCurrency;

  @Column({
    type: 'decimal',
    precision: 12,
    scale: 4,
    comment: 'Exchange rate to KRW (1 source currency = ? KRW)',
  })
  rateToKRW: number;

  @Column({
    type: 'enum',
    enum: SupportedLanguage,
    nullable: true,
    comment: 'User language for region-specific rates',
  })
  language?: SupportedLanguage;

  @Column({
    type: 'enum',
    enum: SupportedRegion,
    nullable: true,
    comment: 'User region for region-specific rates',
  })
  region?: SupportedRegion;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    default: 'manual',
    comment: 'Data source (xe.com, fixer.io, manual, etc.)',
  })
  source?: string;

  @Column({
    type: 'decimal',
    precision: 12,
    scale: 4,
    nullable: true,
    comment: 'Original rate from API before regional adjustment',
  })
  originalRate?: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    nullable: true,
    default: 0,
    comment: 'Regional adjustment percentage',
  })
  adjustmentPercentage?: number;

  @Column({
    type: 'enum',
    enum: ['active', 'inactive', 'expired'],
    default: 'active',
    comment: 'Exchange rate status',
  })
  status: 'active' | 'inactive' | 'expired';

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional metadata (API response, etc.)',
  })
  metadata?: Record<string, any>;

  @CreateDateColumn({ comment: 'Record creation timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ comment: 'Record last update timestamp' })
  updatedAt: Date;
}
