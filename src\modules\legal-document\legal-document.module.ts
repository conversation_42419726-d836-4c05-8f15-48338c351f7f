import { Module } from '@nestjs/common';
import { LegalDocumentController } from './controllers/legal-document.controller';
import { LegalDocumentService } from './services/legal-document.service';
import { LegalDocumentRepository } from '../../database/repositories/legal-document.repository';
import { AnalyticsQueueModule } from '../analytics/analytics-queue.module';

@Module({
  imports: [AnalyticsQueueModule],
  controllers: [LegalDocumentController],
  providers: [LegalDocumentService, LegalDocumentRepository],
  exports: [LegalDocumentService],
})
export class LegalDocumentModule {}
