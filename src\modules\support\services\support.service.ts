import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { MESSAGE_CONFIG } from '../../../common/message.config';
import { SupportTicketRepository } from '../../../database/repositories/support-ticket.repository';
import { SupportResponseRepository } from '../../../database/repositories/support-response.repository';
import { SupportCategoryRepository } from '../../../database/repositories/support-category.repository';
import { UserRepository } from '../../../database/repositories/user.repository';
import { AdminRepository } from '../../../database/repositories/admin.repository';
import { CreateSupportTicketDto } from '../dtos/create-support-ticket.dto';
import { UserFollowUpDto } from '../dtos/user-follow-up.dto';
import {
  SupportTicketEntity,
  TicketStatus,
  TicketPriority,
} from '../../../database/entities/support-ticket.entity';
import { QAAutoReplyService } from './qa-auto-reply.service';
// import { MailService } from '../../mail/mail.service';
// import { AnalyticsQueueService } from '../../analytics/services/analytics-queue.service';
import { CommonService } from '../../../common/common.service';
import configurationGlobal from '../../../config/configuration.global';
import { adminNameSystem } from '../../../common/common.config';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class SupportService {
  constructor(
    private supportTicketRepository: SupportTicketRepository,
    private supportResponseRepository: SupportResponseRepository,
    private supportCategoryRepository: SupportCategoryRepository,
    private userRepository: UserRepository,
    private adminRepository: AdminRepository,
    private qaAutoReplyService: QAAutoReplyService,
    private commonService: CommonService,
    // private mailService: MailService,
  ) {}

  async createSupportTicket(
    createDto: CreateSupportTicketDto,
    userId: number,
  ): Promise<{ success: boolean; ticketId: number; hasAutoReply: boolean }> {
    // 1. Validate Category (if provided)
    if (createDto.categoryId) {
      const validatedCategory =
        await this.supportCategoryRepository.validateCategoryExists(
          createDto.categoryId,
        );
      if (!validatedCategory) {
        throw new BadRequestException(MESSAGE_CONFIG.CATEGORY_NOT_FOUND);
      }
    }

    // 2. Generate ticket number
    const ticketNumber =
      await this.supportTicketRepository.generateTicketNumber();

    // 3. Save Support Ticket
    const ticket = await this.supportTicketRepository.save({
      userId,
      categoryId: createDto.categoryId,
      ticketNumber,
      subject: createDto.subject,
      description: createDto.description,
      attachments: createDto.attachments || [],
      priority: createDto.priority || TicketPriority.MEDIUM,
      contextData: createDto.contextData,
      status: TicketStatus.OPEN,
    });

    // 4. Send Admin Notification Email
    try {
      const admin = await this.adminRepository.findOne({
        where: { username: adminNameSystem },
      });
      if (!admin) {
        throw new NotFoundException(MESSAGE_CONFIG.ADMIN_NOT_FOUND);
      }
      const template = 'qa-question';

      const category = await this.supportCategoryRepository.findOne({
        where: { id: createDto.categoryId },
      });
      if (!category) {
        throw new NotFoundException(MESSAGE_CONFIG.CATEGORY_NOT_FOUND);
      }

      await this.commonService.sendEmail(
        admin.email,
        'New Support Ticket Created',
        template,
        {
          url: `${configurationGlobal().url.admin}/customer-service/question-answer/details?id=${ticket.id}`,
          urlLogo: 'this.urlLogo',
          title: ticket.subject,
          category: category.name,
          description: ticket.description,
        },
      );
      console.log(
        'Admin email notification sent for ticket:',
        ticket.ticketNumber,
      );
    } catch (error) {
      console.error('Failed to send admin notification:', error.message);
    }

    // 6. Process Auto-Reply using QA Auto Reply Service
    let hasAutoReply = false;
    try {
      const autoReplyResult = await this.qaAutoReplyService.processAutoReply(
        ticket.id,
        userId,
        ticket.subject,
      );

      hasAutoReply = autoReplyResult.success;

      if (hasAutoReply) {
        console.log(
          `Auto-reply created for ticket ${ticket.ticketNumber}: Response ID ${autoReplyResult.autoReplyId}`,
        );
      } else {
        console.log(
          `Auto-reply not processed for ticket ${ticket.ticketNumber} (disabled or error)`,
        );
      }
    } catch (error) {
      console.error('Auto-reply logic failed:', error.message);
      // Don't throw error, just log it
    }

    return {
      success: true,
      ticketId: ticket.id,
      hasAutoReply,
    };
  }

  async getUserTickets(
    userId: number,
    page: number = 1,
    limit: number = 10,
    filters?: {
      status?: TicketStatus;
      category?: string;
      search?: string;
      isRead?: boolean;
    },
  ) {
    const [tickets, total] = await this.supportTicketRepository.findUserTickets(
      userId,
      page,
      limit,
      filters,
    );

    const tickketPlain = plainToInstance(SupportTicketEntity, tickets);

    return {
      data: tickketPlain.map((ticket) => ({
        id: ticket.id,
        ticketNumber: ticket.ticketNumber,
        subject: ticket.subject,
        status: ticket.status,
        priority: ticket.priority,
        description: ticket.description,
        attachments: ticket.attachments || [],
        category: ticket.category
          ? {
              id: ticket.category.id,
              name: ticket.category.name,
              title: ticket.category.title,
            }
          : null,
        hasUnreadResponse: this.checkUnreadResponse(ticket),
        lastResponseAt: this.getLastResponseDate(ticket),
        responseCount: ticket.responses?.length || 0,
        lastResponse: this.getLastResponse(ticket),
        createdAt: ticket.createdAt,
        responses: ticket?.responses || null,
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getUserTicketDetail(ticketId: number, userId: number) {
    const ticket = await this.supportTicketRepository.findUserTicketById(
      ticketId,
      userId,
    );

    if (!ticket) {
      throw new NotFoundException(MESSAGE_CONFIG.TICKET_NOT_FOUND);
    }

    // Auto mark responses as read (similar to check-answer logic in require.md)
    if (ticket.responses && ticket.responses.length > 0) {
      if (!ticket.responseCheckTime) {
        await this.supportTicketRepository.update(ticket.id, {
          responseCheckTime: new Date(),
          isRead: true,
        });
      }
    }

    return {
      id: ticket.id,
      ticketNumber: ticket.ticketNumber,
      subject: ticket.subject,
      description: ticket.description,
      attachments: ticket.attachments || [],
      status: ticket.status,
      priority: ticket.priority,
      category: ticket.category
        ? {
            id: ticket.category.id,
            name: ticket.category.name,
            title: ticket.category.title,
          }
        : null,
      contextData: ticket.contextData,
      createdAt: ticket.createdAt,
      updatedAt: ticket.updatedAt,
      responses:
        ticket.responses?.map((response) => ({
          id: response.id,
          response: response.response,
          attachments: response.attachments || [],
          status: response.status,
          responseCompletedTime: response.responseCompletedTime,
          createdAt: response.createdAt,
          admin: {
            id: response.admin.id,
            username: response.admin.username,
          },
        })) || [],
    };
  }

  async checkTicketResponse(
    ticketId: number,
    userId: number,
  ): Promise<boolean> {
    // Similar to check-answer logic in require.md
    const { ticket, hasCompletedResponse } =
      await this.supportTicketRepository.findWithCompletedResponses(
        ticketId,
        userId,
      );

    if (!ticket) {
      throw new NotFoundException(MESSAGE_CONFIG.TICKET_NOT_FOUND);
    }

    if (!hasCompletedResponse) {
      throw new BadRequestException(MESSAGE_CONFIG.NO_RESPONSE);
    }

    // Update response check time
    await this.supportTicketRepository.update(ticket.id, {
      responseCheckTime: new Date(),
      isRead: true,
    });

    return true;
  }

  async addUserFollowUp(
    ticketId: number,
    followUpDto: UserFollowUpDto,
    userId: number,
  ): Promise<{
    success: boolean;
    responseId: number;
    ticketStatus: TicketStatus;
  }> {
    // 1. Validate ticket exists and belongs to user
    const ticket = await this.supportTicketRepository.findOne({
      where: { id: ticketId, userId },
      relations: ['category'],
    });

    if (!ticket) {
      throw new NotFoundException(MESSAGE_CONFIG.TICKET_NOT_FOUND);
    }

    // 2. Check if ticket is closed
    if (ticket.status === TicketStatus.CLOSED) {
      throw new BadRequestException(MESSAGE_CONFIG.TICKET_CLOSED);
    }

    // 3. Get user info
    const user = await this.userRepository.findOne({ where: { id: userId } });

    // 4. Create user follow-up response (using user as "admin" for consistency)
    const systemAdmin = await this.adminRepository.findOne({
      where: { username: adminNameSystem },
    });

    if (!systemAdmin) {
      throw new BadRequestException(MESSAGE_CONFIG.SYSTEM_ERROR);
    }

    const response = await this.supportResponseRepository.save({
      ticketId,
      adminId: systemAdmin.id, // Using system admin to track user responses
      response: `[User Follow-up from ${user?.email || 'User'}]\n\n${followUpDto.message}`,
      attachments: followUpDto.attachments || [],
      status: 'sent' as any,
      isInternal: false,
      responseCompletedTime: new Date(),
    });

    // 5. Update ticket status to pending_user
    let newStatus = ticket.status;
    if (ticket.status === TicketStatus.IN_PROGRESS) {
      newStatus = TicketStatus.PENDING_USER;
    }

    await this.supportTicketRepository.update(ticketId, {
      status: newStatus,
      isRead: false, // Mark as unread for admin
    });

    // 6. Send notification to admin
    try {
      // await this.mailService.sendSupportFollowUpNotification({
      //   ticketId: ticket.id,
      //   ticketNumber: ticket.ticketNumber,
      //   subject: ticket.subject,
      //   userEmail: user?.email,
      //   followUpMessage: followUpDto.message,
      //   category: ticket.category?.title,
      // });
      console.log(
        'Follow-up notification sent to admin for ticket:',
        ticket.ticketNumber,
      );
    } catch (error) {
      console.error('Failed to send follow-up notification:', error.message);
    }

    // 7. Track analytics
    try {
      // await this.analyticsQueueService.trackSupportActivity({
      //   userId,
      //   ticketId: ticket.id,
      //   activityType: 'user_follow_up',
      //   category: ticket.category?.title || 'uncategorized',
      //   priority: ticket.priority,
      // });
      console.log('Analytics tracked for user follow-up:', ticketId);
    } catch (error) {
      console.error('Analytics tracking failed:', error.message);
    }

    return {
      success: true,
      responseId: response.id,
      ticketStatus: newStatus,
    };
  }

  // Helper methods
  private checkUnreadResponse(ticket: SupportTicketEntity): boolean {
    if (!ticket.responses || ticket.responses.length === 0) return false;
    return (
      !ticket.responseCheckTime ||
      ticket.responses.some((r) => r.createdAt > ticket.responseCheckTime)
    );
  }

  private getLastResponseDate(ticket: SupportTicketEntity): Date | null {
    if (!ticket.responses || ticket.responses.length === 0) return null;
    return ticket.responses[ticket.responses.length - 1].createdAt;
  }

  private getLastResponse(ticket: SupportTicketEntity) {
    if (!ticket.responses || ticket.responses.length === 0) return null;
    const lastResponse = ticket.responses[ticket.responses.length - 1];
    return {
      id: lastResponse.id,
      response: lastResponse.response.substring(0, 100) + '...',
      status: lastResponse.status,
      createdAt: lastResponse.createdAt,
      admin: {
        id: lastResponse.admin.id,
        username: lastResponse.admin.username,
      },
    };
  }
}
