import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiQuery,
} from '@nestjs/swagger';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { User } from '../../auth/decorators/user.decorator';
import { UserEntity } from '../../../database/entities/user.entity';
import { UploadService } from '../shared/services/upload.service';
import {
  UploaderType,
  UploadContext,
} from '../../../database/entities/upload.entity';
import { UserUploadImageDto } from './dtos/upload-image.dto';
import { UserUploadDocumentDto } from './dtos/upload-document.dto';
import { UserUploadSupportAttachmentDto } from './dtos/upload-support-attachment.dto';

@Controller('upload')
@UseGuards(JwtAuthGuard)
@ApiTags('User Upload')
@ApiBearerAuth()
export class UserUploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post('profile-image')
  @UseInterceptors(
    FileInterceptor('image', {
      limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Upload Profile Image',
    description: 'Upload user profile image (PNG/JPEG only, max 5MB)',
  })
  @ApiResponse({
    status: 201,
    description: 'Profile image uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        uploadId: { type: 'number', example: 123 },
        filePath: {
          type: 'string',
          example: '/uploads/users/1/profile/image_123456.png',
        },
        thumbnailPath: {
          type: 'string',
          example: '/uploads/thumbnails/users/1/profile/thumb_image_123456.png',
        },
        publicUrl: {
          type: 'string',
          example:
            'https://example.com/uploads/users/1/profile/image_123456.png',
        },
      },
    },
  })
  async uploadProfileImage(
    @UploadedFile() file: any,
    @User() user: UserEntity,
    @Body() dto: UserUploadImageDto,
  ) {
    return this.uploadService.uploadFile(
      file,
      user.id,
      UploaderType.USER,
      UploadContext.PROFILE,
      dto.description,
    );
  }

  @Post('support-attachment')
  @UseInterceptors(
    FilesInterceptor('files', undefined, {
      limits: { fileSize: 25 * 1024 * 1024 }, // 25MB per file
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Upload Support Attachments',
    description:
      'Upload multiple files for support tickets (max 10 files, 25MB each)',
  })
  @ApiResponse({
    status: 201,
    description: 'Support attachments uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        uploads: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              uploadId: { type: 'number' },
              filePath: { type: 'string' },
              publicUrl: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async uploadSupportAttachments(
    @UploadedFiles() files: any[],
    @User() user: UserEntity,
    @Body() dto: UserUploadSupportAttachmentDto,
  ) {
    if (!files || files.length === 0) {
      throw new Error('No files provided');
    }

    const results: any[] = [];
    for (const file of files) {
      try {
        const result = await this.uploadService.uploadFile(
          file,
          user.id,
          UploaderType.USER,
          UploadContext.SUPPORT,
          dto.description,
        );
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          error: error.message,
          fileName: file.originalname,
        });
      }
    }

    return { uploads: results };
  }

  @Post('document')
  @UseInterceptors(
    FileInterceptor('document', {
      limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Upload Document',
    description: 'Upload general document (PDF, DOC, TXT - max 10MB)',
  })
  @ApiResponse({
    status: 201,
    description: 'Document uploaded successfully',
  })
  async uploadDocument(
    @UploadedFile() file: any,
    @User() user: UserEntity,
    @Body() dto: UserUploadDocumentDto,
  ) {
    return this.uploadService.uploadFile(
      file,
      user.id,
      UploaderType.USER,
      UploadContext.GENERAL,
      dto.description,
    );
  }

  @Get('my-files')
  @ApiOperation({
    summary: 'Get My Files',
    description: 'Get list of files uploaded by current user',
  })
  @ApiQuery({ name: 'context', required: false, enum: UploadContext })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 20 })
  @ApiResponse({
    status: 200,
    description: 'User files retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              originalName: { type: 'string' },
              fileName: { type: 'string' },
              fileSize: { type: 'number' },
              mimeType: { type: 'string' },
              fileType: { type: 'string' },
              context: { type: 'string' },
              description: { type: 'string' },
              publicUrl: { type: 'string' },
              thumbnailUrl: { type: 'string' },
              createdAt: { type: 'string' },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  async getMyFiles(
    @User() user: UserEntity,
    @Query('context') context?: UploadContext,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.uploadService.getUserFiles(user.id, {
      context,
      page: page || 1,
      limit: limit || 20,
    });
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete My File',
    description: 'Delete a file uploaded by current user',
  })
  @ApiResponse({
    status: 200,
    description: 'File deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'File deleted successfully' },
      },
    },
  })
  async deleteFile(
    @Param('id', ParseIntPipe) uploadId: number,
    @User() user: UserEntity,
  ) {
    const success = await this.uploadService.deleteUserFile(uploadId, user.id);

    if (!success) {
      return {
        success: false,
        message: 'File not found or cannot be deleted',
      };
    }

    return {
      success: true,
      message: 'File deleted successfully',
    };
  }
}
