import { Entity, Column, OneToMany, Unique } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { AdminRole } from 'src/common/role.enum';
import { AdminStatus } from 'src/common/status.enum';
import { Exclude } from 'class-transformer';
import { MenuAdminEntity } from './menu-admin.entity';
import { SanctionEntity } from './sanction.entity';
@Entity('admins')
@Unique(['username', 'email', 'deletedAt'])
export class AdminEntity extends DefaultEntity {
  @Column()
  email: string;

  @Column()
  username: string;

  @Exclude()
  @Column({ nullable: true })
  password: string;

  @Column({ type: 'enum', enum: AdminRole, default: AdminRole.ADMIN })
  role: AdminRole;

  @Column({ type: 'enum', enum: AdminStatus, default: AdminStatus.ACTIVE })
  status: AdminStatus;

  @Column({ nullable: true })
  forgotPasswordToken: string;

  @Column({ nullable: true })
  forgotPasswordTokenExpiresAt: Date;

  @OneToMany(() => MenuAdminEntity, (menu) => menu.admin)
  menu: MenuAdminEntity[];

  @OneToMany(() => SanctionEntity, (sanction) => sanction.admin)
  sanctions: SanctionEntity[];
}
