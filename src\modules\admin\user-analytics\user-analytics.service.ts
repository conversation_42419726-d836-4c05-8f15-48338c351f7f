import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import {
  UserAnalyticsQueryDto,
  HourlyAnalyticsQueryDto,
  MonthlyAnalyticsQueryDto,
} from './dtos/user-analytics-request.dto';
import {
  UserAnalyticsResponseDto,
  HourlyAnalyticsResponseDto,
  MonthlyAnalyticsResponseDto,
  UserAnalyticsStatsDto,
  HourlyAnalyticsStatsDto,
  MonthlyAnalyticsStatsDto,
} from './dtos/user-analytics-response.dto';
import {
  HourlyAnalyticsV2QueryDto,
  DailyAnalyticsV2QueryDto,
  MonthlyAnalyticsV2QueryDto,
} from './dtos/user-analytics-v2-request.dto';
import {
  HourlyAnalyticsV2ResponseDto,
  DailyAnalyticsV2ResponseDto,
  MonthlyAnalyticsV2ResponseDto,
  HourlyAnalyticsV2StatsDto,
  DailyAnalyticsV2StatsDto,
  MonthlyAnalyticsV2StatsDto,
} from './dtos/user-analytics-v2-response.dto';
import { UserAnalyticRepository } from '../../../database/repositories/user-analytics.repository';
import { UserAnalyticsEntityV2 } from '../../../database/entities/user-analytics_v2.entity';

@Injectable()
export class UserAnalyticsService {
  constructor(
    private dataSource: DataSource,
    private userAnalyticsRepository: UserAnalyticRepository,
    @InjectRepository(UserAnalyticsEntityV2)
    private userAnalyticsV2Repository: Repository<UserAnalyticsEntityV2>,
  ) {}

  async getDailyAnalytics(
    filters: UserAnalyticsQueryDto,
  ): Promise<UserAnalyticsResponseDto> {
    const { startDate, endDate } = filters;

    // Set default date range if not provided (last 30 days)
    const defaultEndDate = new Date();
    const defaultStartDate = new Date();
    defaultStartDate.setDate(defaultStartDate.getDate() - 30);

    const queryStartDate = startDate ? new Date(startDate) : defaultStartDate;
    const queryEndDate = endDate ? new Date(endDate) : defaultEndDate;

    // Format dates for SQL query (YYYY-MM-DD)
    const formattedStartDate = queryStartDate.toISOString().split('T')[0];
    const formattedEndDate = queryEndDate.toISOString().split('T')[0];

    const query = `
      SELECT 
        ua.date,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.activeUsers ELSE 0 END) as pcActiveUsers,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.activeUsers ELSE 0 END) as mobileActiveUsers,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.newRegistrations ELSE 0 END) as pcNewRegistrations,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.newRegistrations ELSE 0 END) as mobileNewRegistrations,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.deletedAccounts ELSE 0 END) as pcDeletedAccounts,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.deletedAccounts ELSE 0 END) as mobileDeletedAccounts,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.loginCount ELSE 0 END) as pcLoginCount,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.loginCount ELSE 0 END) as mobileLoginCount,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.uniqueLogins ELSE 0 END) as pcUniqueLogins,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.uniqueLogins ELSE 0 END) as mobileUniqueLogins,
        SUM(ua.activeUsers) as totalActiveUsers,
        SUM(ua.newRegistrations) as totalNewRegistrations,
        SUM(ua.deletedAccounts) as totalDeletedAccounts,
        SUM(ua.loginCount) as totalLoginCount,
        SUM(ua.uniqueLogins) as totalUniqueLogins
      FROM user_analytics ua
      WHERE ua.date BETWEEN ? AND ?
        AND ua.deletedAt IS NULL
      GROUP BY ua.date
      ORDER BY ua.date DESC
    `;

    const rawResults = await this.dataSource.query(query, [
      formattedStartDate,
      formattedEndDate,
    ]);

    const processedData: UserAnalyticsStatsDto[] = rawResults.map(
      (row: any) => {
        const totalViews = parseInt(row.totalLoginCount) || 0;
        const signup = parseInt(row.totalNewRegistrations) || 0;
        const signupRate = totalViews > 0 ? (signup / totalViews) * 100 : 0;

        return {
          date: row.date.toISOString().split('T')[0],
          totalViews,
          userActive: parseInt(row.totalActiveUsers) || 0,
          signup,
          signupRate: Math.round(signupRate * 100) / 100,
          deleteAccount: parseInt(row.totalDeletedAccounts) || 0,
        };
      },
    );

    return {
      data: processedData,
      total: processedData.length,
      queryInfo: {
        startDate: formattedStartDate,
        endDate: formattedEndDate,
      },
    };
  }

  async getHourlyAnalytics(
    filters: HourlyAnalyticsQueryDto,
  ): Promise<HourlyAnalyticsResponseDto> {
    const { date } = filters;

    const query = `
      SELECT 
        ua.hour,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.activeUsers ELSE 0 END) as pcActiveUsers,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.activeUsers ELSE 0 END) as mobileActiveUsers,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.newRegistrations ELSE 0 END) as pcNewRegistrations,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.newRegistrations ELSE 0 END) as mobileNewRegistrations,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.deletedAccounts ELSE 0 END) as pcDeletedAccounts,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.deletedAccounts ELSE 0 END) as mobileDeletedAccounts,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.loginCount ELSE 0 END) as pcLoginCount,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.loginCount ELSE 0 END) as mobileLoginCount,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.uniqueLogins ELSE 0 END) as pcUniqueLogins,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.uniqueLogins ELSE 0 END) as mobileUniqueLogins,
        SUM(ua.activeUsers) as totalActiveUsers,
        SUM(ua.newRegistrations) as totalNewRegistrations,
        SUM(ua.deletedAccounts) as totalDeletedAccounts,
        SUM(ua.loginCount) as totalLoginCount,
        SUM(ua.uniqueLogins) as totalUniqueLogins
      FROM user_analytics ua
      WHERE ua.date = ?
        AND ua.deletedAt IS NULL
      GROUP BY ua.hour
      ORDER BY ua.hour
    `;

    const rawResults = await this.dataSource.query(query, [date]);

    const processedData: HourlyAnalyticsStatsDto[] = rawResults.map(
      (row: any) => {
        const totalViews = parseInt(row.totalLoginCount) || 0;
        const signup = parseInt(row.totalNewRegistrations) || 0;
        const signupRate = totalViews > 0 ? (signup / totalViews) * 100 : 0;

        return {
          hour: `${String(row.hour).padStart(2, '0')}-${String(row.hour + 1).padStart(2, '0')}`,
          totalViews,
          userActive: parseInt(row.totalActiveUsers) || 0,
          signup,
          signupRate: Math.round(signupRate * 100) / 100,
          deleteAccount: parseInt(row.totalDeletedAccounts) || 0,
        };
      },
    );

    return {
      data: processedData,
      total: processedData.length,
      queryInfo: {
        date,
      },
    };
  }

  async getMonthlyAnalytics(
    filters: MonthlyAnalyticsQueryDto,
  ): Promise<MonthlyAnalyticsResponseDto> {
    const { startMonth, endMonth } = filters;

    // Convert YYYY-MM to date range
    const startDate = `${startMonth}-01`;
    const endDate = `${endMonth}-31`; // Last day of end month

    const query = `
      SELECT 
        DATE_FORMAT(ua.date, '%Y-%m') as month,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.activeUsers ELSE 0 END) as pcActiveUsers,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.activeUsers ELSE 0 END) as mobileActiveUsers,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.newRegistrations ELSE 0 END) as pcNewRegistrations,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.newRegistrations ELSE 0 END) as mobileNewRegistrations,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.deletedAccounts ELSE 0 END) as pcDeletedAccounts,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.deletedAccounts ELSE 0 END) as mobileDeletedAccounts,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.loginCount ELSE 0 END) as pcLoginCount,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.loginCount ELSE 0 END) as mobileLoginCount,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.uniqueLogins ELSE 0 END) as pcUniqueLogins,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.uniqueLogins ELSE 0 END) as mobileUniqueLogins,
        SUM(ua.activeUsers) as totalActiveUsers,
        SUM(ua.newRegistrations) as totalNewRegistrations,
        SUM(ua.deletedAccounts) as totalDeletedAccounts,
        SUM(ua.loginCount) as totalLoginCount,
        SUM(ua.uniqueLogins) as totalUniqueLogins,
        AVG(ua.activeUsers) as avgDailyActiveUsers,
        COUNT(DISTINCT ua.date) as daysInMonth
      FROM user_analytics ua
      WHERE ua.date BETWEEN ? AND ?
        AND ua.deletedAt IS NULL
      GROUP BY DATE_FORMAT(ua.date, '%Y-%m')
      ORDER BY month DESC
    `;

    const rawResults = await this.dataSource.query(query, [startDate, endDate]);

    const processedData: MonthlyAnalyticsStatsDto[] = rawResults.map(
      (row: any) => {
        const totalViews = parseInt(row.totalLoginCount) || 0;
        const signup = parseInt(row.totalNewRegistrations) || 0;
        const signupRate = totalViews > 0 ? (signup / totalViews) * 100 : 0;

        return {
          month: row.month,
          totalViews,
          userActive: parseInt(row.totalActiveUsers) || 0,
          signup,
          signupRate: Math.round(signupRate * 100) / 100,
          deleteAccount: parseInt(row.totalDeletedAccounts) || 0,
          avgDailyActiveUsers: Math.round(
            parseFloat(row.avgDailyActiveUsers) || 0,
          ),
        };
      },
    );

    return {
      data: processedData,
      total: processedData.length,
      queryInfo: {
        startMonth,
        endMonth,
      },
    };
  }

  async getHourlyAnalyticsV2(
    filters: HourlyAnalyticsV2QueryDto,
  ): Promise<HourlyAnalyticsV2ResponseDto> {
    const { date, language } = filters;

    if (language) {
      // Get data for specific language
      const data = await this.userAnalyticsV2Repository.find({
        where: { date, language: parseInt(language) || 1 },
        order: { hour: 'ASC' },
      });

      const formattedData: HourlyAnalyticsV2StatsDto[] = data.map((item) => ({
        date: item.date,
        hour: item.hour,
        language: String(item.language), // Convert number to string
        activeUsers: item.activeUsers,
        newRegistrations: item.newRegistrations,
        deletedAccounts: item.deletedAccounts,
        loginCount: item.loginCount,
        uniqueLogins: item.uniqueLogins,
        adultVerifiedCount: item.adultVerifiedCount,
        firtPurchaseCount: item.firtPurchaseCount,
        firstPurchaseAttemptCount: item.firstPurchaseAttemptCount,
        firstPurchaseAmount: item.firstPurchaseAmount,
        rePurchaseCount: item.rePurchaseCount,
        rePurchaseAttemptCount: item.rePurchaseAttemptCount,
        rePurchaseAmount: item.rePurchaseAmount,
      }));

      return {
        data: formattedData,
        total: formattedData.length,
        date,
        language,
      };
    } else {
      // Aggregate all languages by hour
      const query = this.userAnalyticsV2Repository
        .createQueryBuilder('ua')
        .select('ua.hour', 'hour')
        .addSelect('ua.date', 'date')
        .addSelect('SUM(ua.activeUsers)', 'activeUsers')
        .addSelect('SUM(ua.newRegistrations)', 'newRegistrations')
        .addSelect('SUM(ua.deletedAccounts)', 'deletedAccounts')
        .addSelect('SUM(ua.loginCount)', 'loginCount')
        .addSelect('SUM(ua.uniqueLogins)', 'uniqueLogins')
        .addSelect('SUM(ua.adultVerifiedCount)', 'adultVerifiedCount')
        .addSelect('SUM(ua.firtPurchaseCount)', 'firtPurchaseCount')
        .addSelect(
          'SUM(ua.firstPurchaseAttemptCount)',
          'firstPurchaseAttemptCount',
        )
        .addSelect('SUM(ua.firstPurchaseAmount)', 'firstPurchaseAmount')
        .addSelect('SUM(ua.rePurchaseCount)', 'rePurchaseCount')
        .addSelect('SUM(ua.rePurchaseAttemptCount)', 'rePurchaseAttemptCount')
        .addSelect('SUM(ua.rePurchaseAmount)', 'rePurchaseAmount')
        .where('ua.date = :date', { date })
        .groupBy('ua.hour, ua.date')
        .orderBy('ua.hour', 'ASC');

      const rawData = await query.getRawMany();

      const formattedData: HourlyAnalyticsV2StatsDto[] = rawData.map((row) => ({
        date: row.date,
        hour: parseInt(row.hour),
        language: 'all',
        activeUsers: parseInt(row.activeUsers) || 0,
        newRegistrations: parseInt(row.newRegistrations) || 0,
        deletedAccounts: parseInt(row.deletedAccounts) || 0,
        loginCount: parseInt(row.loginCount) || 0,
        uniqueLogins: parseInt(row.uniqueLogins) || 0,
        adultVerifiedCount: parseInt(row.adultVerifiedCount) || 0,
        firtPurchaseCount: parseInt(row.firtPurchaseCount) || 0,
        firstPurchaseAttemptCount: parseInt(row.firstPurchaseAttemptCount) || 0,
        firstPurchaseAmount: parseInt(row.firstPurchaseAmount) || 0,
        rePurchaseCount: parseInt(row.rePurchaseCount) || 0,
        rePurchaseAttemptCount: parseInt(row.rePurchaseAttemptCount) || 0,
        rePurchaseAmount: parseInt(row.rePurchaseAmount) || 0,
      }));

      return {
        data: formattedData,
        total: formattedData.length,
        date,
        language: 'all',
      };
    }
  }

  async getDailyAnalyticsV2(
    filters: DailyAnalyticsV2QueryDto,
  ): Promise<DailyAnalyticsV2ResponseDto> {
    const { startDate, endDate, language } = filters;

    const query = this.userAnalyticsV2Repository
      .createQueryBuilder('ua')
      .select('ua.date', 'date')
      .addSelect('SUM(ua.activeUsers)', 'activeUsers')
      .addSelect('SUM(ua.newRegistrations)', 'newRegistrations')
      .addSelect('SUM(ua.deletedAccounts)', 'deletedAccounts')
      .addSelect('SUM(ua.loginCount)', 'loginCount')
      .addSelect('SUM(ua.uniqueLogins)', 'uniqueLogins')
      .addSelect('SUM(ua.adultVerifiedCount)', 'adultVerifiedCount')
      .addSelect('SUM(ua.firtPurchaseCount)', 'firtPurchaseCount')
      .addSelect(
        'SUM(ua.firstPurchaseAttemptCount)',
        'firstPurchaseAttemptCount',
      )
      .addSelect('SUM(ua.firstPurchaseAmount)', 'firstPurchaseAmount')
      .addSelect('SUM(ua.rePurchaseCount)', 'rePurchaseCount')
      .addSelect('SUM(ua.rePurchaseAttemptCount)', 'rePurchaseAttemptCount')
      .addSelect('SUM(ua.rePurchaseAmount)', 'rePurchaseAmount')
      .where('ua.date >= :startDate', { startDate })
      .andWhere('ua.date <= :endDate', { endDate });

    if (language) {
      query.andWhere('ua.language = :language', { language });
    }

    query.groupBy('ua.date').orderBy('ua.date', 'DESC');

    const rawData = await query.getRawMany();

    const formattedData: DailyAnalyticsV2StatsDto[] = rawData.map((row) => ({
      date: row.date,
      language: language || 'all',
      activeUsers: parseInt(row.activeUsers) || 0,
      newRegistrations: parseInt(row.newRegistrations) || 0,
      deletedAccounts: parseInt(row.deletedAccounts) || 0,
      loginCount: parseInt(row.loginCount) || 0,
      uniqueLogins: parseInt(row.uniqueLogins) || 0,
      adultVerifiedCount: parseInt(row.adultVerifiedCount) || 0,
      firtPurchaseCount: parseInt(row.firtPurchaseCount) || 0,
      firstPurchaseAttemptCount: parseInt(row.firstPurchaseAttemptCount) || 0,
      firstPurchaseAmount: parseInt(row.firstPurchaseAmount) || 0,
      rePurchaseCount: parseInt(row.rePurchaseCount) || 0,
      rePurchaseAttemptCount: parseInt(row.rePurchaseAttemptCount) || 0,
      rePurchaseAmount: parseInt(row.rePurchaseAmount) || 0,
    }));

    return {
      data: formattedData,
      total: formattedData.length,
      startDate,
      endDate,
      language: language || 'all',
    };
  }

  async getMonthlyAnalyticsV2(
    filters: MonthlyAnalyticsV2QueryDto,
  ): Promise<MonthlyAnalyticsV2ResponseDto> {
    const { startMonth, endMonth, language } = filters;

    // Calculate date range from months
    const startDate = `${startMonth}/01`;
    const [endYear, endMonthNum] = endMonth.split('/').map(Number);
    const lastDay = new Date(endYear, endMonthNum, 0).getDate();
    const endDate = `${endMonth}/${String(lastDay).padStart(2, '0')}`;

    const query = this.userAnalyticsV2Repository
      .createQueryBuilder('ua')
      .select(`SUBSTRING(ua.date, 1, 7)`, 'month')
      .addSelect('SUM(ua.activeUsers)', 'activeUsers')
      .addSelect('SUM(ua.newRegistrations)', 'newRegistrations')
      .addSelect('SUM(ua.deletedAccounts)', 'deletedAccounts')
      .addSelect('SUM(ua.loginCount)', 'loginCount')
      .addSelect('SUM(ua.uniqueLogins)', 'uniqueLogins')
      .addSelect('SUM(ua.adultVerifiedCount)', 'adultVerifiedCount')
      .addSelect('SUM(ua.firtPurchaseCount)', 'firtPurchaseCount')
      .addSelect(
        'SUM(ua.firstPurchaseAttemptCount)',
        'firstPurchaseAttemptCount',
      )
      .addSelect('SUM(ua.firstPurchaseAmount)', 'firstPurchaseAmount')
      .addSelect('SUM(ua.rePurchaseCount)', 'rePurchaseCount')
      .addSelect('SUM(ua.rePurchaseAttemptCount)', 'rePurchaseAttemptCount')
      .addSelect('SUM(ua.rePurchaseAmount)', 'rePurchaseAmount')
      .addSelect('COUNT(DISTINCT ua.date)', 'daysInMonth')
      .where('ua.date >= :startDate', { startDate })
      .andWhere('ua.date <= :endDate', { endDate });

    if (language) {
      query.andWhere('ua.language = :language', { language });
    }

    query.groupBy('month').orderBy('month', 'DESC');

    const rawData = await query.getRawMany();

    const formattedData: MonthlyAnalyticsV2StatsDto[] = rawData.map((row) => {
      const activeUsers = parseInt(row.activeUsers) || 0;
      const daysInMonth = parseInt(row.daysInMonth) || 1;

      return {
        month: row.month,
        language: language || 'all',
        activeUsers,
        newRegistrations: parseInt(row.newRegistrations) || 0,
        deletedAccounts: parseInt(row.deletedAccounts) || 0,
        loginCount: parseInt(row.loginCount) || 0,
        uniqueLogins: parseInt(row.uniqueLogins) || 0,
        adultVerifiedCount: parseInt(row.adultVerifiedCount) || 0,
        firtPurchaseCount: parseInt(row.firtPurchaseCount) || 0,
        firstPurchaseAttemptCount: parseInt(row.firstPurchaseAttemptCount) || 0,
        firstPurchaseAmount: parseInt(row.firstPurchaseAmount) || 0,
        rePurchaseCount: parseInt(row.rePurchaseCount) || 0,
        rePurchaseAttemptCount: parseInt(row.rePurchaseAttemptCount) || 0,
        rePurchaseAmount: parseInt(row.rePurchaseAmount) || 0,
        avgDailyActiveUsers: Math.round(activeUsers / daysInMonth),
      };
    });

    return {
      data: formattedData,
      total: formattedData.length,
      startMonth,
      endMonth,
      language: language || 'all',
    };
  }
}
