import {
  IsNotEmpty,
  <PERSON>String,
  <PERSON><PERSON><PERSON>ber,
  <PERSON>Enum,
  <PERSON><PERSON><PERSON><PERSON>,
  Min,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaymentStatus } from '../../../../common/status.enum';

export class CreatePaymentDto {
  @ApiProperty({
    description: 'Payment amount',
    example: 29.99,
    minimum: 0,
  })
  @IsNotEmpty()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  amount: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
  })
  @IsNotEmpty()
  @IsString()
  currency: string;

  @ApiPropertyOptional({
    description: 'Payment status',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus;

  @ApiPropertyOptional({
    description: 'Payment method used',
    example: 'credit_card',
  })
  @IsOptional()
  @IsString()
  method?: string;

  @ApiPropertyOptional({
    description: 'Idempotency key for preventing duplicate payments',
    example: 'idmp_1234567890',
  })
  @IsOptional()
  @IsString()
  idempotencyKey?: string;

  @ApiPropertyOptional({
    description: 'Transaction ID from payment provider',
    example: 'txn_abc123xyz',
  })
  @IsOptional()
  @IsString()
  providerTransactionId?: string;

  @ApiPropertyOptional({
    description: 'Transaction status from payment provider',
    example: 'completed',
  })
  @IsOptional()
  @IsString()
  providerTransactionStatus?: string;

  @ApiProperty({
    description: 'ID of the subscription this payment is for',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  subscriptionId: number;
}
