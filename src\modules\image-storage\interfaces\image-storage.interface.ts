import { ImageStorageType } from '../dto/upload-image.dto';
import { UploadStatus } from '../dto/upload-response.dto';

export interface ImageMetadata {
  filename?: string;
  tags?: string[];
  description?: string;
  albumId?: string;
  targetPath?: string;
  originalFilename: string;
  size: number;
  mimetype: string;
  signature: string;
  publicKey: string;
  type: ImageStorageType;
}

export interface UploadMetadata extends ImageMetadata {
  uploadId: string;
  status: UploadStatus;
  progress?: number;
  imageUrl?: string;
  thumbnailUrl?: string;
  error?: string;
  createdAt: string;
  completedAt?: string;
}

export interface ProcessImageJob {
  uploadId: string;
  imageBuffer: Buffer;
  metadata: ImageMetadata;
}

export interface WebhookPayload {
  uploadId: string;
  status: UploadStatus;
  imageUrl?: string;
  thumbnailUrl?: string;
  metadata?: ImageMetadata;
  error?: string;
  createdAt: string;
  completedAt?: string;
}

export interface StorageResult {
  originalUrl: string;
  thumbnailUrl: string;
  sizes?: {
    small?: string;
    medium?: string;
    large?: string;
  };
}

export interface ImageProcessingResult {
  originalBuffer: Buffer;
  thumbnailBuffer: Buffer;
  optimizedBuffer?: Buffer;
  sizes?: {
    small?: Buffer;
    medium?: Buffer;
    large?: Buffer;
  };
}
