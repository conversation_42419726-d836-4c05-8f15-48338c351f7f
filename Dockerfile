FROM node:20-alpine AS builder
# Set timezone
ENV TZ=Asia/Ho_Chi_Minh
RUN apk add --no-cache tzdata
WORKDIR /app

# Add build argument for environment
ARG BUILD_ENV=dev

COPY package*.json ./
RUN npm install

# Copy environment file based on build arg

COPY . .
COPY .env.${BUILD_ENV} ./.env
RUN npm run build

# Stage 2: Runtime (distroless)
FROM node:20-alpine
# Set timezone for runtime
ENV TZ=Asia/Ho_Chi_Minh
RUN apk add --no-cache tzdata
WORKDIR /app

# Add runtime argument
ARG BUILD_ENV=dev

COPY --from=builder /app/package*.json ./
COPY --from=builder /app/.env.${BUILD_ENV} ./.env.${BUILD_ENV}
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/tsconfig.json ./tsconfig.json
COPY --from=builder /app/uploads ./uploads
COPY --from=builder /app/mail-templates ./mail-templates
EXPOSE 3005
CMD ["dist/main"]