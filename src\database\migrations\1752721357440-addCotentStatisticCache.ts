import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCotentStatisticCache1752721357440
  implements MigrationInterface
{
  name = 'AddCotentStatisticCache1752721357440';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`content_statistics_cache\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`contentId\` int NOT NULL, \`title\` varchar(255) NOT NULL, \`releaseDate\` datetime NOT NULL, \`type\` varchar(10) NOT NULL, \`episodes\` int NOT NULL DEFAULT '0', \`author\` varchar(255) NULL, \`series\` varchar(20) NOT NULL, \`genre\` varchar(255) NULL, \`freeEpisodeRead\` bigint NOT NULL DEFAULT '0', \`paidEpisodeRead\` bigint NOT NULL DEFAULT '0', \`totalEpisodeRead\` bigint NOT NULL DEFAULT '0', \`lastUpdated\` datetime NOT NULL, \`metadata\` json NULL, INDEX \`IDX_c37a64f44a1fd0ed49854f70c4\` (\`contentId\`), INDEX \`IDX_959a7a7b93e8975b5896b67dee\` (\`lastUpdated\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` ADD \`totalAmountKRW\` decimal(15,2) NOT NULL DEFAULT '0.00'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` ADD \`paymentAttempts\` int NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` ADD \`language\` varchar(5) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` ADD \`region\` varchar(5) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` ADD \`exchangeRate\` decimal(10,4) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` ADD \`exchangeRateDate\` date NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` ADD \`totalRegisteredUsers\` int NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` ADD \`firstPurchaseRate\` decimal(5,2) NOT NULL DEFAULT '0.00'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` ADD \`repurchaseRate\` decimal(5,2) NOT NULL DEFAULT '0.00'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` ADD \`totalPurchaseRate\` decimal(5,2) NOT NULL DEFAULT '0.00'`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`IDX_eb91d23314f2ca3e6f0a7d4711\` ON \`payment_analytics\` (\`date\`, \`hour\`, \`paymentType\`, \`platform\`, \`language\`, \`region\`)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_eb91d23314f2ca3e6f0a7d4711\` ON \`payment_analytics\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` DROP COLUMN \`totalPurchaseRate\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` DROP COLUMN \`repurchaseRate\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` DROP COLUMN \`firstPurchaseRate\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` DROP COLUMN \`totalRegisteredUsers\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` DROP COLUMN \`exchangeRateDate\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` DROP COLUMN \`exchangeRate\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` DROP COLUMN \`region\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` DROP COLUMN \`language\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` DROP COLUMN \`paymentAttempts\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` DROP COLUMN \`totalAmountKRW\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_959a7a7b93e8975b5896b67dee\` ON \`content_statistics_cache\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_c37a64f44a1fd0ed49854f70c4\` ON \`content_statistics_cache\``,
    );
    await queryRunner.query(`DROP TABLE \`content_statistics_cache\``);
  }
}
