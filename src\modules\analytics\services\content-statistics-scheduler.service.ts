import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

@Injectable()
export class ContentStatisticsSchedulerService implements OnModuleInit {
  private readonly logger = new Logger(ContentStatisticsSchedulerService.name);

  constructor(
    @InjectQueue('content-statistics')
    private contentStatisticsQueue: Queue,
  ) {}

  async onModuleInit() {
    // Setup recurring job for content statistics refresh
    await this.setupContentStatisticsJob();
    // Setup periodic job for all-time ranking stats update
    // await this.setupAllTimeRankingStatsJob();
  }

  private async setupContentStatisticsJob(): Promise<void> {
    try {
      // Remove existing repeatable jobs first
      const repeatableJobs =
        await this.contentStatisticsQueue.getRepeatableJobs();
      for (const job of repeatableJobs) {
        if (job.name === 'refresh-cache') {
          await this.contentStatisticsQueue.removeRepeatableByKey(job.key);
        }
      }

      // Add new repeatable job every 20 seconds
      await this.contentStatisticsQueue.add(
        'refresh-cache',
        {},
        {
          repeat: {
            every: 20000, // 20 seconds
          },
          removeOnComplete: 10, // Keep last 10 completed jobs
          removeOnFail: 5, // Keep last 5 failed jobs
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      );

      this.logger.log(
        'Content statistics recurring job setup completed (every 20s)',
      );
    } catch (error) {
      this.logger.error('Failed to setup content statistics job:', error);
      throw error;
    }
  }

  private async setupAllTimeRankingStatsJob(): Promise<void> {
    try {
      // Remove existing repeatable jobs first
      const repeatableJobs =
        await this.contentStatisticsQueue.getRepeatableJobs();
      for (const job of repeatableJobs) {
        if (job.name === 'update-all-time-stats') {
          await this.contentStatisticsQueue.removeRepeatableByKey(job.key);
        }
      }

      // Only add job to update all-time stats every 6 hours
      // Daily stats are handled real-time by ContentAnalyticsProcessor
      await this.contentStatisticsQueue.add(
        'update-all-time-stats',
        {},
        {
          repeat: {
            pattern: '0 */6 * * *', // Every 6 hours
          },
          removeOnComplete: 10,
          removeOnFail: 5,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      );

      this.logger.log(
        'All-time ranking stats recurring job setup completed (every 6 hours)',
      );
    } catch (error) {
      this.logger.error('Failed to setup ranking stats jobs:', error);
      throw error;
    }
  }

  /**
   * Manually trigger cache refresh
   */
  async triggerCacheRefresh(): Promise<void> {
    await this.contentStatisticsQueue.add('refresh-cache', {});
    this.logger.log('Manual content statistics cache refresh triggered');
  }

  /**
   * Manually trigger daily stats update
   */
  async triggerDailyStatsUpdate(date?: string): Promise<void> {
    const targetDate = date || new Date().toISOString().split('T')[0];
    await this.contentStatisticsQueue.add('update-daily-stats', {
      date: targetDate,
    });
    this.logger.log(`Manual daily stats update triggered for ${targetDate}`);
  }

  /**
   * Manually trigger all-time stats update
   */
  async triggerAllTimeStatsUpdate(): Promise<void> {
    await this.contentStatisticsQueue.add('update-all-time-stats', {});
    this.logger.log('Manual all-time stats update triggered');
  }

  /**
   * Get job queue status
   */
  async getQueueStatus(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    repeatableJobs: any[];
  }> {
    const [waiting, active, completed, failed, repeatableJobs] =
      await Promise.all([
        this.contentStatisticsQueue.getWaiting(),
        this.contentStatisticsQueue.getActive(),
        this.contentStatisticsQueue.getCompleted(),
        this.contentStatisticsQueue.getFailed(),
        this.contentStatisticsQueue.getRepeatableJobs(),
      ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      repeatableJobs,
    };
  }
}
