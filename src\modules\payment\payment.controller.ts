import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { SubscriptionPlanService } from '../admin/payment-management/services/subscription-plan.service';
import { CreatePaymentIntentDto } from './dtos/create-payment-intent.dto';
import { PaymentService } from '../admin/payment-management/services/payment.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AnalyticsQueueService } from '../analytics/services/analytics-queue.service';

@ApiTags('User: Payment')
@Controller('payment')
export class PaymentController {
  constructor(
    private readonly subscriptionPlanService: SubscriptionPlanService,
    private readonly paymentService: PaymentService,
    private readonly analyticsQueueService: AnalyticsQueueService,
  ) {}
  @Get('payment-plans')
  @ApiOperation({ summary: 'Get payment plan' })
  @ApiResponse({ status: 200, description: 'Get payment plan' })
  getPaymentPlan(@Query('currency') currency: string) {
    return this.subscriptionPlanService.getPaymentPlans(currency);
  }

  @Post('create-payment-intent')
  @ApiOperation({ summary: 'Create payment intent' })
  @ApiResponse({ status: 200, description: 'Create payment intent' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async createPaymentIntent(
    @Body() body: CreatePaymentIntentDto,
    @Req() req: any,
  ) {
    const result = await this.paymentService.createPaymentIntent(
      body,
      req.user.id,
      req,
    );

    // Track user analytics for payment
    if (result && req.user?.id) {
      try {
        // Determine if this is first purchase or repurchase
        const paymentType = await this.determinePaymentType(req.user.id);

        // Get subscription plan to determine amount
        const subscriptionPlan = await this.subscriptionPlanService.findOne(
          body.planId,
        );

        const platform = req.headers['user-agent']
          ?.toLowerCase()
          .includes('mobile')
          ? 'mobile'
          : 'pc';

        const baseActivityData = {
          userId: req.user.id,
          timestamp: Date.now(),
          language: body.languageId, // Required languageId from request
          platform: platform,
          metadata: {
            paymentId: result.id || result.paymentId,
            amount: Number(subscriptionPlan?.price || 0),
            currency: body.currency || 'USD',
            region: body.region || 'US', // Add region for exchange rate
            subscriptionPlanId: body.planId,
            userAgent: req.headers['user-agent'],
          },
        };

        // 1. Track Payment Attempt
        await this.analyticsQueueService.trackUserActivity({
          ...baseActivityData,
          activityType:
            paymentType === 'first_purchase'
              ? 'first_purchase_attempt'
              : 'repurchase_attempt',
        });

        // 2. Track Payment Success (assumed success for now)
        await this.analyticsQueueService.trackUserActivity({
          ...baseActivityData,
          activityType:
            paymentType === 'first_purchase'
              ? 'first_purchase_success'
              : 'repurchase_success',
        });
      } catch (error) {
        // Don't fail payment if analytics fails
        console.error('Failed to track user analytics:', error);
      }
    }

    return result;
  }

  private async determinePaymentType(
    userId: number,
  ): Promise<'first_purchase' | 'repurchase'> {
    // Check if user has any successful payments before
    const hasPayments = await this.paymentService.hasSuccessfulPayments(userId);
    return hasPayments ? 'repurchase' : 'first_purchase';
  }
}
