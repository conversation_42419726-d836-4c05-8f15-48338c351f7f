import { Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAdminAuthGuard } from '../auth-admin/guards/jwt-admin-auth.guard';
import { ContentStatisticsService } from '../../analytics/services/content-statistics.service';
import { ContentStatisticsSchedulerService } from '../../analytics/services/content-statistics-scheduler.service';
import {
  ContentStatisticsQueryDto,
  ContentStatisticsListResponseDto,
  CacheInfoResponseDto,
  QueueStatusResponseDto,
  DateRangeAnalyticsListResponseDto,
} from '../../analytics/dtos/content-statistics.dto';

@ApiTags('Admin: Content Ranking')
@Controller('admin/content-ranking')
@ApiBearerAuth()
@UseGuards(JwtAdminAuthGuard)
export class ContentRankingController {
  constructor(
    private readonly contentStatisticsService: ContentStatisticsService,
    private readonly schedulerService: ContentStatisticsSchedulerService,
  ) {}

  @Get('statistics')
  @ApiOperation({
    summary: 'Get content statistics ranking',
    description:
      'Get comprehensive content statistics including views, episodes, and ranking data from cache (updated every 20s)',
  })
  @ApiResponse({
    status: 200,
    description: 'Content statistics retrieved successfully',
    type: ContentStatisticsListResponseDto,
  })
  async getContentStatistics(
    @Query() query: ContentStatisticsQueryDto,
  ): Promise<ContentStatisticsListResponseDto> {
    const result =
      await this.contentStatisticsService.getCachedContentStatistics({
        type: query.type,
        author: query.author,
        genre: query.genre,
        series: query.series,
        startDate: query.startDate,
        endDate: query.endDate,
        limit: query.limit || 20,
        offset: query.offset || 0,
      });

    const cacheInfo = await this.contentStatisticsService.getCacheInfo();

    return {
      data: result.data.map((item) => ({
        contentId: item.contentId,
        title: item.title,
        releaseDate: item.releaseDate,
        type: item.type,
        episodes: item.episodes,
        author: item.author,
        series: item.series,
        genre: item.genre,
        freeEpisodeRead: item.freeEpisodeRead,
        paidEpisodeRead: item.paidEpisodeRead,
        totalEpisodeRead: item.totalEpisodeRead,
        lastUpdated: item.lastUpdated,
      })),
      total: result.total,
      limit: query.limit || 20,
      offset: query.offset || 0,
      lastUpdated: result.lastUpdated,
      cacheAge: cacheInfo.cacheAge,
    };
  }

  @Get('cache-info')
  @ApiOperation({
    summary: 'Get cache information',
    description: 'Get information about the content statistics cache status',
  })
  @ApiResponse({
    status: 200,
    description: 'Cache information retrieved successfully',
    type: CacheInfoResponseDto,
  })
  async getCacheInfo(): Promise<CacheInfoResponseDto> {
    return await this.contentStatisticsService.getCacheInfo();
  }

  @Post('refresh-cache')
  @ApiOperation({
    summary: 'Manually refresh content statistics cache',
    description: 'Trigger an immediate refresh of the content statistics cache',
  })
  @ApiResponse({
    status: 200,
    description: 'Cache refresh triggered successfully',
  })
  async refreshCache(): Promise<{ message: string; triggeredAt: Date }> {
    await this.schedulerService.triggerCacheRefresh();
    return {
      message: 'Content statistics cache refresh triggered successfully',
      triggeredAt: new Date(),
    };
  }

  @Get('queue-status')
  @ApiOperation({
    summary: 'Get job queue status',
    description:
      'Get the status of the content statistics background job queue',
  })
  @ApiResponse({
    status: 200,
    description: 'Queue status retrieved successfully',
    type: QueueStatusResponseDto,
  })
  async getQueueStatus(): Promise<QueueStatusResponseDto> {
    return await this.schedulerService.getQueueStatus();
  }

  @Get('top-content')
  @ApiOperation({
    summary: 'Get top performing content',
    description: 'Get top content by various metrics (views, episodes, etc.)',
  })
  @ApiQuery({
    name: 'metric',
    enum: [
      'totalEpisodeRead',
      'freeEpisodeRead',
      'paidEpisodeRead',
      'episodes',
    ],
    required: false,
    description: 'Metric to rank by (default: totalEpisodeRead)',
  })
  @ApiQuery({
    name: 'limit',
    type: Number,
    required: false,
    description: 'Number of top items to return (default: 10)',
  })
  @ApiResponse({
    status: 200,
    description: 'Top content retrieved successfully',
    type: ContentStatisticsListResponseDto,
  })
  async getTopContent(
    @Query('metric') metric: string = 'totalEpisodeRead',
    @Query('limit') limit: number = 10,
  ): Promise<ContentStatisticsListResponseDto> {
    // Validate metric parameter
    const validMetrics = [
      'totalEpisodeRead',
      'freeEpisodeRead',
      'paidEpisodeRead',
      'episodes',
    ];
    if (!validMetrics.includes(metric)) {
      metric = 'totalEpisodeRead';
    }

    const result =
      await this.contentStatisticsService.getCachedContentStatistics({
        limit: Math.min(limit, 100), // Cap at 100
        offset: 0,
      });

    const cacheInfo = await this.contentStatisticsService.getCacheInfo();

    // Sort by the specified metric
    const sortedData = result.data.sort((a, b) => {
      const valueA = a[metric as keyof typeof a] as number;
      const valueB = b[metric as keyof typeof b] as number;
      return valueB - valueA;
    });

    return {
      data: sortedData.slice(0, limit).map((item) => ({
        contentId: item.contentId,
        title: item.title,
        releaseDate: item.releaseDate,
        type: item.type,
        episodes: item.episodes,
        author: item.author,
        series: item.series,
        genre: item.genre,
        freeEpisodeRead: item.freeEpisodeRead,
        paidEpisodeRead: item.paidEpisodeRead,
        totalEpisodeRead: item.totalEpisodeRead,
        lastUpdated: item.lastUpdated,
      })),
      total: result.total,
      limit,
      offset: 0,
      lastUpdated: result.lastUpdated,
      cacheAge: cacheInfo.cacheAge,
    };
  }

  @Get('analytics-summary')
  @ApiOperation({
    summary: 'Get content analytics summary',
    description: 'Get aggregated summary statistics for all content',
  })
  @ApiResponse({
    status: 200,
    description: 'Analytics summary retrieved successfully',
  })
  async getAnalyticsSummary(): Promise<{
    totalContent: number;
    totalEpisodes: number;
    totalViews: number;
    totalFreeViews: number;
    totalPaidViews: number;
    averageEpisodesPerContent: number;
    averageViewsPerContent: number;
    adultContentPercentage: number;
    ongoingContentPercentage: number;
    lastUpdated: Date;
    cacheAge: number;
  }> {
    const result =
      await this.contentStatisticsService.getCachedContentStatistics({
        limit: 999999, // Get all content for summary
      });

    const cacheInfo = await this.contentStatisticsService.getCacheInfo();

    const summary = result.data.reduce(
      (acc, item) => {
        acc.totalContent++;
        acc.totalEpisodes += item.episodes;
        acc.totalViews += item.totalEpisodeRead;
        acc.totalFreeViews += item.freeEpisodeRead;
        acc.totalPaidViews += item.paidEpisodeRead;

        if (item.type === 'adult') acc.adultContent++;
        if (item.series === 'ongoing') acc.ongoingContent++;

        return acc;
      },
      {
        totalContent: 0,
        totalEpisodes: 0,
        totalViews: 0,
        totalFreeViews: 0,
        totalPaidViews: 0,
        adultContent: 0,
        ongoingContent: 0,
      },
    );

    return {
      totalContent: summary.totalContent,
      totalEpisodes: summary.totalEpisodes,
      totalViews: summary.totalViews,
      totalFreeViews: summary.totalFreeViews,
      totalPaidViews: summary.totalPaidViews,
      averageEpisodesPerContent:
        summary.totalContent > 0
          ? Math.round((summary.totalEpisodes / summary.totalContent) * 100) /
            100
          : 0,
      averageViewsPerContent:
        summary.totalContent > 0
          ? Math.round((summary.totalViews / summary.totalContent) * 100) / 100
          : 0,
      adultContentPercentage:
        summary.totalContent > 0
          ? Math.round((summary.adultContent / summary.totalContent) * 10000) /
            100
          : 0,
      ongoingContentPercentage:
        summary.totalContent > 0
          ? Math.round(
              (summary.ongoingContent / summary.totalContent) * 10000,
            ) / 100
          : 0,
      lastUpdated: result.lastUpdated,
      cacheAge: cacheInfo.cacheAge,
    };
  }

  @Get('analytics-by-date')
  @ApiOperation({
    summary: 'Get content analytics by date range',
    description:
      'Get detailed content analytics data filtered by date range with daily breakdown',
  })
  @ApiResponse({
    status: 200,
    description: 'Date range analytics retrieved successfully',
    type: DateRangeAnalyticsListResponseDto,
  })
  async getAnalyticsByDateRange(
    @Query() query: ContentStatisticsQueryDto,
  ): Promise<DateRangeAnalyticsListResponseDto> {
    // Validate date range
    if (query.startDate && query.endDate) {
      const startDate = new Date(query.startDate);
      const endDate = new Date(query.endDate);

      if (startDate > endDate) {
        throw new Error('Start date must be before or equal to end date');
      }

      // Limit to maximum 90 days range for performance
      const daysDiff = Math.ceil(
        (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
      );
      if (daysDiff > 90) {
        throw new Error('Date range cannot exceed 90 days');
      }
    }

    const result =
      await this.contentStatisticsService.getContentAnalyticsByDateRange({
        startDate: query.startDate,
        endDate: query.endDate,
        type: query.type,
        author: query.author,
        genre: query.genre,
        series: query.series,
        limit: query.limit || 20,
        offset: query.offset || 0,
      });

    return {
      data: result.data,
      total: result.total,
      limit: query.limit || 20,
      offset: query.offset || 0,
      startDate: query.startDate || '',
      endDate: query.endDate || '',
      executedAt: new Date(),
    };
  }

  @Post('populate-ranking-stats')
  @ApiOperation({
    summary: 'Populate ranking stats table with historical data',
    description:
      'Initialize content_ranking_stats table with historical data from content_analytics',
  })
  @ApiResponse({
    status: 200,
    description: 'Ranking stats population triggered successfully',
  })
  async populateRankingStats(): Promise<{
    message: string;
    triggeredAt: Date;
  }> {
    await this.schedulerService.triggerAllTimeStatsUpdate();

    // Trigger daily stats for last 30 days
    const today = new Date();
    const promises: Promise<void>[] = [];

    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      promises.push(this.schedulerService.triggerDailyStatsUpdate(dateStr));
    }

    await Promise.all(promises);

    return {
      message:
        'Ranking stats population triggered successfully (all-time + last 30 days)',
      triggeredAt: new Date(),
    };
  }

  @Post('trigger-daily-stats')
  @ApiOperation({
    summary: 'Manually trigger daily stats update',
    description:
      'Trigger an immediate update of daily ranking statistics for a specific date',
  })
  @ApiResponse({
    status: 200,
    description: 'Daily stats update triggered successfully',
  })
  async triggerDailyStats(
    @Query('date') date?: string,
  ): Promise<{ message: string; date: string; triggeredAt: Date }> {
    const targetDate = date || new Date().toISOString().split('T')[0];
    await this.schedulerService.triggerDailyStatsUpdate(targetDate);

    return {
      message: 'Daily stats update triggered successfully',
      date: targetDate,
      triggeredAt: new Date(),
    };
  }

  @Post('trigger-all-time-stats')
  @ApiOperation({
    summary: 'Manually trigger all-time stats update',
    description: 'Trigger an immediate update of all-time ranking statistics',
  })
  @ApiResponse({
    status: 200,
    description: 'All-time stats update triggered successfully',
  })
  async triggerAllTimeStats(): Promise<{ message: string; triggeredAt: Date }> {
    await this.schedulerService.triggerAllTimeStatsUpdate();

    return {
      message: 'All-time stats update triggered successfully',
      triggeredAt: new Date(),
    };
  }
}
