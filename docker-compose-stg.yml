version: '3.8'
services:
  app:
    container_name: trunk_app_stg
    build:
      args:
        - BUILD_ENV=prod
      context: .
      dockerfile: Dockerfile
    ports:
      - '3006:3005'
    depends_on:
      - db
      - redis
    networks:
      - nest-network
    restart: unless-stopped
    volumes:
      - ./uploads:/app/uploads
    environment:
      - TZ=Asia/Ho_Chi_Minh
    env_file:
      - .env.stg

  migration:
    container_name: trunk_migration_stg
    build:
      args:
        - BUILD_ENV=stg
      context: .
      dockerfile: Dockerfile
    depends_on:
      - db
    networks:
      - nest-network
    command: >
      sh -c "
        npm run migration:run:prod"
    restart: 'no'
    environment:
      - TZ=Asia/Ho_Chi_Minh
    env_file:
      - .env.stg

  db:
    container_name: trunk_db_stg
    image: 'mysql:8.0.19'
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    ports:
      - '33072:3306'
    volumes:
      - db-store:/var/lib/mysql
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost']
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - nest-network
    env_file:
      - .env.stg

  redis:
    container_name: trunk_redis_stg
    image: redis:latest
    ports:
      - '6379'
    restart: always
    volumes:
      - redis-store:/data
    networks:
      - nest-network
  mailhog:
    image: mailhog/mailhog
    container_name: mailhog_stg
    ports:
      - '1027:1025'
      - '8027:8025'
    networks:
      - nest-network
    env_file:
      - .env.stg

volumes:
  db-store:
  redis-store:

networks:
  nest-network:
    driver: bridge
