import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { DisplayRepository } from '../../../database/repositories/display.repository';
import { CreateDisplayMultipleDto } from './dtos/create-display.dto';
import {
  CommonStatus,
  ContentStatus,
  DisplayStatus,
} from '../../../common/status.enum';
import { In, Not } from 'typeorm';
import { ContentRepository } from '../../../database/repositories/content.repository';
import { MESSAGE_CONFIG } from '../../../common/message.config';
import { DisplayType } from '../../../common/common.config';
import { GenreRepository } from '../../../database/repositories/genre.repository';
import { EpisodeEntity } from '../../../database/entities/episode.entity';
import { UserContentEntity } from '../../../database/entities/user_content.entity';

interface DisplayQueryOptions {
  type: DisplayType;
  userId?: number;
  forUser?: boolean;
}

@Injectable()
export class DisplayManagementService {
  constructor(
    private readonly displayRepository: DisplayRepository,
    private readonly contentRepository: ContentRepository,
    private readonly genreRepository: GenreRepository,
  ) {}

  async createDisplay(createDisplayDto: CreateDisplayMultipleDto) {
    await this.validateContentExists(createDisplayDto);
    const { newDisplays, oldDisplays } =
      await this.categorizeDisplays(createDisplayDto);
    await this.deactivateUnusedDisplays(createDisplayDto);
    const newDisplayEntities = await this.createNewDisplays(
      newDisplays,
      createDisplayDto,
    );
    await this.updateExistingDisplays(oldDisplays, createDisplayDto);

    return this.formatCreateResponse(oldDisplays, newDisplayEntities);
  }

  async getDisplay(type: DisplayType, userId?: number) {
    return this.getDisplayInternal({ type, userId, forUser: false });
  }

  async getDisplayForUser(type: DisplayType, userId?: number) {
    return this.getDisplayInternal({ type, userId, forUser: true });
  }

  private async getDisplayInternal(options: DisplayQueryOptions) {
    const { type } = options;

    if (type !== DisplayType.GENRE_DISPLAY) {
      return await this.getNonGenreDisplays(options);
    }

    return await this.getGenreDisplays(options);
  }

  private async getNonGenreDisplays(options: DisplayQueryOptions) {
    const { type, userId, forUser } = options;

    const query = this.displayRepository
      .createQueryBuilder('display')
      .addSelect((subQuery) => {
        let episodeSubQuery = subQuery
          .select('COUNT(episode.id)', 'countEpisode')
          .from(EpisodeEntity, 'episode')
          .where('episode.contentId = content.id');

        if (forUser) {
          episodeSubQuery = episodeSubQuery
            .andWhere('episode.status = :status', {
              status: CommonStatus.ACTIVE,
            })
            .andWhere('episode.expectedReleaseDate <= :date', {
              date: new Date().toISOString(),
            });
        }

        return episodeSubQuery;
      }, 'countEpisode')
      .leftJoinAndSelect('display.content', 'content')
      .leftJoinAndSelect('display.genre', 'genre');

    if (userId) {
      query.addSelect((subQuery) => {
        return subQuery
          .select('COUNT(userContent.id)', 'countUserContent')
          .from(UserContentEntity, 'userContent')
          .where('userContent.contentId = content.id');
      }, 'countUserContent');
      query
        .leftJoinAndSelect('content.userContents', 'userContents')
        .leftJoinAndSelect('userContents.episode', 'episode')
        .andWhere('userContents.user = :userId', { userId });
    }

    query
      .where('display.type = :type', { type })
      .andWhere('display.status = :status', { status: DisplayStatus.ACTIVE });

    if (forUser) {
      query.andWhere('content.expectedReleaseDate <= :date', {
        date: new Date().toISOString(),
      });
    }

    query.orderBy('display.order', 'ASC');

    const { entities: displays, raw } = await query.getRawAndEntities();

    // Merge count data into displays
    displays.forEach((display, index) => {
      (display as any).countEpisode = parseInt(raw[index].countEpisode) || 0;
      (display as any).countUserContent =
        parseInt(raw[index].countUserContent) || 0;
    });

    return { displays };
  }

  private async getGenreDisplays(options: DisplayQueryOptions) {
    const { userId, forUser } = options;

    const query = this.genreRepository
      .createQueryBuilder('genre')
      .leftJoinAndSelect('genre.displays', 'display')
      .leftJoinAndSelect('display.content', 'content');

    if (userId) {
      query
        .leftJoinAndSelect('content.userContents', 'userContents')
        .leftJoinAndSelect('userContents.episode', 'episode')
        .andWhere('userContents.user = :userId', { userId });
    }

    query
      .andWhere('display.type = :type', { type: DisplayType.GENRE_DISPLAY })
      .andWhere('content.status = :status', { status: ContentStatus.ACTIVE })
      .andWhere('content.expectedReleaseDate <= :date', {
        date: new Date().toISOString(),
      })
      .andWhere('display.status = :status', { status: DisplayStatus.ACTIVE })
      .orderBy('display.order', 'ASC');

    const genres = await query.getMany();

    // Attach counts to genres using batch queries
    await this.attachCountsToGenres(genres, userId, forUser);

    return { genres };
  }

  private async attachCountsToGenres(
    genres: any[],
    userId?: number,
    forUser?: boolean,
  ) {
    // Collect all content IDs
    const contentIds: number[] = [];
    for (const genre of genres) {
      if (genre.displays && genre.displays.length > 0) {
        for (const display of genre.displays) {
          if (display.content) {
            contentIds.push(display.content.id);
          }
        }
      }
    }

    if (contentIds.length === 0) return;

    // Get episode counts in batch
    const episodeCounts = await this.getEpisodeCountsBatch(contentIds, forUser);

    // Get user content counts in batch if userId provided
    let userContentCounts = new Map<number, number>();
    if (userId) {
      userContentCounts = await this.getUserContentCountsBatch(
        contentIds,
        userId,
      );
    }

    // Assign counts to displays
    for (const genre of genres) {
      if (genre.displays && genre.displays.length > 0) {
        for (const display of genre.displays) {
          if (display.content) {
            display.countEpisode = episodeCounts.get(display.content.id) || 0;
            display.countUserContent =
              userContentCounts.get(display.content.id) || 0;
          }
        }
      }
    }
  }

  private async getEpisodeCountsBatch(
    contentIds: number[],
    forUser?: boolean,
  ): Promise<Map<number, number>> {
    let query = this.displayRepository.manager
      .createQueryBuilder()
      .select('episode.contentId', 'contentId')
      .addSelect('COUNT(episode.id)', 'count')
      .from(EpisodeEntity, 'episode')
      .where('episode.contentId IN (:...contentIds)', { contentIds });

    if (forUser) {
      query = query
        .andWhere('episode.status = :status', { status: CommonStatus.ACTIVE })
        .andWhere('episode.expectedReleaseDate <= :date', {
          date: new Date().toISOString(),
        });
    }

    const results = await query.groupBy('episode.contentId').getRawMany();

    return new Map(
      results.map((r) => [parseInt(r.contentId), parseInt(r.count)]),
    );
  }

  private async getUserContentCountsBatch(
    contentIds: number[],
    userId: number,
  ): Promise<Map<number, number>> {
    const results = await this.displayRepository.manager
      .createQueryBuilder()
      .select('userContent.contentId', 'contentId')
      .addSelect('COUNT(userContent.id)', 'count')
      .from(UserContentEntity, 'userContent')
      .where('userContent.contentId IN (:...contentIds)', { contentIds })
      .andWhere('userContent.userId = :userId', { userId })
      .groupBy('userContent.contentId')
      .getRawMany();

    return new Map(
      results.map((r) => [parseInt(r.contentId), parseInt(r.count)]),
    );
  }

  private async validateContentExists(
    createDisplayDto: CreateDisplayMultipleDto,
  ) {
    const { displays } = createDisplayDto;
    const displayIds = displays.map((display) => display.contentId);

    const contents = await this.contentRepository.find({
      where: { id: In(displayIds) },
      relations: ['genre'],
    });

    if (contents.length !== displayIds.length) {
      throw new NotFoundException(MESSAGE_CONFIG.CONTENT_NOT_FOUND);
    }

    return contents;
  }

  private async categorizeDisplays(createDisplayDto: CreateDisplayMultipleDto) {
    const { displays, type } = createDisplayDto;

    // Get existing displays
    const oldDisplays = await this.displayRepository.find({
      where: {
        content: { id: In(displays.map((display) => display.contentId)) },
        type: type,
      },
      relations: ['content', 'content.genre'],
    });

    // Get new displays not exist in old displays
    const newDisplays = displays.filter(
      (display) =>
        !oldDisplays.some((old) => old.content.id === display.contentId),
    );

    return { newDisplays, oldDisplays };
  }

  private async deactivateUnusedDisplays(
    createDisplayDto: CreateDisplayMultipleDto,
  ) {
    const { displays, type } = createDisplayDto;
    const displayIds = displays.map((display) => display.contentId);

    // Update status of displays not in the new list to inactive
    await this.displayRepository.update(
      {
        id: Not(In(displayIds)),
        type: type,
      },
      { status: DisplayStatus.INACTIVE },
    );
  }

  private async createNewDisplays(
    newDisplays: any[],
    createDisplayDto: CreateDisplayMultipleDto,
  ) {
    if (newDisplays.length === 0) return [];

    const contents = await this.validateContentExists(createDisplayDto);

    const newDisplayEntities = newDisplays.map((display) =>
      this.displayRepository.create({
        type: createDisplayDto.type,
        content: { id: display.contentId },
        genre: {
          id: contents.find((content) => content.id === display.contentId)
            ?.genre.id,
        },
        status: DisplayStatus.ACTIVE,
        order: display.order,
      }),
    );

    return await this.displayRepository.save(newDisplayEntities);
  }

  private async updateExistingDisplays(
    oldDisplays: any[],
    createDisplayDto: CreateDisplayMultipleDto,
  ) {
    const { displays } = createDisplayDto;

    await Promise.all(
      oldDisplays.map((display) =>
        this.displayRepository.update(display.id, {
          status: DisplayStatus.ACTIVE,
          order: displays.find(
            (createDisplay) => createDisplay.contentId === display.content.id,
          )?.order,
        }),
      ),
    ).catch((error) => {
      throw new BadRequestException(error.message);
    });
  }

  private formatCreateResponse(oldDisplays: any[], newDisplayEntities: any[]) {
    return {
      message: MESSAGE_CONFIG.DISPLAY_CREATED_SUCCESSFULLY,
      updatedIds: oldDisplays.map((display) => display.id),
      createdIds: newDisplayEntities.map((display) => display.id),
    };
  }
}
