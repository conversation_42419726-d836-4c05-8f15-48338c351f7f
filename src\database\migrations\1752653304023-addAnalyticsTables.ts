import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAnalyticsTables1752653304023 implements MigrationInterface {
  name = 'AddAnalyticsTables1752653304023';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`user_analytics\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`date\` date NOT NULL, \`hour\` tinyint NOT NULL DEFAULT '0', \`platform\` enum ('pc', 'mobile') NULL, \`activeUsers\` int NOT NULL DEFAULT '0', \`newRegistrations\` int NOT NULL DEFAULT '0', \`deletedAccounts\` int NOT NULL DEFAULT '0', \`loginCount\` int NOT NULL DEFAULT '0', \`uniqueLogins\` int NOT NULL DEFAULT '0', \`metadata\` json NULL, INDEX \`IDX_1c80252f0d825c8c6a6e8f63c2\` (\`date\`), UNIQUE INDEX \`IDX_8635d6b5222896bdcef83e2faf\` (\`date\`, \`hour\`, \`platform\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`payment_analytics\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`date\` date NOT NULL, \`hour\` tinyint NOT NULL DEFAULT '0', \`paymentType\` enum ('first_purchase', 'repurchase', 'subscription') NOT NULL, \`platform\` enum ('pc', 'mobile') NULL, \`currency\` varchar(3) NOT NULL DEFAULT 'USD', \`totalRequests\` int NOT NULL DEFAULT '0', \`successfulPayments\` int NOT NULL DEFAULT '0', \`failedPayments\` int NOT NULL DEFAULT '0', \`totalAmount\` decimal(15,2) NOT NULL DEFAULT '0.00', \`uniqueUsers\` int NOT NULL DEFAULT '0', \`metadata\` json NULL, INDEX \`IDX_da9dfbe0b0f227df7a8bf1f02e\` (\`date\`), UNIQUE INDEX \`IDX_2531c89bae87452c07e5f9e925\` (\`date\`, \`hour\`, \`paymentType\`, \`platform\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`content_analytics\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`date\` date NOT NULL, \`hour\` tinyint NOT NULL DEFAULT '0', \`contentId\` int NULL, \`episodeId\` int NULL, \`viewCount\` int NOT NULL DEFAULT '0', \`uniqueViewers\` int NOT NULL DEFAULT '0', \`totalReadTime\` bigint NOT NULL DEFAULT '0', \`averageReadTime\` decimal(10,2) NOT NULL DEFAULT '0.00', \`metadata\` json NULL, INDEX \`IDX_e2464d42e330e00ee2822eeae9\` (\`date\`), INDEX \`IDX_230fb3d27fb311a11d911610d3\` (\`contentId\`), INDEX \`IDX_fd586ff77057b60164fcc0cf1b\` (\`episodeId\`), UNIQUE INDEX \`IDX_686ec579592e61793018994f7a\` (\`date\`, \`hour\`, \`contentId\`, \`episodeId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_686ec579592e61793018994f7a\` ON \`content_analytics\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_fd586ff77057b60164fcc0cf1b\` ON \`content_analytics\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_230fb3d27fb311a11d911610d3\` ON \`content_analytics\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_e2464d42e330e00ee2822eeae9\` ON \`content_analytics\``,
    );
    await queryRunner.query(`DROP TABLE \`content_analytics\``);
    await queryRunner.query(
      `DROP INDEX \`IDX_2531c89bae87452c07e5f9e925\` ON \`payment_analytics\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_da9dfbe0b0f227df7a8bf1f02e\` ON \`payment_analytics\``,
    );
    await queryRunner.query(`DROP TABLE \`payment_analytics\``);
    await queryRunner.query(
      `DROP INDEX \`IDX_8635d6b5222896bdcef83e2faf\` ON \`user_analytics\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_1c80252f0d825c8c6a6e8f63c2\` ON \`user_analytics\``,
    );
    await queryRunner.query(`DROP TABLE \`user_analytics\``);
  }
}
