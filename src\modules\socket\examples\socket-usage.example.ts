import { Injectable } from '@nestjs/common';
import { SocketService } from '../socket.service';
import { EAiGenPromptByDescriptionType } from 'src/common/ai-webtoon.enum';

/**
 * Example service showing how to use SocketService globally
 * Since SocketModule is marked as @Global(), you can inject SocketService
 * into any service without importing SocketModule in your module
 */
@Injectable()
export class ExampleService {
  constructor(private readonly socketService: SocketService) {}

  /**
   * Example: Emit chapter update notification
   */
  async notifyChapterUpdate(chapterId: number) {
    // This will emit to all connected clients
    this.socketService.emitUpdateChapter(chapterId);
    console.log(`Notified clients about chapter ${chapterId} update`);
  }

  /**
   * Example: Emit synthetic data update
   */
  async notifySyntheticDataUpdate(syntheticDataId: number) {
    this.socketService.emitSyntheticData(syntheticDataId);
    console.log(`Notified clients about synthetic data ${syntheticDataId} update`);
  }

  /**
   * Example: Emit prompt generation result
   */
  async notifyPromptGenerated(
    cutId: number,
    prompt: string,
    typePrompt: EAiGenPromptByDescriptionType = EAiGenPromptByDescriptionType.GENERAL_PROMPT
  ) {
    this.socketService.emitPromptByDescription(cutId, typePrompt, prompt);
    console.log(`Notified clients about prompt generation for cut ${cutId}`);
  }

  /**
   * Example: Emit merged preview images
   */
  async notifyMergePreviewImages(chapterId: number, images: string[]) {
    this.socketService.emitMergePreviewImages(chapterId, images);
    console.log(`Notified clients about merged preview images for chapter ${chapterId}`);
  }

  /**
   * Example: Complex workflow with multiple socket emissions
   */
  async processChapterWorkflow(chapterId: number) {
    try {
      // Step 1: Notify chapter processing started
      this.socketService.emitUpdateChapter(chapterId);
      
      // Simulate some processing...
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Step 2: Generate some sample images
      const sampleImages = [
        'https://example.com/image1.jpg',
        'https://example.com/image2.jpg'
      ];
      
      // Step 3: Notify about merged images
      this.socketService.emitMergePreviewImages(chapterId, sampleImages);
      
      // Step 4: Final chapter update
      this.socketService.emitUpdateChapter(chapterId);
      
      console.log(`Chapter ${chapterId} workflow completed`);
    } catch (error) {
      console.error(`Error in chapter ${chapterId} workflow:`, error);
    }
  }
}

/**
 * Example controller showing how to use SocketService in controllers
 */
import { Controller, Post, Param, Body } from '@nestjs/common';

@Controller('example')
export class ExampleController {
  constructor(private readonly socketService: SocketService) {}

  @Post('notify-chapter/:id')
  async notifyChapter(@Param('id') chapterId: string) {
    this.socketService.emitUpdateChapter(parseInt(chapterId));
    return { message: `Notification sent for chapter ${chapterId}` };
  }

  @Post('notify-prompt')
  async notifyPrompt(@Body() body: { cutId: number; prompt: string; type?: EAiGenPromptByDescriptionType }) {
    const { cutId, prompt, type = EAiGenPromptByDescriptionType.GENERAL_PROMPT } = body;
    this.socketService.emitPromptByDescription(cutId, type, prompt);
    return { message: `Prompt notification sent for cut ${cutId}` };
  }
}

/**
 * Usage in other modules:
 * 
 * 1. Since SocketModule is @Global(), you don't need to import it
 * 2. Just inject SocketService in your constructor:
 * 
 * @Injectable()
 * export class YourService {
 *   constructor(private readonly socketService: SocketService) {}
 * 
 *   someMethod() {
 *     this.socketService.emitUpdateChapter(123);
 *   }
 * }
 * 
 * 3. Available methods:
 *   - emitUpdateChapter(chapterId: number)
 *   - emitSyntheticData(syntheticDataId: number)
 *   - emitPromptByDescription(cutId: number, typePrompt: EAiGenPromptByDescriptionType, prompt: string)
 *   - emitMergePreviewImages(chapterId: number, images: string[])
 */
