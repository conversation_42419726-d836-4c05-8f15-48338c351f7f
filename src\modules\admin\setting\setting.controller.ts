import {
  Controller,
  Post,
  UseGuards,
  Body,
  Get,
  Put,
  Query,
  UseInterceptors,
  UploadedFile,
  Delete,
  Param,
} from '@nestjs/common';
import { SettingService } from './setting.service';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiTags,
  ApiConsumes,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAdminAuthGuard } from 'src/modules/admin/auth-admin/guards/jwt-admin-auth.guard';
import { Roles } from '../../auth/decorators/role.decorator';
import { AdminRole } from '../../../common/role.enum';
import { CreateSettingDto } from './dtos/create-setting.dto';
import { UpdateMultipleSettingsDto } from './dtos/update-setting.dto';
import { SettingGroup } from '../../../common/setting.enum';
import { SettingStatus } from '../../../common/status.enum';
import { CreateMultipleSettingMetadataDto } from './dtos/create-metadata.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { UploadImageDto } from './dtos/upload-image.dto';
import { CreateMultipleLanguageDto } from './dtos/create-language.dto';
import { UpdateMultipleLanguageDto } from './dtos/update-language.dto';
import { LanguageFilterDto } from './dtos/language-filter.dto';
import { CreateClassificationDto } from './dtos/create-classification.dto';
import { CreateDateDto } from './dtos/create-date.dto';
import { DeleteMultipleLanguageDto } from './dtos/delete-language.dto';
import { UpdateAutoReplyDto } from './dtos/update-auto-reply.dto';
@ApiTags('Admin Setting')
@ApiBearerAuth()
@UseGuards(JwtAdminAuthGuard)
@Roles(AdminRole.SUPER_ADMIN, AdminRole.ADMIN)
@Controller('admin/setting')
export class SettingController {
  constructor(private readonly settingService: SettingService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new setting' })
  async createSetting(@Body() createSettingDto: CreateSettingDto) {
    return await this.settingService.createSetting(createSettingDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all settings' })
  async getSettings() {
    return await this.settingService.getSettings();
  }

  @Put()
  @ApiOperation({ summary: 'Update multiple settings' })
  async updateMultipleSettings(
    @Body() updateMultipleSettingsDto: UpdateMultipleSettingsDto,
  ) {
    return await this.settingService.updateMultipleSettings(
      updateMultipleSettingsDto,
    );
  }

  @Get('group')
  @ApiOperation({
    summary: 'Get settings by group with optional status filter',
  })
  @ApiQuery({
    name: 'group',
    enum: SettingGroup,
    required: true,
    description: 'Setting group to filter by',
  })
  @ApiQuery({
    name: 'status',
    enum: SettingStatus,
    required: false,
    description:
      'Filter by status (active/inactive). If not provided, returns all statuses',
  })
  async getSettingByGroup(
    @Query('group') group: SettingGroup,
    @Query('status') status?: SettingStatus,
  ) {
    return await this.settingService.getSettingByGroupAdmin(group, status);
  }

  @Post('metadata')
  @ApiOperation({ summary: 'Create a new setting metadata' })
  async createSettingMetadata(
    @Body() createSettingMetadataDto: CreateMultipleSettingMetadataDto,
  ) {
    return await this.settingService.createMultipleSettingMetadata(
      createSettingMetadataDto,
    );
  }

  @Post('upload-image')
  @ApiOperation({ summary: 'Upload image' })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  async uploadImage(@UploadedFile() file: any, @Body() body: UploadImageDto) {
    return await this.settingService.uploadImage(file, body);
  }

  @Delete('image-access-page')
  @ApiOperation({ summary: 'Delete image access page' })
  async deleteImageAccessPage() {
    return await this.settingService.deleteImageAccessPage();
  }

  @Post('language')
  @ApiOperation({ summary: 'Create a new language' })
  async createLanguage(@Body() createLanguageDto: CreateMultipleLanguageDto) {
    return await this.settingService.createMultipleLanguage(createLanguageDto);
  }

  @Get('language')
  @ApiOperation({ summary: 'Get all languages' })
  async getLanguages(@Query() languageFilterDto: LanguageFilterDto) {
    return await this.settingService.getLanguages(languageFilterDto);
  }

  @Put('language')
  @ApiOperation({ summary: 'Update a language' })
  async updateLanguage(@Body() updateLanguageDto: UpdateMultipleLanguageDto) {
    return await this.settingService.updateMultipleLanguage(updateLanguageDto);
  }

  @Delete('language/:id')
  @ApiOperation({ summary: 'Delete a language' })
  async deleteLanguage(@Param('id') id: number) {
    return await this.settingService.deleteLanguage(id);
  }
  @Delete('multiple-language')
  @ApiOperation({ summary: 'Delete multiple language' })
  async deleteMultipleLanguage(
    @Body() deleteMultipleLanguageDto: DeleteMultipleLanguageDto,
  ) {
    return await this.settingService.deleteMultipleLanguage(
      deleteMultipleLanguageDto,
    );
  }

  @Get('content-classification')
  @ApiOperation({ summary: 'Get all content classification' })
  async getContentClassification() {
    return await this.settingService.getContentClassification();
  }

  @Get('content-date')
  @ApiOperation({ summary: 'Get all content date' })
  async getContentDate() {
    return await this.settingService.getContentDate();
  }

  @Post('content-classification')
  @ApiOperation({ summary: 'Create content classification' })
  async createContentClassification(
    @Body() createClassificationDto: CreateClassificationDto,
  ) {
    return await this.settingService.createContentClassification(
      createClassificationDto,
    );
  }

  @Post('content-date')
  @ApiOperation({ summary: 'Create content date' })
  async createContentDate(@Body() createDateDto: CreateDateDto) {
    return await this.settingService.createContentDate(createDateDto);
  }

  @Put('auto-reply')
  @ApiOperation({ summary: 'Update auto reply settings' })
  async updateAutoReply(@Body() updateAutoReplyDto: UpdateAutoReplyDto) {
    return await this.settingService.updateAutoReply(updateAutoReplyDto);
  }
}
