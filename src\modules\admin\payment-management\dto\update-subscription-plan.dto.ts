import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum } from 'class-validator';
import { IsOptional } from 'class-validator';
import { IsNumber, IsString, Min } from 'class-validator';
import { CommonStatus } from '../../../../common/status.enum';

export class UpdateSubscriptionPlanDto {
  @ApiProperty({
    description: 'Name of the subscription plan',
    example: 'Premium Monthly Plan',
  })
  @IsOptional()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Description of the subscription plan',
    example: 'Access to premium content for one month',
  })
  @IsOptional()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Price of the subscription plan',
    example: 29.99,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  price: number;

  @ApiProperty({
    description: 'Duration of the subscription plan in days',
    example: 30,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  duration_day: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
  })
  @IsOptional()
  @IsString()
  currency: string;

  @ApiPropertyOptional({
    description: 'Status of the subscription plan',
    enum: CommonStatus,
    default: CommonStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(CommonStatus)
  status?: CommonStatus;
}
