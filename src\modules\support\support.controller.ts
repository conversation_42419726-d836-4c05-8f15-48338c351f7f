import {
  Controller,
  Post,
  Get,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { SupportService } from './services/support.service';
import { CreateSupportTicketDto } from './dtos/create-support-ticket.dto';
import { UserFollowUpDto } from './dtos/user-follow-up.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { TicketStatus } from '../../database/entities/support-ticket.entity';

@ApiTags('Support')
@Controller('support')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SupportController {
  constructor(private readonly supportService: SupportService) {}

  @Post('tickets')
  @ApiOperation({
    summary: 'Create Support Ticket',
    description: 'Create a new support ticket with auto-reply if enabled',
  })
  @ApiResponse({
    status: 201,
    description: 'Ticket created successfully',
    example: {
      success: true,
      ticketId: 123,
      hasAutoReply: true,
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid category or validation error',
    example: {
      error: 'support/category-not-found',
      message: 'Category not found or inactive',
    },
  })
  async createTicket(
    @Body() createDto: CreateSupportTicketDto,
    @Request() req: any,
  ) {
    return await this.supportService.createSupportTicket(
      createDto,
      req.user.id,
    );
  }

  @Get('tickets')
  @ApiOperation({
    summary: 'Get User Support Tickets',
    description: 'Get paginated list of user support tickets with filters',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    example: 1,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    example: 10,
    description: 'Items per page (max 50)',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: TicketStatus,
    description: 'Filter by ticket status',
  })
  @ApiQuery({
    name: 'category',
    required: false,
    type: String,
    example: 'payment-billing',
    description: 'Filter by category name',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    example: 'payment issue',
    description: 'Search in subject and description',
  })
  @ApiResponse({
    status: 200,
    description: 'User tickets retrieved successfully',
    example: {
      data: [
        {
          id: 123,
          ticketNumber: 'SUP-2025-0001',
          subject: 'Cannot access premium content',
          status: 'open',
          priority: 'medium',
          category: {
            id: 1,
            name: 'content-access',
            title: 'Content Access Issues',
          },
          hasUnreadResponse: true,
          lastResponseAt: '2025-01-22T10:30:00.000Z',
          responseCount: 2,
          lastResponse: {
            id: 456,
            response:
              'Thank you for contacting us. We are looking into your issue...',
            status: 'sent',
            createdAt: '2025-01-22T10:30:00.000Z',
            admin: {
              id: 1,
              username: 'support_admin',
            },
          },
          createdAt: '2025-01-22T09:00:00.000Z',
        },
      ],
      pagination: {
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
      },
    },
  })
  async getUserTickets(
    @Request() req: any,
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 10,
    @Query('status') status?: TicketStatus,
    @Query('category') category?: string,
    @Query('search') search?: string,
  ) {
    // Limit page size to prevent abuse
    limit = Math.min(limit, 50);

    const filters = {};
    if (status) filters['status'] = status;
    if (category) filters['category'] = category;
    if (search) filters['search'] = search;

    return await this.supportService.getUserTickets(
      req.user.id,
      page,
      limit,
      Object.keys(filters).length > 0 ? filters : undefined,
    );
  }

  @Get('tickets/:id')
  @ApiOperation({
    summary: 'Get Support Ticket Detail',
    description:
      'Get detailed view of a specific support ticket and mark responses as read',
  })
  @ApiResponse({
    status: 200,
    description: 'Ticket detail retrieved successfully',
    example: {
      id: 123,
      ticketNumber: 'SUP-2025-0001',
      subject: 'Cannot access premium content',
      description:
        'I have an active subscription but cannot read episode 15 of My Hero Academia',
      attachments: ['https://example.com/screenshot1.png'],
      status: 'open',
      priority: 'medium',
      category: {
        id: 1,
        name: 'content-access',
        title: 'Content Access Issues',
      },
      contextData: {
        contentId: 123,
        episodeId: 456,
        subscriptionId: 789,
        deviceInfo: 'iPhone 14 iOS 16.1',
        errorCode: 'CONTENT_LOCKED_ERROR',
      },
      createdAt: '2025-01-22T09:00:00.000Z',
      updatedAt: '2025-01-22T10:30:00.000Z',
      responses: [
        {
          id: 456,
          response:
            'Thank you for contacting Webtoon Support! We have received your support request...',
          attachments: [],
          status: 'auto_reply',
          responseCompletedTime: '2025-01-22T09:05:00.000Z',
          createdAt: '2025-01-22T09:05:00.000Z',
          admin: {
            id: 1,
            username: 'system',
          },
        },
        {
          id: 457,
          response:
            'We have reviewed your account and subscription status. The issue appears to be...',
          attachments: [],
          status: 'sent',
          responseCompletedTime: '2025-01-22T10:30:00.000Z',
          createdAt: '2025-01-22T10:30:00.000Z',
          admin: {
            id: 2,
            username: 'support_admin',
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Ticket not found or access denied',
    example: {
      error: 'support/ticket-not-found',
      message: 'Ticket not found or access denied',
    },
  })
  async getTicketDetail(
    @Param('id', ParseIntPipe) ticketId: number,
    @Request() req: any,
  ) {
    return await this.supportService.getUserTicketDetail(ticketId, req.user.id);
  }

  @Post('tickets/:id/check-response')
  @ApiOperation({
    summary: 'Check Ticket Response Status',
    description:
      'Check if ticket has completed responses and mark as read (similar to require.md check-answer)',
  })
  @ApiResponse({
    status: 200,
    description: 'Response check completed successfully',
    example: {
      success: true,
    },
  })
  @ApiResponse({
    status: 400,
    description: 'No completed response available',
    example: {
      error: 'support/no-response',
      message: 'No completed response available',
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Ticket not found or access denied',
    example: {
      error: 'support/ticket-not-found',
      message: 'Ticket not found or access denied',
    },
  })
  async checkTicketResponse(
    @Param('id', ParseIntPipe) ticketId: number,
    @Request() req: any,
  ) {
    const success = await this.supportService.checkTicketResponse(
      ticketId,
      req.user.id,
    );

    return { success };
  }

  @Post('tickets/:id/follow-up')
  @ApiOperation({
    summary: 'User Follow-up on Ticket',
    description:
      'Add additional information or follow-up message to an existing ticket',
  })
  @ApiResponse({
    status: 201,
    description: 'Follow-up message added successfully',
    example: {
      success: true,
      responseId: 789,
      ticketStatus: 'pending_user',
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Ticket not found or access denied',
    example: {
      error: 'support/ticket-not-found',
      message: 'Ticket not found or access denied',
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Ticket is closed',
    example: {
      error: 'support/ticket-closed',
      message: 'Cannot add follow-up to closed ticket',
    },
  })
  async addFollowUp(
    @Param('id', ParseIntPipe) ticketId: number,
    @Body() followUpDto: UserFollowUpDto,
    @Request() req: any,
  ) {
    return await this.supportService.addUserFollowUp(
      ticketId,
      followUpDto,
      req.user.id,
    );
  }
}
