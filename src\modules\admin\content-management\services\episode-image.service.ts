import { Injectable } from '@nestjs/common';
import { EpisodeImageRepository } from 'src/database/repositories/episode-image.repository';
import { EpisodeEntity } from 'src/database/entities/episode.entity';
import { EpisodeImageEntity } from 'src/database/entities/episode_image.entity';

export interface ImageDto {
  path: string;
}

interface ImageComparison {
  toDelete: EpisodeImageEntity[];
  toUpdate: { existing: EpisodeImageEntity; new: ImageDto }[];
  toCreate: ImageDto[];
}

@Injectable()
export class EpisodeImageService {
  constructor(
    private readonly episodeImageRepository: EpisodeImageRepository,
  ) {}

  async bulkCreateImages(
    episode: EpisodeEntity,
    images: ImageDto[],
  ): Promise<EpisodeImageEntity[]> {
    if (images.length === 0) {
      return [];
    }

    const episodeImages = images.map((image, index) => ({
      path: image.path,
      episode,
      order: index,
    }));

    return this.episodeImageRepository.save(episodeImages);
  }

  async handleEpisodeImages(
    episode: EpisodeEntity,
    images: ImageDto[],
    isUpdate = false,
  ): Promise<EpisodeImageEntity[]> {
    if (!isUpdate) {
      return this.bulkCreateImages(episode, images);
    }

    // For updates, implement intelligent comparison
    const existingImages = await this.episodeImageRepository.find({
      where: { episode: { id: episode.id } },
      order: { order: 'ASC' },
    });

    const comparison = this.compareImages(existingImages, images);

    // Execute all operations in parallel for better performance
    await Promise.all([
      this.bulkDeleteImages(comparison.toDelete),
      this.bulkUpdateImages(comparison.toUpdate),
    ]);

    // Create new images
    const newImages = await this.bulkCreateImages(episode, comparison.toCreate);

    // Return all current images (updated + new)
    const updatedExistingImages = comparison.toUpdate.map(({ existing }) => {
      return {
        ...existing,
        path:
          comparison.toUpdate.find((u) => u.existing.id === existing.id)?.new
            .path || existing.path,
      };
    });

    return [...updatedExistingImages, ...newImages];
  }

  private compareImages(
    existingImages: EpisodeImageEntity[],
    newImages: ImageDto[],
  ): ImageComparison {
    const toDelete: EpisodeImageEntity[] = [];
    const toUpdate: { existing: EpisodeImageEntity; new: ImageDto }[] = [];
    const toCreate: ImageDto[] = [];

    // Create maps for efficient lookup
    const existingByOrder = new Map<number, EpisodeImageEntity>();
    existingImages.forEach((img) => existingByOrder.set(img.order, img));

    const newByOrder = new Map<number, ImageDto>();
    newImages.forEach((img, index) => {
      newByOrder.set(index, img);
    });

    // Find images to delete (exist in old but not in new)
    existingImages.forEach((existing) => {
      if (!newByOrder.has(existing.order)) {
        toDelete.push(existing);
      }
    });

    // Find images to update or create
    newImages.forEach((newImg, index) => {
      const existing = existingByOrder.get(index);

      if (existing) {
        // Update if path changed
        if (existing.path !== newImg.path) {
          toUpdate.push({ existing, new: newImg });
        }
      } else {
        // Create new image
        toCreate.push(newImg);
      }
    });

    return { toDelete, toUpdate, toCreate };
  }

  private async bulkDeleteImages(
    imagesToDelete: EpisodeImageEntity[],
  ): Promise<void> {
    if (imagesToDelete.length === 0) {
      return;
    }

    const ids = imagesToDelete.map((img) => img.id);
    await this.episodeImageRepository.delete(ids);
  }

  private async bulkUpdateImages(
    imagesToUpdate: { existing: EpisodeImageEntity; new: ImageDto }[],
  ): Promise<void> {
    if (imagesToUpdate.length === 0) {
      return;
    }

    const updatePromises = imagesToUpdate.map(({ existing, new: newImg }) => {
      existing.path = newImg.path;
      return this.episodeImageRepository.save(existing);
    });

    await Promise.all(updatePromises);
  }

  async deleteAllEpisodeImages(episodeId: number): Promise<void> {
    await this.episodeImageRepository.delete({
      episode: { id: episodeId },
    });
  }

  async getEpisodeImages(episodeId: number): Promise<EpisodeImageEntity[]> {
    return this.episodeImageRepository.find({
      where: { episode: { id: episodeId } },
      order: { order: 'ASC' },
    });
  }
}
