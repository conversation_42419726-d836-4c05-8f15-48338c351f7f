import { IsOptional, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { LegalDocumentType } from '../../../database/entities/legal-document.entity';

export class UserLegalDocumentFilterDto {
  @ApiProperty({
    enum: LegalDocumentType,
    required: false,
    description: 'Filter by document type',
  })
  @IsOptional()
  @IsEnum(LegalDocumentType)
  type?: LegalDocumentType;
}
