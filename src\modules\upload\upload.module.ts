import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UploadEntity } from '../../database/entities/upload.entity';
import { UploadRepository } from '../../database/repositories/upload.repository';
import { AnalyticsQueueModule } from '../analytics/analytics-queue.module';

// Services
import { UploadService } from './shared/services/upload.service';
import { FileValidationService } from './shared/services/file-validation.service';
import { FileProcessingService } from './shared/services/file-processing.service';

// Controllers
import { UserUploadController } from './user/user-upload.controller';
import { AdminUploadController } from './admin/admin-upload.controller';
import { MenuModule } from '../admin/menu/menu.module';

// External Upload Queue
import { ExternalUploadQueueModule } from './queues/external-upload-queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UploadEntity]),
    AnalyticsQueueModule,
    MenuModule,
    ExternalUploadQueueModule,
  ],
  controllers: [UserUploadController, AdminUploadController],
  providers: [
    UploadRepository,
    UploadService,
    FileValidationService,
    FileProcessingService,
  ],
  exports: [UploadService, FileProcessingService, ExternalUploadQueueModule],
})
export class UploadModule {}
