import {
  Controller,
  Get,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { NotificationService } from '../services/notification.service';
import { PaginationDto } from '../dtos/pagination.dto';
import { OptionalJwtAuthGuard } from '../../auth/guards/optional-jwt-auth.guard';

@ApiTags('Notifications')
@Controller('notifications')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  // System Notification Endpoints
  @Get('system')
  @ApiOperation({ summary: 'Get active system notifications' })
  @ApiResponse({
    status: 200,
    description: 'Active system notifications retrieved successfully',
  })
  async getActiveSystemNotifications(@Query() paginationDto: PaginationDto) {
    const result = await this.notificationService.getActiveSystemNotifications(
      paginationDto.page,
      paginationDto.limit,
    );
    return {
      status: 'success',
      message: 'System notifications retrieved successfully',
      ...result,
    };
  }

  @Get('system/:id')
  @UseGuards(OptionalJwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary:
      'Get system notification detail (marks as viewed if user is logged in)',
  })
  @ApiResponse({
    status: 200,
    description: 'System notification detail retrieved successfully',
  })
  async getSystemNotificationDetail(
    @Param('id', ParseIntPipe) id: number,
    @Request() req: any,
  ) {
    const userId = req.user?.id;
    const notification =
      await this.notificationService.getSystemNotificationDetail(id, userId);
    return {
      status: 'success',
      message: 'System notification detail retrieved successfully',
      data: notification,
    };
  }

  // User Notification Endpoints
  @Get('user/latest')
  @UseGuards(OptionalJwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get latest user notification with read status',
  })
  @ApiResponse({
    status: 200,
    description: 'Latest user notification retrieved successfully',
  })
  async getLatestUserNotification(@Request() req: any) {
    const userId = req.user?.id;

    if (!userId) {
      return {
        status: 'success',
        message: 'No notification found',
        data: null,
      };
    }

    const notification =
      await this.notificationService.getLatestUserNotification(userId);
    return {
      status: 'success',
      message: 'Latest notification retrieved successfully',
      data: notification,
    };
  }

  @Get('user')
  @UseGuards(OptionalJwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get user notifications (active, within time range)',
  })
  @ApiResponse({
    status: 200,
    description: 'User notifications retrieved successfully',
  })
  async getUserNotifications(
    @Query() paginationDto: PaginationDto,
    @Request() req: any,
  ) {
    const userId = req.user?.id;

    if (!userId) {
      return {
        status: 'success',
        message: 'User notifications retrieved successfully',
        data: [],
        total: 0,
        page: paginationDto.page || 1,
        limit: paginationDto.limit || 10,
        totalPages: 0,
      };
    }

    const result = await this.notificationService.getUserNotificationsForUser(
      userId,
      paginationDto.page,
      paginationDto.limit,
    );
    return {
      status: 'success',
      message: 'User notifications retrieved successfully',
      ...result,
    };
  }

  @Get('user/:id')
  @UseGuards(OptionalJwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get user notification detail (automatically marks as viewed)',
  })
  @ApiResponse({
    status: 200,
    description: 'User notification detail retrieved successfully',
  })
  async getUserNotificationDetail(
    @Param('id', ParseIntPipe) id: number,
    @Request() req: any,
  ) {
    const userId = req.user?.id;

    if (!userId) {
      return {
        status: 'error',
        message: 'Authentication required to view user notifications',
      };
    }

    const notification =
      await this.notificationService.getUserNotificationDetail(id, userId);
    return {
      status: 'success',
      message: 'User notification detail retrieved successfully',
      data: notification,
    };
  }
}
