import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MinLength } from 'class-validator';

export class CheckPasswordDto {
  @ApiProperty({
    description: 'Current password to verify',
    example: 'mySecurePassword123!',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(6)
  password: string;
}

export class CheckPasswordResponseDto {
  @ApiProperty({
    description: 'Indicates if the password is valid',
    example: true,
  })
  isValid: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Password is valid',
  })
  message: string;
}
