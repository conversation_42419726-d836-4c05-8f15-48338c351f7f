import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { MESSAGE_CONFIG } from '../../../../common/message.config';
import { SupportTicketRepository } from '../../../../database/repositories/support-ticket.repository';
import { SupportResponseRepository } from '../../../../database/repositories/support-response.repository';
import { AdminRepository } from '../../../../database/repositories/admin.repository';
import { TicketStatus } from '../../../../database/entities/support-ticket.entity';
import { ResponseStatus } from '../../../../database/entities/support-response.entity';
import { UpdateResponseDto } from '../dtos/update-response.dto';
import { CommonService } from '../../../../common/common.service';
import configurationGlobal from '../../../../config/configuration.global';
// import { MailService } from '../../../mail/mail.service';
// import { AnalyticsQueueService } from '../../../analytics/services/analytics-queue.service';

@Injectable()
export class SupportAdminService {
  constructor(
    private supportTicketRepository: SupportTicketRepository,
    private supportResponseRepository: SupportResponseRepository,
    private adminRepository: AdminRepository,
    private commonService: CommonService,
    // private mailService: MailService,
    // private analyticsQueueService: AnalyticsQueueService,
  ) {}

  async getAdminTickets(
    page: number = 1,
    limit: number = 20,
    filters?: {
      status?: TicketStatus;
      priority?: string;
      category?: string;
      assignedTo?: number | 'unassigned' | 'me';
      dateType?: 'created' | 'updated' | 'resolved';
      fromDate?: string;
      toDate?: string;
      searchType?: 'subject' | 'description' | 'ticket_number' | 'user_email';
      keyword?: string;
      isRead?: boolean;
    },
  ) {
    const [tickets, total] =
      await this.supportTicketRepository.findAdminTickets(page, limit, filters);

    return {
      data: tickets.map((ticket) => ({
        id: ticket.id,
        ticketNumber: ticket.ticketNumber,
        subject: ticket.subject,
        status: ticket.status,
        priority: ticket.priority,
        category: ticket.category
          ? {
              id: ticket.category.id,
              name: ticket.category.name,
              title: ticket.category.title,
            }
          : null,
        user: {
          id: ticket.user.id,
          email: ticket.user.email,
        },
        assignedAdmin: ticket.assignedAdmin
          ? {
              id: ticket.assignedAdmin.id,
              username: ticket.assignedAdmin.username,
            }
          : null,
        responseCount: ticket.responses?.length || 0,
        lastResponseAt: this.getLastResponseDate(ticket),
        isRead: ticket.isRead || false,
        responseCheckTime: ticket.responseCheckTime,
        createdAt: ticket.createdAt,
        updatedAt: ticket.updatedAt,
        responses: ticket.responses,
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getAdminTicketDetail(ticketId: number) {
    const ticket = await this.supportTicketRepository.findOne({
      where: { id: ticketId },
      relations: [
        'category',
        'user',
        'assignedAdmin',
        'responses',
        'responses.admin',
      ],
      order: {
        responses: { createdAt: 'ASC' },
      },
    });

    if (!ticket) {
      throw new NotFoundException(MESSAGE_CONFIG.TICKET_NOT_FOUND);
    }

    // Mark ticket as read when admin views the detail
    if (!ticket.isRead) {
      await this.supportTicketRepository.update(ticketId, {
        isRead: true,
        responseCheckTime: new Date(),
      });
    }

    return {
      id: ticket.id,
      ticketNumber: ticket.ticketNumber,
      subject: ticket.subject,
      description: ticket.description,
      attachments: ticket.attachments || [],
      status: ticket.status,
      priority: ticket.priority,
      category: ticket.category
        ? {
            id: ticket.category.id,
            name: ticket.category.name,
            title: ticket.category.title,
          }
        : null,
      user: {
        id: ticket.user.id,
        email: ticket.user.email,
      },
      assignedAdmin: ticket.assignedAdmin
        ? {
            id: ticket.assignedAdmin.id,
            username: ticket.assignedAdmin.username,
          }
        : null,
      contextData: ticket.contextData,
      isRead: true, // Always true after viewing
      responseCheckTime: ticket.responseCheckTime || new Date(),
      responses:
        ticket.responses?.map((response) => ({
          id: response.id,
          response: response.response,
          attachments: response.attachments || [],
          status: response.status,
          isInternal: response.isInternal,
          responseCompletedTime: response.responseCompletedTime,
          createdAt: response.createdAt,
          admin: {
            id: response.admin.id,
            username: response.admin.username,
          },
        })) || [],
      createdAt: ticket.createdAt,
      updatedAt: ticket.updatedAt,
    };
  }

  async respondToTicket(ticketId: number, responseDto: any, adminId: number) {
    // 1. Validate ticket exists
    const ticket = await this.supportTicketRepository.findOne({
      where: { id: ticketId },
      relations: ['user', 'category'],
    });

    if (!ticket) {
      throw new NotFoundException(MESSAGE_CONFIG.TICKET_NOT_FOUND);
    }

    // 2. Create response
    const response = await this.supportResponseRepository.save({
      ticketId,
      adminId,
      response: responseDto.response,
      attachments: responseDto.attachments || [],
      status: responseDto.status,
      isInternal: responseDto.isInternal || false,
      responseCompletedTime: new Date(),
    });

    // 3. Update ticket status if requested
    let newTicketStatus = ticket.status;
    if (responseDto.changeStatus) {
      newTicketStatus = responseDto.changeStatus;
      await this.supportTicketRepository.update(ticketId, {
        status: responseDto.changeStatus,
        assignedAdminId: adminId, // Auto-assign when responding
      });
    } else if (ticket.status === TicketStatus.OPEN) {
      // Auto-move to in_progress when first admin response
      newTicketStatus = TicketStatus.IN_PROGRESS;
      await this.supportTicketRepository.update(ticketId, {
        status: TicketStatus.IN_PROGRESS,
        assignedAdminId: adminId,
      });
    }

    // 4. Send notification email to user (if not internal and status is complete)
    let emailSent = false;
    if (
      !responseDto.isInternal &&
      this.shouldSendEmail(ResponseStatus.COMPLETE)
    ) {
      const admin = await this.adminRepository.findOne({
        where: { id: adminId },
      });
      const adminUsername = admin?.username || 'Support Team';

      emailSent = await this.sendUserNotificationEmail(
        ticket,
        responseDto.response,
        adminUsername,
      );
    }

    // 5. Track analytics
    try {
      // await this.analyticsQueueService.trackSupportActivity({
      //   adminId,
      //   ticketId: ticket.id,
      //   activityType: 'admin_response',
      //   category: ticket.category?.title || 'uncategorized',
      //   priority: ticket.priority,
      //   responseTime: this.calculateResponseTime(ticket.createdAt),
      // });
      console.log('Analytics tracked for admin response:', response.id);
    } catch (error) {
      console.error('Analytics tracking failed:', error.message);
    }

    return {
      success: true,
      responseId: response.id,
      ticketStatus: newTicketStatus,
      emailSent,
    };
  }

  async assignTicket(ticketId: number, assignToAdminId: number) {
    // 1. Validate ticket exists
    const ticket = await this.supportTicketRepository.findOne({
      where: { id: ticketId },
    });

    if (!ticket) {
      throw new NotFoundException(MESSAGE_CONFIG.TICKET_NOT_FOUND);
    }

    // 2. Validate target admin exists
    const assignToAdmin = await this.adminRepository.findOne({
      where: { id: assignToAdminId },
    });

    if (!assignToAdmin) {
      throw new NotFoundException(MESSAGE_CONFIG.ADMIN_NOT_FOUND);
    }

    // 3. Update ticket assignment
    await this.supportTicketRepository.update(ticketId, {
      assignedAdminId: assignToAdminId,
      status:
        ticket.status === TicketStatus.OPEN
          ? TicketStatus.ASSIGNED
          : ticket.status,
    });

    // 4. Track analytics
    try {
      // await this.analyticsQueueService.trackSupportActivity({
      //   adminId: assignByAdminId,
      //   ticketId: ticket.id,
      //   activityType: 'ticket_assigned',
      //   assignedToAdminId: assignToAdminId,
      // });
      console.log('Analytics tracked for ticket assignment:', ticketId);
    } catch (error) {
      console.error('Analytics tracking failed:', error.message);
    }

    return {
      success: true,
      assignedTo: {
        id: assignToAdmin.id,
        username: assignToAdmin.username,
      },
    };
  }

  async closeTicket(
    ticketId: number,
    closeDto: {
      resolutionNotes?: string;
      sendNotification?: boolean;
    },
    adminId: number,
  ) {
    // 1. Validate ticket exists and can be closed
    const ticket = await this.supportTicketRepository.findOne({
      where: { id: ticketId },
      relations: ['user', 'category'],
    });

    if (!ticket) {
      throw new NotFoundException(MESSAGE_CONFIG.TICKET_NOT_FOUND);
    }

    if (ticket.status === TicketStatus.CLOSED) {
      throw new BadRequestException(MESSAGE_CONFIG.TICKET_ALREADY_CLOSED);
    }

    const closedAt = new Date();

    // 2. Add closing notes if provided
    if (closeDto.resolutionNotes) {
      await this.supportResponseRepository.save({
        ticketId,
        adminId,
        response: closeDto.resolutionNotes,
        status: ResponseStatus.COMPLETE,
        isInternal: false, // Closing notes are visible to user
        responseCompletedTime: closedAt,
      });
    }

    // 3. Update ticket status
    await this.supportTicketRepository.update(ticketId, {
      status: TicketStatus.CLOSED,
      closedAt,
      assignedAdminId: adminId,
    });

    // 4. Send closure notification
    let emailSent = false;
    if (closeDto.sendNotification !== false) {
      try {
        // await this.mailService.sendSupportTicketClosed({
        //   ticketId: ticket.id,
        //   ticketNumber: ticket.ticketNumber,
        //   subject: ticket.subject,
        //   userEmail: ticket.user.email,
        //   resolutionNotes: closeDto.resolutionNotes,
        // });
        emailSent = true;
        console.log('Ticket closure email sent to user:', ticket.user.email);
      } catch (error) {
        console.error('Failed to send ticket closure email:', error.message);
      }
    }

    // 5. Track analytics
    try {
      // await this.analyticsQueueService.trackSupportActivity({
      //   adminId,
      //   ticketId: ticket.id,
      //   activityType: 'ticket_closed',
      //   category: ticket.category?.title || 'uncategorized',
      //   priority: ticket.priority,
      //   resolutionTime: this.calculateResolutionTime(ticket.createdAt, closedAt),
      // });
      console.log('Analytics tracked for ticket closure:', ticketId);
    } catch (error) {
      console.error('Analytics tracking failed:', error.message);
    }

    return {
      success: true,
      ticketId,
      status: TicketStatus.CLOSED,
      closedAt,
      emailSent,
    };
  }

  async updateResponse(
    responseId: number,
    updateDto: UpdateResponseDto,
    adminId: number,
  ) {
    // 1. Find response and validate ownership
    const response = await this.supportResponseRepository.findAdminResponse(
      responseId,
      adminId,
    );

    if (!response) {
      throw new NotFoundException(MESSAGE_CONFIG.RESPONSE_NOT_FOUND);
    }

    // 2. Check if response can be updated (only drafts and recently sent can be updated)
    if (response.status === ResponseStatus.AUTOMATED_REPLY) {
      throw new BadRequestException(MESSAGE_CONFIG.CANNOT_UPDATE_AUTO_REPLY);
    }

    // Allow updating sent responses within a short time window (e.g., 1 hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    if (
      response.status === ResponseStatus.COMPLETE &&
      response.responseCompletedTime &&
      response.responseCompletedTime < oneHourAgo
    ) {
      throw new BadRequestException(MESSAGE_CONFIG.RESPONSE_TOO_OLD);
    }

    // 3. Update response fields
    response.response = updateDto.response;
    if (updateDto.attachments !== undefined) {
      response.attachments = updateDto.attachments;
    }
    if (updateDto.isInternal !== undefined) {
      response.isInternal = updateDto.isInternal;
    }

    // Keep existing status unless it's pending
    if (updateDto.status) {
      response.status = updateDto.status;
      response.responseCompletedTime = new Date();
    }

    const updatedResponse = await this.supportResponseRepository.save(response);

    // 4. Send email notification if response became public and status is complete
    let emailSent = false;
    if (!response.isInternal && this.shouldSendEmail(updatedResponse.status)) {
      const admin = await this.adminRepository.findOne({
        where: { id: adminId },
      });
      const adminUsername = admin?.username || 'Support Team';

      // Get ticket info for email
      const ticket = await this.supportTicketRepository.findOne({
        where: { id: response.ticketId },
        relations: ['user'],
      });

      if (ticket) {
        emailSent = await this.sendUserNotificationEmail(
          ticket,
          updateDto.response,
          adminUsername,
        );
      }
    }

    // 5. Track analytics
    try {
      // await this.analyticsQueueService.trackSupportActivity({
      //   adminId,
      //   ticketId: response.ticket.id,
      //   responseId: response.id,
      //   activityType: 'response_updated',
      // });
      console.log('Analytics tracked for response update:', responseId);
    } catch (error) {
      console.error('Analytics tracking failed:', error.message);
    }

    return {
      success: true,
      responseId: updatedResponse.id,
      status: updatedResponse.status,
      updatedAt: updatedResponse.updatedAt,
      emailSent,
    };
  }

  async getSupportAnalytics(
    period: 'today' | 'week' | 'month' | 'quarter' | 'year',
  ) {
    // Calculate date range based on period
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(
          now.getFullYear(),
          now.getMonth() - 1,
          now.getDate(),
        );
        break;
      case 'quarter':
        startDate = new Date(
          now.getFullYear(),
          now.getMonth() - 3,
          now.getDate(),
        );
        break;
      case 'year':
        startDate = new Date(
          now.getFullYear() - 1,
          now.getMonth(),
          now.getDate(),
        );
        break;
      default:
        startDate = new Date(
          now.getFullYear(),
          now.getMonth() - 1,
          now.getDate(),
        );
    }

    // 1. Overview Statistics
    const [totalTickets, ticketsByStatus] = await Promise.all([
      this.supportTicketRepository.count(),
      this.supportTicketRepository
        .createQueryBuilder('ticket')
        .select('ticket.status', 'status')
        .addSelect('COUNT(*)', 'count')
        .where('ticket.createdAt >= :startDate', { startDate })
        .andWhere('ticket.deletedAt IS NULL')
        .groupBy('ticket.status')
        .getRawMany(),
    ]);

    const statusCounts = {
      open: 0,
      assigned: 0,
      in_progress: 0,
      pending_user: 0,
      closed: 0,
    };

    ticketsByStatus.forEach((item) => {
      statusCounts[item.status] = parseInt(item.count) || 0;
    });

    // 2. Priority Distribution
    const ticketsByPriority = await this.supportTicketRepository
      .createQueryBuilder('ticket')
      .select('ticket.priority', 'priority')
      .addSelect('COUNT(*)', 'count')
      .where('ticket.createdAt >= :startDate', { startDate })
      .andWhere('ticket.deletedAt IS NULL')
      .groupBy('ticket.priority')
      .getRawMany();

    const priorityCounts = {
      low: 0,
      medium: 0,
      high: 0,
      urgent: 0,
    };

    ticketsByPriority.forEach((item) => {
      priorityCounts[item.priority] = parseInt(item.count) || 0;
    });

    // 3. Category Statistics
    const ticketsByCategory = await this.supportTicketRepository
      .createQueryBuilder('ticket')
      .leftJoin('ticket.category', 'category')
      .select('category.title', 'category')
      .addSelect('COUNT(*)', 'count')
      .where('ticket.createdAt >= :startDate', { startDate })
      .andWhere('ticket.deletedAt IS NULL')
      .groupBy('category.id')
      .orderBy('count', 'DESC')
      .limit(10)
      .getRawMany();

    const categoryStats = ticketsByCategory.map((item) => ({
      category: item.category || 'Uncategorized',
      count: parseInt(item.count) || 0,
      percentage:
        totalTickets > 0
          ? (((parseInt(item.count) || 0) / totalTickets) * 100).toFixed(1)
          : 0,
    }));

    // 4. Response Time Analytics (mock calculation)
    const avgResponseTime = await this.supportResponseRepository
      .createQueryBuilder('response')
      .leftJoin('response.ticket', 'ticket')
      .where('response.createdAt >= :startDate', { startDate })
      .andWhere('response.status != :autoReply', { autoReply: 'auto_reply' })
      .andWhere('ticket.deletedAt IS NULL')
      .getCount();

    // Mock calculations for demo
    const averageResponseTime = avgResponseTime > 0 ? 4.2 : 0;
    const averageResolutionTime = statusCounts.closed > 0 ? 28.5 : 0;

    // 5. Daily Trends (last 7 days)
    const dailyTrends = await this.supportTicketRepository
      .createQueryBuilder('ticket')
      .select('DATE(ticket.createdAt)', 'date')
      .addSelect('COUNT(*)', 'count')
      .where('ticket.createdAt >= :weekStart', {
        weekStart: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      })
      .andWhere('ticket.deletedAt IS NULL')
      .groupBy('DATE(ticket.createdAt)')
      .orderBy('date', 'ASC')
      .getRawMany();

    const dailyTickets = dailyTrends.map((item) => parseInt(item.count) || 0);

    // 6. Admin Performance (top 10)
    const adminPerformance = await this.supportTicketRepository
      .createQueryBuilder('ticket')
      .leftJoin('ticket.assignedAdmin', 'admin')
      .select('admin.id', 'adminId')
      .addSelect('admin.username', 'username')
      .addSelect('COUNT(*)', 'ticketsHandled')
      .addSelect(
        'COUNT(CASE WHEN ticket.status = :closed THEN 1 END)',
        'closedTickets',
      )
      .where('ticket.createdAt >= :startDate', { startDate })
      .andWhere('ticket.deletedAt IS NULL')
      .andWhere('admin.id IS NOT NULL')
      .setParameter('closed', 'closed')
      .groupBy('admin.id')
      .orderBy('ticketsHandled', 'DESC')
      .limit(10)
      .getRawMany();

    const performanceStats = adminPerformance.map((item) => ({
      adminId: parseInt(item.adminId) || 0,
      username: item.username || 'Unknown',
      ticketsHandled: parseInt(item.ticketsHandled) || 0,
      averageResponseTime: Math.random() * 6 + 2, // Mock data
      closureRate:
        parseInt(item.ticketsHandled) > 0
          ? (
              ((parseInt(item.closedTickets) || 0) /
                parseInt(item.ticketsHandled)) *
              100
            ).toFixed(1)
          : 0,
    }));

    return {
      overview: {
        totalTickets,
        openTickets:
          statusCounts.open +
          statusCounts.assigned +
          statusCounts.in_progress +
          statusCounts.pending_user,
        closedTickets: statusCounts.closed,
        averageResponseTime,
        averageResolutionTime,
        satisfactionRate: 94.2, // Mock data - would come from user feedback
      },
      ticketsByStatus: statusCounts,
      ticketsByPriority: priorityCounts,
      ticketsByCategory: categoryStats,
      trends: {
        dailyTickets,
        responseTimesByDay: dailyTickets.map(() => Math.random() * 3 + 2), // Mock data
      },
      adminPerformance: performanceStats,
      period,
      dateRange: {
        startDate: startDate.toISOString(),
        endDate: now.toISOString(),
      },
    };
  }

  // Helper methods
  private async sendUserNotificationEmail(
    ticket: any,
    responseContent: string,
    adminUsername: string,
  ): Promise<boolean> {
    try {
      await this.commonService.sendEmail(
        ticket.user.email,
        'Support Response Received',
        'qa-answer', // Email template for responses
        {
          url: `${configurationGlobal().url.user}/support/tickets/${ticket.id}`,
          urlLogo: 'this.urlLogo',
          title: ticket.subject,
          ticketNumber: ticket.ticketNumber,
          responseContent: responseContent,
          adminUsername: adminUsername,
        },
      );
      console.log(
        'User email notification sent for ticket:',
        ticket.ticketNumber,
      );
      return true;
    } catch (error) {
      console.error('Failed to send user notification email:', error.message);
      return false;
    }
  }

  private shouldSendEmail(status: ResponseStatus): boolean {
    return [
      ResponseStatus.COMPLETE,
      ResponseStatus.COMPLETE_ADD,
      ResponseStatus.AUTOMATED_REPLY,
    ].includes(status);
  }

  private getLastResponseDate(ticket: any): Date | null {
    if (!ticket.responses || ticket.responses.length === 0) return null;
    return ticket.responses[ticket.responses.length - 1].createdAt;
  }

  private calculateResponseTime(ticketCreatedAt: Date): number {
    return Date.now() - new Date(ticketCreatedAt).getTime();
  }

  private calculateResolutionTime(
    ticketCreatedAt: Date,
    closedAt: Date,
  ): number {
    return new Date(closedAt).getTime() - new Date(ticketCreatedAt).getTime();
  }

  async createMultipleResponses(
    ticketId: number,
    responses: Array<{
      response: string;
      attachments?: string[];
      isInternal?: boolean;
      status?: string;
    }>,
    adminId: number,
  ) {
    // 1. Validate ticket exists
    const ticket = await this.supportTicketRepository.findOne({
      where: { id: ticketId },
      relations: ['user', 'category'],
    });

    if (!ticket) {
      throw new NotFoundException(MESSAGE_CONFIG.TICKET_NOT_FOUND);
    }

    const results: Array<{
      responseId: number | null;
      success: boolean;
      error?: string;
    }> = [];
    let emailSent = false;

    // 2. Create multiple responses
    for (const responseData of responses) {
      try {
        const response = await this.supportResponseRepository.save({
          ticketId,
          adminId,
          response: responseData.response,
          attachments: responseData.attachments || [],
          status:
            (responseData?.status as ResponseStatus) ||
            ResponseStatus.UNANSWERED,
          isInternal: responseData.isInternal || false,
          responseCompletedTime: new Date(),
        });

        results.push({
          responseId: response.id,
          success: true,
        });

        // Send email for first public response with complete status only
        if (
          !emailSent &&
          !responseData.isInternal &&
          this.shouldSendEmail(responseData.status as ResponseStatus)
        ) {
          const admin = await this.adminRepository.findOne({
            where: { id: adminId },
          });
          const adminUsername = admin?.username || 'Support Team';

          emailSent = await this.sendUserNotificationEmail(
            ticket,
            responseData.response,
            adminUsername,
          );
        }
      } catch (error) {
        console.log('Failed to create response:', error);
        console.error('Failed to create response:', error.message);
        results.push({
          responseId: null,
          success: false,
          error: error.message,
        });
      }
    }

    // 3. Update ticket status if not already updated
    if (ticket.status === TicketStatus.OPEN) {
      await this.supportTicketRepository.update(ticketId, {
        status: TicketStatus.IN_PROGRESS,
        assignedAdminId: adminId,
      });
    } else if (!ticket.assignedAdminId) {
      await this.supportTicketRepository.update(ticketId, {
        assignedAdminId: adminId,
      });
    }

    // 4. Track analytics for multiple responses
    try {
      // await this.analyticsQueueService.trackSupportActivity({
      //   adminId,
      //   ticketId: ticket.id,
      //   activityType: 'multiple_responses',
      //   category: ticket.category?.title || 'uncategorized',
      //   priority: ticket.priority,
      //   responseCount: results.filter(r => r.success).length,
      // });
      console.log('Analytics tracked for multiple responses:', results.length);
    } catch (error) {
      console.error('Analytics tracking failed:', error.message);
    }

    return {
      success: results.some((r) => r.success),
      responses: results,
      emailSent,
    };
  }

  // Additional methods for Q&A compatibility
  async getTicketsByUser(userId: number, page: number = 1, limit: number = 20) {
    const [tickets, total] = await this.supportTicketRepository.findAndCount({
      where: { userId },
      relations: ['category', 'responses', 'responses.admin'],
      skip: (page - 1) * limit,
      take: limit,
      order: { createdAt: 'DESC' },
    });

    return {
      data: tickets,
      total,
      page,
      limit,
    };
  }

  async bulkDeleteTickets(ticketIds: number[]) {
    // Soft delete multiple tickets
    await this.supportTicketRepository.softDelete(ticketIds);

    return {
      success: true,
      deletedCount: ticketIds.length,
    };
  }
}
