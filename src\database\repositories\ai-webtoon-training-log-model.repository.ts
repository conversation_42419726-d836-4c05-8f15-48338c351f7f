import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { AiWebtoonTrainingLogModelEntity } from '../entities/ai-webtoon-training-log-model.entity';

@Injectable()
export class AiWebtoonTrainingLogModelRepository extends Repository<AiWebtoonTrainingLogModelEntity> {
  constructor(private dataSource: DataSource) {
    super(AiWebtoonTrainingLogModelEntity, dataSource.createEntityManager());
  }
}
