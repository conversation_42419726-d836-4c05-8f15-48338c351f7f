import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsUrl,
  ValidateNested,
} from 'class-validator';
import { EAiFrameImageSizeType } from 'src/common/ai-webtoon.enum';

export class AddImageCutNormalDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUrl()
  image: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  imageWidth: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  imageHeight: number;
}

export class AddSketchImagesDto {
  @ApiProperty({
    example: [
      {
        image: 'https://example.com/image.jpg',
        imageWidth: 100,
        imageHeight: 100,
      },
    ],
  })
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AddImageCutNormalDto)
  images: AddImageCutNormalDto[];

  @ApiProperty({
    enum: EAiFrameImageSizeType,
    example: EAiFrameImageSizeType.CONTROLNET_CANNY,
  })
  @IsNotEmpty()
  @IsEnum(EAiFrameImageSizeType)
  @IsIn([
    EAiFrameImageSizeType.CONTROLNET_CANNY,
    EAiFrameImageSizeType.CONTROLNET_DEPTH,
  ])
  imageSizeType: EAiFrameImageSizeType;
}
