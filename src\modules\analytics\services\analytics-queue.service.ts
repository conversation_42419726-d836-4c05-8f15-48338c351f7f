import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { ANALYTICS_QUEUES } from '../constants/analytics-queues.constant';
import {
  ContentViewEventDto,
  UserActivityEventDto,
  PaymentEventDto,
  PaymentAttemptEventDto,
} from '../dtos/analytics-events.dto';

@Injectable()
export class AnalyticsQueueService {
  constructor(
    @InjectQueue(ANALYTICS_QUEUES.CONTENT_ANALYTICS)
    private contentAnalyticsQueue: Queue,
    @InjectQueue(ANALYTICS_QUEUES.USER_ANALYTICS)
    private userAnalyticsQueue: Queue,
    @InjectQueue(ANALYTICS_QUEUES.PAYMENT_ANALYTICS)
    private paymentAnalyticsQueue: Queue,
  ) {}

  async trackContentView(data: ContentViewEventDto): Promise<void> {
    await this.contentAnalyticsQueue.add('content-view', data, {
      priority: 1,
      delay: 0,
    });
  }

  async trackUserActivity(data: UserActivityEventDto): Promise<void> {
    await this.userAnalyticsQueue.add('user-activity', data, {
      priority: 2,
      delay: 0,
    });
  }

  async trackPayment(data: PaymentEventDto): Promise<void> {
    await this.paymentAnalyticsQueue.add('payment-event', data, {
      priority: 3,
      delay: 0,
    });
  }

  async trackPaymentAttempt(data: PaymentAttemptEventDto): Promise<void> {
    await this.paymentAnalyticsQueue.add('payment-attempt', data, {
      priority: 3,
      delay: 0,
    });
  }

  // Batch operations cho high-volume events
  async batchTrackContentViews(events: ContentViewEventDto[]): Promise<void> {
    const jobs = events.map((event, index) => ({
      name: 'content-view',
      data: event,
      opts: { priority: 1, delay: index * 10 }, // Slight delay để tránh overwhelm
    }));

    await this.contentAnalyticsQueue.addBulk(jobs);
  }

  async batchTrackUserActivities(
    events: UserActivityEventDto[],
  ): Promise<void> {
    const jobs = events.map((event, index) => ({
      name: 'user-activity',
      data: event,
      opts: { priority: 2, delay: index * 10 },
    }));

    await this.userAnalyticsQueue.addBulk(jobs);
  }

  async batchTrackPayments(events: PaymentEventDto[]): Promise<void> {
    const jobs = events.map((event, index) => ({
      name: 'payment-event',
      data: event,
      opts: { priority: 3, delay: index * 10 },
    }));

    await this.paymentAnalyticsQueue.addBulk(jobs);
  }
}
