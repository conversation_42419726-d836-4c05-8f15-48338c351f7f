import { Injectable, NotFoundException } from '@nestjs/common';
import { MESSAGE_CONFIG } from '../../../common/message.config';
import { FaqRepository } from '../../../database/repositories/faq.repository';
import { AnalyticsQueueService } from '../../analytics/services/analytics-queue.service';
import { FaqListQueryDto } from '../dtos/faq-list-query.dto';

@Injectable()
export class FaqService {
  constructor(
    private readonly faqRepository: FaqRepository,
    private readonly analyticsQueueService: AnalyticsQueueService,
  ) {}

  async getFaqList(query: FaqListQueryDto) {
    const { page = 1, limit = 20, categoryId } = query;

    const [faqs, total] = await this.faqRepository.findUserFaqList(
      page,
      limit,
      categoryId,
    );

    return {
      data: faqs.map((faq) => ({
        id: faq.id,
        title: faq.title,
        content: faq.content,
        category: faq.category
          ? {
              id: faq.category.id,
              name: faq.category.name,
              title: faq.category.title,
            }
          : null,
        viewCount: faq.viewCount,
        createdAt: faq.createdAt,
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getFaqDetail(id: number, userId?: number) {
    const faq = await this.faqRepository.findUserFaqDetail(id);

    if (!faq) {
      throw new NotFoundException(MESSAGE_CONFIG.FAQ_NOT_FOUND);
    }

    // Increment view count
    await this.faqRepository.incrementViewCount(id);

    // Track analytics if user is logged in
    if (userId) {
      try {
        await this.analyticsQueueService.trackUserActivity({
          userId,
          activityType: 'faq_view',
          platform: 'web', // Could be determined from request headers
          timestamp: Date.now(),
          metadata: {
            faqId: id,
            categoryId: faq.categoryId,
          },
        });
      } catch (error) {
        // Log error but don't fail the request
        console.error('Failed to track FAQ analytics:', error.message);
      }
    }

    return {
      id: faq.id,
      title: faq.title,
      content: faq.content,
      category: faq.category
        ? {
            id: faq.category.id,
            name: faq.category.name,
            title: faq.category.title,
          }
        : null,
      viewCount: faq.viewCount + 1, // Return incremented count
      createdAt: faq.createdAt,
    };
  }
}
