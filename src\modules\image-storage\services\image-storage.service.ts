import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { v4 as uuidv4 } from 'uuid';
import { SignatureService } from './signature.service';
import { StatusService } from './status.service';
import { UploadImageDto } from '../dto/upload-image.dto';
import {
  UploadImageResponseDto,
  UploadStatus,
} from '../dto/upload-response.dto';
import {
  ImageMetadata,
  ProcessImageJob,
} from '../interfaces/image-storage.interface';

@Injectable()
export class ImageStorageService {
  private readonly logger = new Logger(ImageStorageService.name);

  constructor(
    @InjectQueue('image-storage') private readonly imageQueue: Queue,
    private readonly signatureService: SignatureService,
    private readonly statusService: StatusService,
  ) {}

  async processUpload(
    file: any,
    uploadData: UploadImageDto,
  ): Promise<UploadImageResponseDto> {
    const uploadId = uuidv4();

    try {
      this.logger.log(`Processing upload ${uploadId}`);

      // Step 1: Verify signature
      const isValidSignature = this.signatureService.verifySignature(
        file.buffer,
        uploadData.signature,
        uploadData.publicKey,
      );

      if (!isValidSignature) {
        throw new HttpException(
          {
            success: false,
            error: {
              code: 'INVALID_SIGNATURE',
              message: 'Signature verification failed',
              details: {
                uploadId,
                signatureLength: uploadData.signature.length,
                publicKeyValid: this.isValidPublicKeyFormat(
                  uploadData.publicKey,
                ),
              },
            },
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      this.logger.log(`Signature verification passed for ${uploadId}`);

      // Step 2: Create metadata
      const metadata: ImageMetadata = {
        filename: uploadData.filename,
        tags: uploadData.tags,
        description: uploadData.description,
        albumId: uploadData.albumId,
        targetPath: uploadData.targetPath,
        originalFilename: file.originalname,
        size: file.size,
        mimetype: file.mimetype,
        signature: uploadData.signature,
        publicKey: uploadData.publicKey,
        type: uploadData.type,
      };

      // Step 3: Save initial status
      await this.statusService.createUploadStatus(uploadId, metadata);

      // Step 4: Queue processing job
      console.log('=== BEFORE QUEUE DEBUG ===');
      console.log('File buffer length:', file.buffer.length);
      console.log('File buffer is Buffer?', Buffer.isBuffer(file.buffer));
      console.log('First 20 bytes:', file.buffer.slice(0, 20).toString('hex'));

      const job: ProcessImageJob = {
        uploadId,
        imageBuffer: file.buffer,
        metadata,
      };

      console.log('Job data size:', JSON.stringify(job).length);
      console.log('======================');

      await this.imageQueue.add('processImage', job, {
        priority: this.getJobPriority(uploadData.type),
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      });

      this.logger.log(`Upload ${uploadId} queued successfully`);

      // Step 5: Return early ACK
      return {
        success: true,
        uploadId,
        status: UploadStatus.PROCESSING,
        estimatedTime: this.getEstimatedProcessingTime(file.size),
        webhookUrl: uploadData.webhookUrl,
      };
    } catch (error) {
      this.logger.error(`Upload processing failed for ${uploadId}:`, error);

      // Save failed status
      await this.statusService.updateUploadStatus(
        uploadId,
        UploadStatus.FAILED,
        0,
        error.message || 'Unknown error during upload processing',
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        {
          success: false,
          error: {
            code: 'PROCESSING_ERROR',
            message: 'Failed to process upload',
            details: { uploadId, error: error.message },
          },
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get job priority based on upload type
   */
  private getJobPriority(type: string): number {
    const priorities = {
      avatar: 1, // Highest priority
      thumbnail: 2,
      content: 3,
      episode: 4,
      general: 5, // Lowest priority
    };

    return priorities[type] || 5;
  }

  /**
   * Calculate estimated processing time based on file size
   */
  private getEstimatedProcessingTime(fileSize: number): number {
    // Base time: 10 seconds
    // Add 1 second per MB
    const baseSizeInMB = fileSize / (1024 * 1024);
    return Math.max(10, Math.round(10 + baseSizeInMB));
  }

  /**
   * Basic public key format validation
   */
  private isValidPublicKeyFormat(publicKey: string): boolean {
    const pemRegex =
      /^-----BEGIN PUBLIC KEY-----[\s\S]*-----END PUBLIC KEY-----$/;
    return pemRegex.test(publicKey.trim());
  }
}
