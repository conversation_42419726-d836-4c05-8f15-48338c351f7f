import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';

export class LoginDto {
  @ApiProperty({ example: '<EMAIL>', description: 'Email' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ example: 'password', description: 'Password' })
  @IsNotEmpty()
  password: string;

  @ApiProperty({
    example: '1',
    description: 'languageId',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  languageId: number;
}
