import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsString,
  Min,
} from 'class-validator';
import { EAiGenPromptByDescriptionType } from 'src/common/ai-webtoon.enum';

export class GeneratePromptDto {
  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty({ enum: EAiGenPromptByDescriptionType })
  @IsEnum(EAiGenPromptByDescriptionType)
  typePrompt: EAiGenPromptByDescriptionType;
}

export class ReceiveGeneratePromptByDescriptionDto {
  @ApiProperty()
  @IsBoolean()
  isImportFile: boolean;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  @Min(1)
  cutId: number;

  @ApiProperty({ enum: EAiGenPromptByDescriptionType })
  @IsEnum(EAiGenPromptByDescriptionType)
  typePrompt: EAiGenPromptByDescriptionType;

  @ApiProperty()
  @IsString()
  prompt: string;
}
