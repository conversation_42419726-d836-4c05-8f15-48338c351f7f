import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { AiLetteringTypeService } from './ai-lettering-type.service';
import { CreateAiLetteringTypeDto } from './dto/create-ai-lettering-type.dto';
import { SortAiLetteringTypeDto } from './dto/sort-ai-lettering-type.dto';
import { JwtAdminAuthGuard } from 'src/modules/admin/auth-admin/guards/jwt-admin-auth.guard';

@ApiTags('ai-lettering-type')
@Controller('ai-lettering-type')
@ApiBearerAuth()
@UseGuards(JwtAdminAuthGuard)
export class AiLetteringTypeController {
  constructor(
    private readonly aiLetteringTypeService: AiLetteringTypeService,
  ) {}

  @Get()
  list() {
    return this.aiLetteringTypeService.list();
  }

  @Post()
  create(@Body() body: CreateAiLetteringTypeDto) {
    return this.aiLetteringTypeService.create(body);
  }

  @Put()
  sort(@Body() body: SortAiLetteringTypeDto) {
    return this.aiLetteringTypeService.sort(body);
  }

  @Delete('/:id')
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.aiLetteringTypeService.delete(id);
  }
}
