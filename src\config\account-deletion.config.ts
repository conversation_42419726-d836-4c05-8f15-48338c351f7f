/**
 * Account Deletion Configuration
 *
 * Control account deletion features through these constants.
 * Can be overridden by environment variables for different deployment environments.
 */

export const ACCOUNT_DELETION_CONFIG = {
  // Security features (currently disabled, can be enabled when needed)
  CAPTCHA_ENABLED:
    process.env.ACCOUNT_DELETION_CAPTCHA_ENABLED === 'true' || false,
  CONFIRM_TEXT_ENABLED:
    process.env.ACCOUNT_DELETION_CONFIRM_TEXT_ENABLED === 'true' || false,

  // Grace period (0 = immediate deletion, >0 = scheduled deletion with recovery option)
  GRACE_PERIOD_DAYS: parseInt(
    process.env.ACCOUNT_DELETION_GRACE_PERIOD_DAYS || '0',
  ),

  // Rate limiting to prevent abuse
  RATE_LIMIT_ATTEMPTS: parseInt(
    process.env.ACCOUNT_DELETION_RATE_LIMIT_ATTEMPTS || '3',
  ),
  RATE_LIMIT_WINDOW_MINUTES: parseInt(
    process.env.ACCOUNT_DELETION_RATE_LIMIT_WINDOW_MINUTES || '60',
  ),

  // Required confirm text when CONFIRM_TEXT_ENABLED is true
  REQUIRED_CONFIRM_TEXT: 'DELETE',
} as const;

/**
 * Account deletion requirements for frontend
 */
export interface AccountDeletionRequirements {
  captchaRequired: boolean;
  confirmTextRequired: boolean;
  gracePeriodDays: number;
  rateLimit: {
    maxAttempts: number;
    windowMinutes: number;
  };
}

/**
 * Get current account deletion requirements
 */
export function getAccountDeletionRequirements(): AccountDeletionRequirements {
  return {
    captchaRequired: ACCOUNT_DELETION_CONFIG.CAPTCHA_ENABLED,
    confirmTextRequired: ACCOUNT_DELETION_CONFIG.CONFIRM_TEXT_ENABLED,
    gracePeriodDays: ACCOUNT_DELETION_CONFIG.GRACE_PERIOD_DAYS,
    rateLimit: {
      maxAttempts: ACCOUNT_DELETION_CONFIG.RATE_LIMIT_ATTEMPTS,
      windowMinutes: ACCOUNT_DELETION_CONFIG.RATE_LIMIT_WINDOW_MINUTES,
    },
  };
}
