import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeSubPlan1752132466222 implements MigrationInterface {
  name = 'ChangeSubPlan1752132466222';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`subscription_plans\` DROP COLUMN \`duration_day\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`subscription_plans\` ADD \`discountPrice\` decimal(10,2) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`subscription_plans\` ADD \`isPopular\` tinyint NOT NULL DEFAULT 0`,
    );
    await queryRunner.query(
      `ALTER TABLE \`subscription_plans\` ADD \`durationDays\` int NOT NULL DEFAULT '30'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`subscription_plans\` DROP COLUMN \`durationDays\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`subscription_plans\` DROP COLUMN \`isPopular\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`subscription_plans\` DROP COLUMN \`discountPrice\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`subscription_plans\` ADD \`duration_day\` int NOT NULL`,
    );
  }
}
