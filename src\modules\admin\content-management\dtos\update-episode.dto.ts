import {
  <PERSON><PERSON><PERSON><PERSON>,
  IsBoolean,
  <PERSON><PERSON><PERSON>,
  Is<PERSON><PERSON>ber,
  IsOptional,
  IsString,
} from 'class-validator';
import {
  EpisodeBackgroundColors,
  PaymentType,
  PaymentUnitType,
} from '../../../../common/setting.enum';
import { ApiProperty } from '@nestjs/swagger';
import { CommonStatus } from '../../../../common/status.enum';

export class UpdateEpisodeDto {
  @ApiProperty({
    description: 'Episode number',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  episodeNumber: number;

  @ApiProperty({
    description: 'Episode string',
    example: 'Episode 1',
  })
  @IsOptional()
  @IsString()
  episodeString: string;

  @ApiProperty({
    description: 'Status',
    example: CommonStatus.ACTIVE,
    enum: CommonStatus,
  })
  @IsOptional()
  @IsEnum(CommonStatus)
  status: CommonStatus;

  @ApiProperty({
    description: 'Content ID',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  contentId: number;

  @ApiProperty({
    description: 'Title',
    example: 'Episode 1',
  })
  @IsOptional()
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Expected release date',
    example: '2025-01-01',
  })
  @IsOptional()
  @IsString()
  expectedReleaseDate: string;

  @ApiProperty({
    description: 'Background color',
    example: EpisodeBackgroundColors.WHITE,
  })
  @IsOptional()
  @IsString()
  bgColor: EpisodeBackgroundColors;

  @ApiProperty({
    description: 'Thumbnail',
    example: '/images/episode/thumbnail.jpg',
  })
  @IsOptional()
  @IsString()
  thumbnail: string;

  @ApiProperty({
    description: 'None user viewing',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  noneUserViewing: boolean;

  @ApiProperty({
    description: 'Images',
    example: [{ path: '/images/episode/image.jpg' }],
  })
  @IsOptional()
  @IsArray()
  images: {
    path: string;
  }[];

  @ApiProperty({
    description: 'Payment type',
    example: PaymentType.FREE,
  })
  @IsOptional()
  @IsString()
  paymentType: PaymentType;

  @ApiProperty({
    description: 'Payment unit',
    example: PaymentUnitType.KRW,
  })
  @IsOptional()
  @IsString()
  paymentUnit: PaymentUnitType;

  @ApiProperty({
    description: 'Payment amount',
    example: 1000,
  })
  @IsOptional()
  @IsNumber()
  paymentAmount: number;
}
