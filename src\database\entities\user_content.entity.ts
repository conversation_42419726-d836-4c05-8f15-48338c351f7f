import { Column, Entity, ManyToOne } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { UserEntity } from './user.entity';
import { ContentEntity } from './content.entity';
import { UserContentType } from '../../common/common.config';
import { UserContentStatus } from '../../common/status.enum';
import { EpisodeEntity } from './episode.entity';

@Entity('user_content')
export class UserContentEntity extends DefaultEntity {
  @Column({
    type: 'enum',
    enum: UserContentType,
    default: UserContentType.RECENTLY_READ,
  })
  type: UserContentType;

  @Column({
    type: 'enum',
    enum: UserContentStatus,
    default: UserContentStatus.ACTIVE,
  })
  status: UserContentStatus;

  @Column({ type: 'bigint', nullable: true })
  lastUpdateTime: number;

  @ManyToOne(() => EpisodeEntity, (episode) => episode.userContents, {
    nullable: true,
  })
  episode: EpisodeEntity;

  @ManyToOne(() => UserEntity, (user) => user.userContents)
  user: UserEntity;

  @ManyToOne(() => ContentEntity, (content) => content.userContents)
  content: ContentEntity;
}
