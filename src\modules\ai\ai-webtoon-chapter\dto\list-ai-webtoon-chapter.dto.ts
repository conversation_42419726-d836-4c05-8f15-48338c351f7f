import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsInt, IsOptional, IsString, Min } from 'class-validator';
import { DirectionSort } from 'src/common/common.enum';

export enum ListAiWebtoonChapterSearchBy {
  TITLE = 'title',
  ADMIN_NAME = 'admin_name',
}

export enum ListAiWebtoonChapterSearchByTime {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

export class ListAiWebtoonChapterDto {
  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  aiWebtoonStoryId: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  search: string;

  @ApiProperty({ example: '2023-03-27', required: false })
  @IsString()
  @IsOptional()
  fromTime: string;

  @ApiProperty({ example: '2023-03-27', required: false })
  @IsString()
  @IsOptional()
  toTime: string;

  @ApiProperty({ enum: ListAiWebtoonChapterSearchBy, required: false })
  @IsOptional()
  @IsEnum(ListAiWebtoonChapterSearchBy)
  searchBy: ListAiWebtoonChapterSearchBy;

  @ApiProperty({ enum: ListAiWebtoonChapterSearchByTime, required: false })
  @IsOptional()
  @IsEnum(ListAiWebtoonChapterSearchByTime)
  searchByTime: ListAiWebtoonChapterSearchByTime;

  @ApiProperty({ enum: ListAiWebtoonChapterSearchByTime, required: false })
  @IsOptional()
  @IsEnum(ListAiWebtoonChapterSearchByTime)
  sortBy: ListAiWebtoonChapterSearchByTime;

  @ApiProperty({ enum: DirectionSort, required: false })
  @IsOptional()
  @IsEnum(DirectionSort)
  direction: DirectionSort;
}
