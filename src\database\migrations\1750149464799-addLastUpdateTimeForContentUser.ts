import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLastUpdateTimeForContentUser1750149464799
  implements MigrationInterface
{
  name = 'AddLastUpdateTimeForContentUser1750149464799';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`user_content\` ADD \`lastUpdateTime\` bigint NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`user_content\` DROP COLUMN \`lastUpdateTime\``,
    );
  }
}
