import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { AiWebtoonSceneChapterEntity } from '../entities/ai-webtoon-scene-chapter.entity';

@Injectable()
export class AiWebtoonSceneChapterRepository extends Repository<AiWebtoonSceneChapterEntity> {
  constructor(private dataSource: DataSource) {
    super(AiWebtoonSceneChapterEntity, dataSource.createEntityManager());
  }
}
