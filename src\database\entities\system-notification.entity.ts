import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

export enum NotificationStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

@Entity('system_notification')
export class SystemNotificationEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ default: 'root' })
  name: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({
    type: 'enum',
    enum: NotificationStatus,
    default: NotificationStatus.ACTIVE,
  })
  status: NotificationStatus;

  @Column({ type: 'text' })
  content: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}
