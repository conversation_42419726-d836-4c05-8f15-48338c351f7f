import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeUserTable1751858324668 implements MigrationInterface {
  name = 'ChangeUserTable1751858324668';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`gender\``);
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`gender\` enum ('male', 'female', 'other') NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`gender\``);
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`gender\` varchar(255) NULL`,
    );
  }
}
