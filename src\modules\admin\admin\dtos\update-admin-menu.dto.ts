import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
export class AdminMenuDto {
  @ApiProperty({ description: 'Menu ID' })
  @IsNumber()
  menuId: number;

  @ApiProperty({ description: 'Status' })
  @IsString()
  status: string;
}
export class UpdateAdminMenuDto {
  @ApiProperty({
    description: 'Menus',
    type: [AdminMenuDto],
    isArray: true,
    example: [
      {
        menuId: 1,
        status: 'active',
      },
    ],
  })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => AdminMenuDto)
  menus: AdminMenuDto[];
}
