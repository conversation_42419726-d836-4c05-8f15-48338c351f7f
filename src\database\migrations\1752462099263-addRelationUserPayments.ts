import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRelationUserPayments1752462099263
  implements MigrationInterface
{
  name = 'AddRelationUserPayments1752462099263';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`payments\` ADD \`userId\` int NULL`);
    await queryRunner.query(
      `ALTER TABLE \`payments\` ADD CONSTRAINT \`FK_d35cb3c13a18e1ea1705b2817b1\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`payments\` DROP FOREIGN KEY \`FK_d35cb3c13a18e1ea1705b2817b1\``,
    );
    await queryRunner.query(`ALTER TABLE \`payments\` DROP COLUMN \`userId\``);
  }
}
