import { IsString, <PERSON><PERSON>otEmpty, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateMenuDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'The key of the menu', example: 'menu' })
  key: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'The label of the menu', example: 'Menu' })
  label: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The icon of the menu',
    example: 'fa-solid fa-home',
  })
  icon: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The route of the menu',
    example: '/menu',
  })
  route: string;

  @IsNumber()
  @IsOptional()
  @ApiProperty({
    description: 'The order of the menu',
    example: 1,
    required: false,
  })
  order: number;

  @IsNumber()
  @IsOptional()
  @ApiProperty({
    description: 'The parent id of the menu',
    example: 1,
    required: false,
  })
  parentId: number;
}
