import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';

import {
  EAiDirection<PERSON>pi,
  EAiWebtoonBasicStatus,
  EAiWebtoonCharacterStatus,
  EAiWebtoonCharacterType,
  EAiWebtoonTrainingCharacterSessionStatus,
  ISyntheticDataImagesItem,
} from 'src/common/ai-webtoon.enum';
import { IAiWebtoonSyntheticDataSend } from 'src/common/interfaces/ai-webtoon/ai-send-request.interface';
import { AiWebtoonCharacterRepository } from 'src/database/repositories/ai-webtoon-character.repository';
import { AiWebtoonTrainingCharacterSessionRepository } from 'src/database/repositories/ai-webtoon-training-character-session.repository';
import { NotificationGoogleService } from 'src/modules/notification-google/notification-google.service';
import { PaginationDto } from 'src/modules/notification/dtos/pagination.dto';
import { v4 as uuidv4 } from 'uuid';
import { AiWebtoonSyntheticDataRepository } from '../../../database/repositories/ai-webtoon-synthetic-data.repository';
import { AiApiGenStateManagementService } from '../ai-api-gen-state-management/ai-api-gen-state-management.service';
import { AiService } from '../ai-service/ai-service.service';
import {
  AddAiWebtoonSyntheticDataImagesDto,
  AiWebtoonSyntheticDataType,
} from './dto/add-images.dto';
import { CreateAiWebtoonSyntheticDataDto } from './dto/create.dto';
import { DeleteAiWebtoonSyntheticDataImageDto } from './dto/delete-image.dto';
import { AiWebtoonSyntheticGenerateImagesDto } from './dto/generate-images.dto';
import { AiWebtoonSyntheticReceiveGenerateImageDto } from './dto/receive-generate-image.dto';
import { AI_MESSAGE_CONFIG } from 'src/common/message.config';
@Injectable()
export class AiWebtoonSyntheticDataService {
  constructor(
    private readonly aiWebtoonSyntheticDataRepository: AiWebtoonSyntheticDataRepository,
    private readonly aiWebtoonCharacterRepository: AiWebtoonCharacterRepository,
    private readonly aiWebtoonTrainingCharacterSessionRepository: AiWebtoonTrainingCharacterSessionRepository,
    private readonly aiApiGenStateManagementService: AiApiGenStateManagementService,
    private readonly aiService: AiService,

    // private readonly httpService: HttpService,
    private readonly notificationGoogleService: NotificationGoogleService,
    // private readonly socketService: SocketService,
  ) {}

  private readonly logger = new Logger(AiWebtoonSyntheticDataService.name);

  async create(body: CreateAiWebtoonSyntheticDataDto) {
    const result = await this.aiWebtoonSyntheticDataRepository.save({
      storyName: body.name,
    });
    return { id: result?.id };
  }

  async addImages(id: number, body: AddAiWebtoonSyntheticDataImagesDto) {
    const item = await this.aiWebtoonSyntheticDataRepository.findOne({
      where: {
        id,
      },
    });
    if (!item) {
      throw new NotFoundException(
        AI_MESSAGE_CONFIG.AI_SYNTHETIC_DATA_NOT_FOUND,
      );
    }
    if (body.type === AiWebtoonSyntheticDataType.FluxContextImages) {
      item.fluxContextImages.push(
        ...body.images.map(
          (image) =>
            ({
              uuid: uuidv4(),
              image,
              prompt: '',
              resultImages: [],
            }) as ISyntheticDataImagesItem,
        ),
      );
    }
    if (body.type === AiWebtoonSyntheticDataType.BLoraImages) {
      item.bLoraImages.push(
        ...body.images.map(
          (image) =>
            ({
              uuid: uuidv4(),
              image,
              resultImages: [],
            }) as ISyntheticDataImagesItem,
        ),
      );
    }
    await this.aiWebtoonSyntheticDataRepository.save(item);
    return true;
  }

  async list(dto: PaginationDto) {
    const [items, total] = await this.aiWebtoonSyntheticDataRepository
      .createQueryBuilder('aiWtSyntheticData')
      .orderBy('aiWtSyntheticData.id', 'DESC')
      .skip((dto.page - 1) * dto.limit)
      .take(dto.limit)
      .getManyAndCount();

    return {
      data: items,
      page: dto.page,
      limit: dto.limit,
      totalPage: Math.ceil(total / dto.limit),
      total,
    };
  }

  async detail(id: number) {
    const item = await this.aiWebtoonSyntheticDataRepository.findOne({
      where: {
        id,
      },
    });

    if (!item) {
      throw new NotFoundException(
        AI_MESSAGE_CONFIG.AI_SYNTHETIC_DATA_NOT_FOUND,
      );
    }

    return item;
  }

  async generateImages(id: number, body: AiWebtoonSyntheticGenerateImagesDto) {
    const item = await this.detail(id);

    const column =
      body.type === AiWebtoonSyntheticDataType.FluxContextImages
        ? 'fluxContextImages'
        : 'bLoraImages';

    const dataColumnImages = item[column];

    const invalidUuids = body.data.filter(
      (dataItem) =>
        !dataColumnImages.some(
          (existingItem) => existingItem.uuid === dataItem.uuid,
        ),
    );

    if (invalidUuids.length > 0) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_SYNTHETIC_DATA_UUID_NOT_FOUND,
      );
    }

    const dataColumnImagesUpdate = dataColumnImages.map((existingItem) => {
      const dataItem = body.data.find(
        (item) => item.uuid === existingItem.uuid,
      );
      if (dataItem) {
        return {
          ...existingItem,
          image: dataItem.image,
          prompt: dataItem.prompt || '',
        };
      }
      return existingItem;
    });

    await this.aiWebtoonSyntheticDataRepository.update(id, {
      [column]: dataColumnImagesUpdate,
    });

    const data: IAiWebtoonSyntheticDataSend = {
      syntheticDataId: id,
      pathReceived: '/ai-webtoon-synthetic-data/receive-generate-image',
      data: body.data.map((item) => ({
        uuid: item.uuid,
        image: item.image,
        prompt:
          body.type === AiWebtoonSyntheticDataType.BLoraImages
            ? ''
            : item.prompt,
      })),
      type: body.type,
    };

    if (body.type === AiWebtoonSyntheticDataType.BLoraImages) {
      const [character, trainingCharacterSession] = await Promise.all([
        this.aiWebtoonCharacterRepository.findOne({
          where: {
            id: body.characterId,
            type: EAiWebtoonCharacterType.B_LORA_STYLE,
          },
        }),
        this.aiWebtoonTrainingCharacterSessionRepository.findOne({
          where: {
            aiWebtoonCharacterId: body.characterId,
            status: EAiWebtoonTrainingCharacterSessionStatus.DONE,
          },
          order: {
            id: 'DESC',
          },
        }),
      ]);

      if (!character) {
        throw new BadRequestException(
          AI_MESSAGE_CONFIG.AI_SYNTHETIC_DATA_CHARACTER_NOT_FOUND,
        );
      }
      if (character.status !== EAiWebtoonCharacterStatus.READY) {
        throw new BadRequestException(
          AI_MESSAGE_CONFIG.AI_SYNTHETIC_DATA_CHARACTER_NOT_READY,
        );
      }
      if (!trainingCharacterSession) {
        throw new BadRequestException(
          AI_MESSAGE_CONFIG.AI_SYNTHETIC_DATA_CHARACTER_NOT_TRAINED,
        );
      }
      data.characterId = body.characterId;
      data.instancePrompt = trainingCharacterSession.descriptionPrompt || '';
    }

    const { apiName } =
      await this.aiService.sendGenerateSyntheticDataImagesRequest(data);

    await this.aiWebtoonSyntheticDataRepository.update(id, {
      apiGenName: apiName,
      status: EAiWebtoonBasicStatus.GENERATING,
    });

    return true;
  }

  async receiveGenerateImage(body: AiWebtoonSyntheticReceiveGenerateImageDto) {
    const { syntheticDataId, uuid, image, isDone, type, apiName } = body;

    this.logger.log(
      `🚀 Received SyntheticData Generate Images ~ data >> ${JSON.stringify(
        body,
      )}`,
    );
    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        apiName,
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Synthetic Data - Received Generated Image - ${body.syntheticDataId}`,
        data: body,
        isSuccess: true,
      },
    );

    const syntheticData = await this.aiWebtoonSyntheticDataRepository.findOne({
      where: {
        id: syntheticDataId,
      },
    });

    if (!syntheticData) {
      throw new NotFoundException(
        AI_MESSAGE_CONFIG.AI_SYNTHETIC_DATA_NOT_FOUND,
      );
    }

    if (syntheticData.status !== EAiWebtoonBasicStatus.GENERATING) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_SYNTHETIC_DATA_NOT_GENERATING,
      );
    }

    const column =
      type === AiWebtoonSyntheticDataType.FluxContextImages
        ? 'fluxContextImages'
        : 'bLoraImages';

    const dataColumnImages = syntheticData[column];
    const itemIndex = dataColumnImages.findIndex((item) => item.uuid === uuid);

    if (itemIndex === -1) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_SYNTHETIC_DATA_UUID_NOT_FOUND,
      );
    }

    dataColumnImages[itemIndex].resultImages.push(image);

    await this.aiWebtoonSyntheticDataRepository.update(syntheticDataId, {
      [column]: dataColumnImages,
      ...(isDone && {
        status: EAiWebtoonBasicStatus.GENERATED,
        apiGenName: null,
      }),
    });

    if (body.isDone) {
      await this.aiApiGenStateManagementService.updateItemsByNames(
        [body.apiName],
        false,
        null,
      );
    }

    // this.socketService.emitSyntheticData(syntheticDataId);

    return true;
  }

  async deleteImage(id: number, body: DeleteAiWebtoonSyntheticDataImageDto) {
    const item = await this.aiWebtoonSyntheticDataRepository.findOne({
      where: {
        id,
      },
    });

    if (!item) {
      throw new NotFoundException(
        AI_MESSAGE_CONFIG.AI_SYNTHETIC_DATA_NOT_FOUND,
      );
    }

    const { type, uuid } = body;
    const column =
      type === AiWebtoonSyntheticDataType.FluxContextImages
        ? 'fluxContextImages'
        : 'bLoraImages';

    const dataColumnImages = item[column];
    const itemIndex = dataColumnImages.findIndex((item) => item.uuid === uuid);

    if (itemIndex === -1) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_SYNTHETIC_DATA_UUID_NOT_FOUND,
      );
    }

    dataColumnImages.splice(itemIndex, 1);

    await this.aiWebtoonSyntheticDataRepository.update(id, {
      [column]: dataColumnImages,
    });

    return true;
  }
}
