import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CreateQADto {
  @ApiProperty({
    description: 'Customer service category ID',
    example: 1,
    required: false,
  })
  @IsInt({ message: 'customerServiceCategoryId must be an integer' })
  @Type(() => Number)
  @IsOptional()
  @Min(1, { message: 'customerServiceCategoryId must be at least 1' })
  customerServiceCategoryId?: number;

  @ApiProperty({
    description: 'Question title',
    example: 'Cannot access premium content after payment',
  })
  @IsString({ message: 'title must be a string' })
  title: string;

  @ApiProperty({
    description: 'Detailed question description',
    example:
      'I have paid for the premium subscription but still cannot access episode 15 of My Hero Academia. The payment was successful but the content shows as locked.',
  })
  @IsString({ message: 'description must be a string' })
  description: string;

  @ApiProperty({
    description: 'Array of file URLs for attachments',
    example: [
      'https://example.com/screenshot1.png',
      'https://example.com/receipt.pdf',
    ],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'files must be an array' })
  @IsUrl({}, { each: true, message: 'each file must be a valid URL' })
  files?: string[];
}
