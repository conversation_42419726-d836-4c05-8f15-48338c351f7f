import {
  <PERSON>,
  Get,
  Param,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { StatusService } from '../services/status.service';
import {
  UploadStatusResponseDto,
  ErrorResponseDto,
} from '../dto/upload-response.dto';

@ApiTags('Image Storage')
@Controller('api/v1/images/upload')
export class StatusController {
  private readonly logger = new Logger(StatusController.name);

  constructor(private readonly statusService: StatusService) {}

  @Get(':uploadId/status')
  @ApiOperation({
    summary: 'Check upload status',
    description: 'Get the current status and progress of an image upload.',
  })
  @ApiParam({
    name: 'uploadId',
    description: 'Unique upload identifier',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @ApiResponse({
    status: 200,
    description: 'Upload status retrieved successfully',
    type: UploadStatusResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Upload ID not found',
    type: ErrorResponseDto,
  })
  async getUploadStatus(
    @Param('uploadId') uploadId: string,
  ): Promise<UploadStatusResponseDto> {
    try {
      this.logger.log(`Checking status for upload: ${uploadId}`);

      const status = await this.statusService.getUploadStatus(uploadId);

      if (!status) {
        throw new HttpException(
          {
            success: false,
            error: {
              code: 'UPLOAD_NOT_FOUND',
              message: 'Upload ID not found',
              details: { uploadId },
            },
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return status;
    } catch (error) {
      this.logger.error(`Failed to get status for ${uploadId}:`, error);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        {
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Failed to retrieve upload status',
            details: error.message,
          },
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
