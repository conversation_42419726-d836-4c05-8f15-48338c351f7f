import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixMigration1753067460713 implements MigrationInterface {
  name = 'FixMigration1753067460713';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`episode\` ADD \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`episode\` DROP COLUMN \`status\``);
  }
}
