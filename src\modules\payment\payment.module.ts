import { Modu<PERSON> } from '@nestjs/common';
import { PaymentController } from './payment.controller';

import { PaymentManagementModule } from '../admin/payment-management/payment-management.module';
import { SubscriptionPlanService } from '../admin/payment-management/services/subscription-plan.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubscriptionPlanEntity } from 'src/database/entities/subscription_plan.entity';
import { SubscriptionPlanRepository } from '../../database/repositories/subscription-plan.repository';
import { AnalyticsQueueModule } from '../analytics/analytics-queue.module';

@Module({
  imports: [
    PaymentManagementModule,
    TypeOrmModule.forFeature([SubscriptionPlanEntity]),
    AnalyticsQueueModule,
  ],
  controllers: [PaymentController],
  providers: [SubscriptionPlanService, SubscriptionPlanRepository],
})
export class PaymentModule {}
