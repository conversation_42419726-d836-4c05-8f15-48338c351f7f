# Socket Module

Module WebSocket cho ứng dụng NestJS, cung cấp real-time communication giữa server và client.

## Tính năng

- ✅ WebSocket Gateway với Socket.IO
- ✅ Global Socket Service có thể sử dụng ở bất cứ đâu
- ✅ Retry mechanism cho reliable message delivery
- ✅ Client ready state management
- ✅ Ping/Pong health check
- ✅ CORS support

## Cấu trúc

```
src/modules/socket/
├── socket.module.ts      # Module chính (Global)
├── socket.gateway.ts     # WebSocket Gateway
├── socket.service.ts     # Service để emit events
├── examples/            # Ví dụ sử dụng
└── README.md           # Tài liệu này
```

## Cài đặt

Module đã được tích hợp vào `AppModule` và được đánh dấu là `@Global()`, có nghĩa là bạn có thể sử dụng `SocketService` ở bất cứ đâu mà không cần import `SocketModule`.

### Dependencies đã cài đặt:
- `@nestjs/websockets`
- `@nestjs/platform-socket.io`
- `socket.io`

## Sử dụng

### 1. Inject SocketService vào service/controller

```typescript
import { Injectable } from '@nestjs/common';
import { SocketService } from 'src/modules/socket/socket.service';
import { EAiGenPromptByDescriptionType } from 'src/common/ai-webtoon.enum';

@Injectable()
export class YourService {
  constructor(private readonly socketService: SocketService) {}

  async someMethod() {
    // Emit chapter update
    this.socketService.emitUpdateChapter(123);
    
    // Emit synthetic data update
    this.socketService.emitSyntheticData(456);
    
    // Emit prompt generation
    this.socketService.emitPromptByDescription(
      789, 
      EAiGenPromptByDescriptionType.GENERAL_PROMPT, 
      'Generated prompt text'
    );
    
    // Emit merged preview images
    this.socketService.emitMergePreviewImages(123, [
      'image1.jpg',
      'image2.jpg'
    ]);
  }
}
```

### 2. Các phương thức có sẵn

#### `emitUpdateChapter(chapterId: number)`
Gửi thông báo cập nhật chapter đến tất cả clients.

#### `emitSyntheticData(syntheticDataId: number)`
Gửi thông báo cập nhật synthetic data đến tất cả clients.

#### `emitPromptByDescription(cutId: number, typePrompt: EAiGenPromptByDescriptionType, prompt: string)`
Gửi prompt được generate đến tất cả clients.

#### `emitMergePreviewImages(chapterId: number, images: string[])`
Gửi danh sách preview images đã merge đến tất cả clients.

## Client-side Integration

### Kết nối từ client

```javascript
import { io } from 'socket.io-client';

const socket = io('http://localhost:3000', {
  cors: {
    origin: "*"
  }
});

// Đánh dấu client đã sẵn sàng
socket.emit('clientReady', {});

// Lắng nghe các events
socket.on('updateChapter', (data) => {
  console.log('Chapter updated:', data.chapterId);
});

socket.on('updateSyntheticData', (data) => {
  console.log('Synthetic data updated:', data.syntheticDataId);
});

socket.on('generatePromptByDescription', (data) => {
  console.log('Prompt generated:', data);
});

socket.on('mergePreviewImages', (data) => {
  console.log('Preview images merged:', data);
});

// Health check
socket.emit('ping');
socket.on('pong', (data) => {
  console.log('Pong received:', data);
});
```

## Configuration

WebSocket Gateway được cấu hình với:
- CORS: `origin: '*'` (cho phép tất cả origins)
- Retry mechanism: 10 lần thử với timeout 5 giây
- Client ready state tracking

## Logging

Module sử dụng NestJS Logger để ghi log:
- Connection/disconnection events
- Message delivery status
- Retry attempts
- Errors

## Ví dụ

Xem file `examples/socket-usage.example.ts` để biết cách sử dụng chi tiết.

## Troubleshooting

### 1. Client không nhận được messages
- Đảm bảo client đã emit `clientReady` event
- Kiểm tra connection status
- Xem logs để debug

### 2. CORS issues
- Cấu hình CORS trong gateway nếu cần
- Đảm bảo client origin được cho phép

### 3. Performance
- Module sử dụng retry mechanism, có thể điều chỉnh `maxRetries` và `timeoutMs` nếu cần
