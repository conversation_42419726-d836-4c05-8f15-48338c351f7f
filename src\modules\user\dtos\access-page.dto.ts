import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class AccessPageDto {
  @ApiProperty({
    description: 'Language code for analytics tracking',
    example: 'en',
    required: false,
  })
  @IsOptional()
  @IsString()
  language?: string;
}

export class AccessPageResponseDto {
  @ApiProperty({
    description: 'Indicates if the page access was tracked successfully',
    example: true,
  })
  success: boolean;
}
