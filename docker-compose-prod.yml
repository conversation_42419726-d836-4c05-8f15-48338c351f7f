version: '3.8'
services:
  app:
    container_name: trunk_app_prod
    build:
      args:
        - BUILD_ENV=prod
      context: .
      dockerfile: Dockerfile
    ports:
      - '3007:3005'
    depends_on:
      - db
      - redis
    networks:
      - nest-network
    restart: unless-stopped
    volumes:
      - ./uploads:/app/uploads
    environment:
      - TZ=Asia/Ho_Chi_Minh
    env_file:
      - .env.prod

  migration:
    container_name: trunk_migration_prod
    build:
      args:
        - BUILD_ENV=prod
      context: .
      dockerfile: Dockerfile
    depends_on:
      - db
    networks:
      - nest-network
    command: >
      sh -c "
        npm run migration:run:prod"
    restart: 'no'
    environment:
      - TZ=Asia/Ho_Chi_Minh
    env_file:
      - .env.prod

  db:
    container_name: trunk_db_prod
    image: 'mysql:8.0.19'
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    ports:
      - '33073:3306'
    volumes:
      - db-store:/var/lib/mysql
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost']
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - nest-network
    env_file:
      - .env.prod

  redis:
    container_name: trunk_redis_prod
    image: redis:latest
    ports:
      - '6379'
    restart: always
    volumes:
      - redis-store:/data
    networks:
      - nest-network
  mailhog:
    image: mailhog/mailhog
    container_name: mailhog_prod
    ports:
      - '1026:1025'
      - '8026:8025'
    networks:
      - nest-network
    env_file:
      - .env.prod

volumes:
  db-store:
  redis-store:

networks:
  nest-network:
    driver: bridge
