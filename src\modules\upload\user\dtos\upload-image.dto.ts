import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength } from 'class-validator';

export class UserUploadImageDto {
  @ApiProperty({
    description: 'The image file to upload',
    type: 'string',
    format: 'binary',
    required: true,
  })
  image: any;

  @ApiProperty({
    description: 'Optional description for the image',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;
}
