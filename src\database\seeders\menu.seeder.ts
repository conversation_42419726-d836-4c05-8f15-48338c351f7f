import { Seeder } from '@jorgebodega/typeorm-seeding';
import { DataSource } from 'typeorm';
import { MenuEntity } from '../entities/menu.entity';
import { MenuStatus } from '../../common/status.enum';

export class MenuSeeder extends Seeder {
  async run(datasource: DataSource) {
    const menuRepo = datasource.getRepository(MenuEntity);

    // No need to check total count, we'll check individual keys

    // Define menu data in flat structure (parents first, then children)
    const menuData = [
      // Parent menus
      {
        key: 'menu_customer_service',
        label: 'Customer Service',
        icon: 'fa',
        route: '/customer-service',
        order: 1,
        status: MenuStatus.ACTIVE,
        parentId: 0,
      },
      {
        key: 'menu_stat',
        label: 'Stats',
        icon: '',
        route: '/statistics',
        order: 1,
        status: MenuStatus.ACTIVE,
        parentId: 0,
      },
      {
        key: 'menu_system_config',
        label: 'System Configuration',
        icon: '',
        route: '/system-config',
        order: 2,
        status: MenuStatus.ACTIVE,
        parentId: 0,
      },
      {
        key: 'menu_admin_management',
        label: 'Admin Management',
        icon: '',
        route: '/admin-management',
        order: 3,
        status: MenuStatus.ACTIVE,
        parentId: 0,
      },
      {
        key: 'menu_user_management',
        label: 'User Management',
        icon: '/svg/menu-sidebar.svg',
        route: '/user-management',
        order: 4,
        status: MenuStatus.ACTIVE,
        parentId: 0,
      },
      {
        key: 'menu_contents',
        label: 'Contents',
        icon: '/svg/menu-sidebar.svg',
        route: '/contents',
        order: 5,
        status: MenuStatus.ACTIVE,
        parentId: 0,
      },
      {
        key: 'menu_transaction',
        label: 'Transaction',
        icon: 'fa-solid fa-home',
        route: '/transaction',
        order: 6,
        status: MenuStatus.ACTIVE,
        parentId: 0,
      },
    ];

    // Insert parent menus first
    console.log('\nCreating parent menus...');
    const parentMenus: MenuEntity[] = [];
    for (const menuItem of menuData) {
      // Check if menu with this key already exists
      const existingMenu = await menuRepo.findOne({
        where: { key: menuItem.key },
      });

      if (existingMenu) {
        console.log(`Parent menu already exists: ${existingMenu.label}`);
        parentMenus.push(existingMenu);
        continue;
      }

      // Create new menu
      const savedMenu = await menuRepo.save(menuItem);
      parentMenus.push(savedMenu);
      console.log(`Created parent menu: ${savedMenu.label}`);
    }

    // Create a map of key to id for parent menus
    const parentMenuMap = new Map<string, number>();
    parentMenus.forEach((menu) => {
      parentMenuMap.set(menu.key, menu.id);
    });

    // Define child menus with parent references
    const childMenuData = [
      // Customer Service children
      {
        key: 'menu_customer_service_qa',
        label: '1:1 Q&A',
        icon: 'fa-solid fa-home',
        route: '/customer-service/question-answer',
        order: 1,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_customer_service',
      },
      {
        key: 'menu_customer_service_',
        label: 'Notifications',
        icon: 'fa',
        route: '/customer-service/notifications',
        order: 2,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_customer_service',
      },
      {
        key: 'menu_customer_service_faq',
        label: 'FAQ',
        icon: 'fa-solid fa-home',
        route: '/customer-service/faq',
        order: 3,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_customer_service',
      },
      {
        key: 'menu_customer_service_delete_account',
        label: 'Delete Account',
        icon: 'fa-solid fa-home',
        route: '/customer-service/delete-account',
        order: 4,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_customer_service',
      },
      {
        key: 'menu_customer_service_term',
        label: 'Term of Use',
        icon: 'fa-solid fa-home',
        route: '/customer-service/term-of-use',
        order: 5,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_customer_service',
      },
      {
        key: 'menu_customer_service_privacy',
        label: 'Privacy Policy',
        icon: 'fa-solid fa-home',
        route: '/customer-service/privacy-policy',
        order: 6,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_customer_service',
      },
      {
        key: 'menu_customer_service_cookie',
        label: 'Cookie Settings',
        icon: 'fa-solid fa-home',
        route: '/customer-service/cookie-settings',
        order: 7,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_customer_service',
      },
      // Stats children
      {
        key: 'menu_overall_stat',
        label: 'Overall Stats',
        icon: '',
        route: '/statistics/overall-stats',
        order: 1,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_stat',
      },
      {
        key: 'menu_content-stat',
        label: 'Contents Stats',
        icon: '',
        route: '/statistics/content-stats',
        order: 2,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_stat',
      },
      {
        key: 'menu_referral_stats',
        label: 'Referral Stats',
        icon: '',
        route: '/statistics/referral-stats',
        order: 3,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_stat',
      },
      // System Configuration children
      {
        key: 'menu_language-management',
        label: 'Language Management',
        icon: '',
        route: '/system-config/language-management',
        order: 1,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_system_config',
      },
      {
        key: 'menu_subscription',
        label: 'Subscription',
        icon: '',
        route: '/system-config/subscription',
        order: 2,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_system_config',
      },
      {
        key: 'menu_system_config_setting',
        label: 'Menu Setting',
        icon: '',
        route: '/system-config/menu-setting',
        order: 3,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_system_config',
      },
      // Admin Management children
      {
        key: 'menu_admin_management_list',
        label: 'Admin List',
        icon: '',
        route: '/admin-management/admin-list',
        order: 1,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_admin_management',
      },
      // User Management children
      {
        key: 'menu_user_management_member_list',
        label: 'Member List',
        icon: '/svg/menu-sidebar.svg',
        route: '/user-management/member-list',
        order: 1,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_user_management',
      },
      // Contents children
      {
        key: 'menu_contents_comics_management',
        label: 'Comics Management',
        icon: '/svg/menu-sidebar.svg',
        route: '/contents/comics-management',
        order: 1,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_contents',
      },
      {
        key: 'menu_contents_author_management',
        label: 'Author Management',
        icon: '/svg/menu-sidebar.svg',
        route: '/contents/author-management',
        order: 2,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_contents',
      },
      // Transaction children
      {
        key: 'menu_transaction_payment_history',
        label: 'Payment History',
        icon: 'fa-solid fa-home',
        route: '/transaction/payment-history',
        order: 1,
        status: MenuStatus.ACTIVE,
        parentKey: 'menu_transaction',
      },
    ];

    // Insert child menus
    console.log('\nCreating child menus...');
    for (const childMenuItem of childMenuData) {
      const { parentKey, ...menuData } = childMenuItem;
      const parentId = parentMenuMap.get(parentKey);

      if (!parentId) {
        console.warn(`Parent menu not found for key: ${parentKey}`);
        continue;
      }

      // Check if child menu with this key already exists
      const existingChildMenu = await menuRepo.findOne({
        where: { key: menuData.key },
      });

      if (existingChildMenu) {
        console.log(
          `Child menu already exists: ${existingChildMenu.label} (parent: ${parentKey})`,
        );
        continue;
      }

      // Create new child menu
      const savedChildMenu = await menuRepo.save({
        ...menuData,
        parentId,
      });
      console.log(
        `Created child menu: ${savedChildMenu.label} (parent: ${parentKey})`,
      );
    }

    console.log('\nMenu seeder completed successfully');
  }
}
