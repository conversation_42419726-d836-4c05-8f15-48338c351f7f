import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'typeorm';
import { UserNotificationEntity } from './user-notification.entity';
import { UserEntity } from './user.entity';
import { DefaultEntity } from './default.entity';

@Entity('user_notification_recipient')
@Index(['userNotificationId', 'userId'], { unique: true })
export class UserNotificationRecipientEntity extends DefaultEntity {
  @Column({ name: 'user_notification_id' })
  userNotificationId: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ type: 'boolean', default: false })
  isRead: boolean;

  @ManyToOne(() => UserNotificationEntity)
  @JoinColumn({ name: 'user_notification_id' })
  userNotification: UserNotificationEntity;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;
}
