import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNotification1753155449960 implements MigrationInterface {
  name = 'AddNotification1753155449960';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`user_notification\` (\`id\` int NOT NULL AUTO_INCREMENT, \`name\` varchar(255) NOT NULL DEFAULT 'root', \`title\` varchar(255) NOT NULL, \`startTime\` bigint NOT NULL COMMENT 'Unix timestamp for start display time (from 00:00)', \`endTime\` bigint NOT NULL COMMENT 'Unix timestamp for end display time (until 23:59)', \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active', \`content\` text NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`system_notification\` (\`id\` int NOT NULL AUTO_INCREMENT, \`name\` varchar(255) NOT NULL DEFAULT 'root', \`title\` varchar(255) NOT NULL, \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active', \`content\` text NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`user_notification_recipient\` (\`id\` int NOT NULL AUTO_INCREMENT, \`user_notification_id\` int NOT NULL, \`user_id\` int NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), UNIQUE INDEX \`IDX_7f71c3c45927785a329b39b400\` (\`user_notification_id\`, \`user_id\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`notification_view\` (\`id\` int NOT NULL AUTO_INCREMENT, \`user_id\` int NOT NULL, \`notification_type\` enum ('system', 'user') NOT NULL, \`notification_id\` int NOT NULL, \`viewedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), UNIQUE INDEX \`IDX_ce4eacb9e73554668c616c26d8\` (\`user_id\`, \`notification_type\`, \`notification_id\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`user_notification_recipient\` ADD CONSTRAINT \`FK_76e24dd6e4c1b5bbfee0fbc03a9\` FOREIGN KEY (\`user_notification_id\`) REFERENCES \`user_notification\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`user_notification_recipient\` ADD CONSTRAINT \`FK_a57875b6222cdcecea216eabba0\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`notification_view\` ADD CONSTRAINT \`FK_fd7f4bf209a97247e6633a01e75\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`notification_view\` DROP FOREIGN KEY \`FK_fd7f4bf209a97247e6633a01e75\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`user_notification_recipient\` DROP FOREIGN KEY \`FK_a57875b6222cdcecea216eabba0\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`user_notification_recipient\` DROP FOREIGN KEY \`FK_76e24dd6e4c1b5bbfee0fbc03a9\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_ce4eacb9e73554668c616c26d8\` ON \`notification_view\``,
    );
    await queryRunner.query(`DROP TABLE \`notification_view\``);
    await queryRunner.query(
      `DROP INDEX \`IDX_7f71c3c45927785a329b39b400\` ON \`user_notification_recipient\``,
    );
    await queryRunner.query(`DROP TABLE \`user_notification_recipient\``);
    await queryRunner.query(`DROP TABLE \`system_notification\``);
    await queryRunner.query(`DROP TABLE \`user_notification\``);
  }
}
