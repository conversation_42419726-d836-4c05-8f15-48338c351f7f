import { Module } from '@nestjs/common';
import { LegalAdminController } from './controllers/legal-admin.controller';
import { LegalAdminService } from './services/legal-admin.service';
import { LegalDocumentRepository } from '../../../database/repositories/legal-document.repository';
import { MenuModule } from '../menu/menu.module';

@Module({
  imports: [MenuModule],
  controllers: [LegalAdminController],
  providers: [LegalAdminService, LegalDocumentRepository],
  exports: [LegalAdminService],
})
export class LegalAdminModule {}
