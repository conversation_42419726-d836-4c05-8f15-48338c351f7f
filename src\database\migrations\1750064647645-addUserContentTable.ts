import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserContentTable1750064647645 implements MigrationInterface {
  name = 'AddUserContentTable1750064647645';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`user_content\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`type\` enum ('recently_read') NOT NULL DEFAULT 'recently_read', \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active', \`userId\` int NULL, \`contentId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`user_content\` ADD CONSTRAINT \`FK_5e01549c1f778a93e98f030d9c2\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`user_content\` ADD CONSTRAINT \`FK_de0c7536d96c8ae701cfe8374de\` FOREIGN KEY (\`contentId\`) REFERENCES \`content\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`user_content\` DROP FOREIGN KEY \`FK_de0c7536d96c8ae701cfe8374de\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`user_content\` DROP FOREIGN KEY \`FK_5e01549c1f778a93e98f030d9c2\``,
    );
    await queryRunner.query(`DROP TABLE \`user_content\``);
  }
}
