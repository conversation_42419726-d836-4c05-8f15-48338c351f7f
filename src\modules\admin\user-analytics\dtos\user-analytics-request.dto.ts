import { IsOptional, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UserAnalyticsQueryDto {
  @ApiProperty({
    description: 'Start date for analytics (YYYY-MM-DD)',
    example: '2025-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    description: 'End date for analytics (YYYY-MM-DD)',
    example: '2025-01-31',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;
}

export class HourlyAnalyticsQueryDto {
  @ApiProperty({
    description: 'Specific date for hourly analytics (YYYY-MM-DD)',
    example: '2025-01-18',
    required: true,
  })
  @IsDateString()
  date: string;
}

export class MonthlyAnalyticsQueryDto {
  @ApiProperty({
    description: 'Start month for analytics (YYYY-MM)',
    example: '2025-01',
    required: true,
  })
  @IsDateString()
  startMonth: string;

  @ApiProperty({
    description: 'End month for analytics (YYYY-MM)',
    example: '2025-03',
    required: true,
  })
  @IsDateString()
  endMonth: string;
}
