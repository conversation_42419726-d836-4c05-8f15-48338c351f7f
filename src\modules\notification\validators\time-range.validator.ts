import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';

@ValidatorConstraint({ name: 'IsTimeRangeValid', async: false })
export class IsTimeRangeValid implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments): boolean {
    const object = args.object as any;

    // If both startTime and endTime are provided, validate the range
    if (object.startTime !== undefined && object.endTime !== undefined) {
      return object.startTime < object.endTime;
    }

    // If only one is provided, we cannot validate the range
    // Individual field validation will handle other cases
    return true;
  }

  defaultMessage(): string {
    return 'startTime must be less than endTime';
  }
}
