import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveIndexEmailAdmin1747815026358 implements MigrationInterface {
  name = 'RemoveIndexEmailAdmin1747815026358';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_051db7d37d478a69a7432df147\` ON \`admins\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`admins\` ADD \`username\` varchar(255) NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`admins\` DROP COLUMN \`username\``);
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`IDX_051db7d37d478a69a7432df147\` ON \`admins\` (\`email\`)`,
    );
  }
}
