import { MigrationInterface, QueryRunner } from "typeorm";

export class FixAnalyticsV21754936371524 implements MigrationInterface {
    name = 'FixAnalyticsV21754936371524'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user_analytics_v2\` CHANGE \`language\` \`language\` int NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_61b43dbec29f74d4d6a11ae222\` ON \`user_analytics_v2\` (\`date\`, \`hour\`, \`language\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_61b43dbec29f74d4d6a11ae222\` ON \`user_analytics_v2\``);
        await queryRunner.query(`ALTER TABLE \`user_analytics_v2\` CHANGE \`language\` \`language\` int NOT NULL DEFAULT '0'`);
    }

}
