import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeQAtable1753342132568 implements MigrationInterface {
  name = 'ChangeQAtable1753342132568';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`support_response\` CHANGE \`status\` \`status\` enum ('unanswered', 'pending', 'processing', 'complete', 'complete_add', 'automatedReply') NOT NULL DEFAULT 'pending'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`support_response\` CHANGE \`status\` \`status\` enum ('draft', 'sent', 'auto_reply') NOT NULL DEFAULT 'sent'`,
    );
  }
}
