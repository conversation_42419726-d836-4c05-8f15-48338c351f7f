import {
  IsString,
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ength,
  Matches,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import {
  LegalDocumentStatus,
  LegalDocumentType,
} from '../../../../database/entities/legal-document.entity';

export class CreateLegalDocumentDto {
  @ApiProperty({ description: 'Legal document title' })
  @IsString()
  @MaxLength(255)
  title: string;

  @ApiProperty({
    description: 'Unique identifier name (URL-friendly)',
    example: 'terms-of-use',
  })
  @IsString()
  @MaxLength(100)
  @Matches(/^[a-z0-9-]+$/, {
    message: 'Name must contain only lowercase letters, numbers, and hyphens',
  })
  name: string;

  @ApiProperty({ enum: LegalDocumentType })
  @IsEnum(LegalDocumentType)
  type: LegalDocumentType;

  @ApiProperty({ description: 'Legal document content (HTML allowed)' })
  @IsString()
  content: string;

  @ApiProperty({
    enum: LegalDocumentStatus,
    default: LegalDocumentStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(LegalDocumentStatus)
  status?: LegalDocumentStatus;

  @ApiProperty({
    description: 'Document version',
    default: '1.0',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  version?: string;
}
