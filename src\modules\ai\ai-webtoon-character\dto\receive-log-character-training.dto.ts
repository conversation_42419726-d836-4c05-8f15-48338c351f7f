import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsInt, IsNumber, IsOptional, IsString, Min } from 'class-validator';

export class ReceiveLogCharacterTrainingDto {
  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  characterId: number;

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  loss: number;

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  percent: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  url?: string;
}
