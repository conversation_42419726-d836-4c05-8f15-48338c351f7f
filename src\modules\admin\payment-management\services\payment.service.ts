import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PaymentRepository } from '../../../../database/repositories/payment.repository';
import { CreatePaymentDto } from '../dto/create-payment.dto';
import { UpdatePaymentDto } from '../dto/update-payment.dto';
import { PaymentEntity } from '../../../../database/entities/payment.entity';
import { CommonStatus, PaymentStatus } from '../../../../common/status.enum';
import { CreatePaymentIntentDto } from '../../../payment/dtos/create-payment-intent.dto';
import { SubscriptionPlanRepository } from '../../../../database/repositories/subscription-plan.repository';
import { v4 as uuidv4 } from 'uuid';
import { SubscriptionRepository } from '../../../../database/repositories/subscription.repository';
import * as moment from 'moment';
import { plainToInstance } from 'class-transformer';
import { SubscriptionPlanEntity } from '../../../../database/entities/subscription_plan.entity';
import { SubscriptionEntity } from '../../../../database/entities/subscription.entity';
import { MESSAGE_CONFIG } from '../../../../common/message.config';
import {
  PaymentHistoryQueryDto,
  PurchaseType,
  ProductType,
  PaymentPlatform,
  PaymentMethodType,
  PaymentHistoryStatus,
  SearchType,
} from '../dto/payment-history-query.dto';
import {
  PaymentHistoryResponseDto,
  PaymentHistoryItemDto,
} from '../dto/payment-history-response.dto';
import { Platform } from '../../../../common/common.config';
import { CommonService } from '../../../../common/common.service';

@Injectable()
export class PaymentService {
  constructor(
    private readonly paymentRepository: PaymentRepository,
    private readonly subscriptionPlanRepository: SubscriptionPlanRepository,
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly commonService: CommonService,
  ) {}

  async create(createPaymentDto: CreatePaymentDto): Promise<PaymentEntity> {
    const payment = this.paymentRepository.create({
      ...createPaymentDto,
      status: createPaymentDto.status || PaymentStatus.PENDING,
      subscription: { id: createPaymentDto.subscriptionId },
    });

    return await this.paymentRepository.save(payment);
  }

  async findAll(): Promise<PaymentEntity[]> {
    return await this.paymentRepository.find({
      relations: [
        'subscription',
        'subscription.user',
        'subscription.subscriptionPlan',
      ],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: number): Promise<PaymentEntity> {
    const payment = await this.paymentRepository.findOne({
      where: { id },
      relations: [
        'subscription',
        'subscription.user',
        'subscription.subscriptionPlan',
      ],
    });

    if (!payment) {
      throw new NotFoundException(`Payment with ID ${id} not found`);
    }

    return payment;
  }

  async update(
    id: number,
    updatePaymentDto: UpdatePaymentDto,
  ): Promise<PaymentEntity> {
    const payment = await this.findOne(id);

    Object.assign(payment, updatePaymentDto);

    // Update subscription relation if provided
    if (updatePaymentDto.subscriptionId) {
      payment.subscription = { id: updatePaymentDto.subscriptionId } as any;
    }

    return await this.paymentRepository.save(payment);
  }

  async remove(id: number): Promise<void> {
    const payment = await this.findOne(id);
    await this.paymentRepository.remove(payment);
  }

  async findByStatus(status: PaymentStatus): Promise<PaymentEntity[]> {
    return await this.paymentRepository.find({
      where: { status },
      relations: [
        'subscription',
        'subscription.user',
        'subscription.subscriptionPlan',
      ],
      order: { createdAt: 'DESC' },
    });
  }

  async findBySubscription(subscriptionId: number): Promise<PaymentEntity[]> {
    return await this.paymentRepository.find({
      where: { subscription: { id: subscriptionId } },
      relations: ['subscription'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByProviderTransactionId(
    providerTransactionId: string,
  ): Promise<PaymentEntity | null> {
    return await this.paymentRepository.findOne({
      where: { providerTransactionId },
      relations: [
        'subscription',
        'subscription.user',
        'subscription.subscriptionPlan',
      ],
    });
  }

  /**
   * Check if user can subscribe to a plan and determine the action needed
   */
  private async checkSubscriptionEligibility(
    userId: number,
    planId: number,
  ): Promise<{
    canSubscribe: boolean;
    action: 'create_new' | 'upgrade' | 'renew' | 'block_same_plan';
    currentSubscription?: SubscriptionEntity;
  }> {
    // Get the most recent subscription for the user
    const currentSubscription = await this.subscriptionRepository.findOne({
      where: { user: { id: userId } },
      order: { expiresAt: 'DESC' },
      relations: ['subscriptionPlan'],
    });

    // No previous subscription - allow new subscription
    if (!currentSubscription) {
      return { canSubscribe: true, action: 'create_new' };
    }

    const now = moment().valueOf();
    const isExpired = currentSubscription.expiresAt <= now;
    const isActive = currentSubscription.status === CommonStatus.ACTIVE;
    const samePlan = currentSubscription.subscriptionPlan.id === planId;

    // Active and not expired subscription
    if (isActive && !isExpired) {
      if (samePlan) {
        // Same plan while active - block
        return {
          canSubscribe: false,
          action: 'block_same_plan',
          currentSubscription,
        };
      } else {
        // Different plan while active - allow upgrade/downgrade
        return {
          canSubscribe: true,
          action: 'upgrade',
          currentSubscription,
        };
      }
    } else {
      // Expired or inactive subscription - allow renewal
      return {
        canSubscribe: true,
        action: 'renew',
        currentSubscription,
      };
    }
  }

  async hasSuccessfulPayments(userId: number): Promise<boolean> {
    const count = await this.paymentRepository.count({
      where: {
        user: { id: userId },
        status: PaymentStatus.SUCCESS,
      },
    });
    return count > 0;
  }

  async markAsSuccessful(
    id: number,
    providerTransactionId?: string,
    providerTransactionStatus?: string,
  ): Promise<PaymentEntity> {
    const payment = await this.findOne(id);
    payment.status = PaymentStatus.SUCCESS;

    if (providerTransactionId) {
      payment.providerTransactionId = providerTransactionId;
    }

    if (providerTransactionStatus) {
      payment.providerTransactionStatus = providerTransactionStatus;
    }

    return await this.paymentRepository.save(payment);
  }

  async markAsFailed(
    id: number,
    providerTransactionStatus?: string,
  ): Promise<PaymentEntity> {
    const payment = await this.findOne(id);
    payment.status = PaymentStatus.FAILED;

    if (providerTransactionStatus) {
      payment.providerTransactionStatus = providerTransactionStatus;
    }

    return await this.paymentRepository.save(payment);
  }

  async markAsCanceled(id: number): Promise<PaymentEntity> {
    const payment = await this.findOne(id);
    payment.status = PaymentStatus.CANCELLED;
    return await this.paymentRepository.save(payment);
  }

  async getTotalRevenueByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<number> {
    const result = await this.paymentRepository
      .createQueryBuilder('payment')
      .select('SUM(payment.amount)', 'total')
      .where('payment.status = :status', { status: PaymentStatus.SUCCESS })
      .andWhere('payment.createdAt >= :startDate', { startDate })
      .andWhere('payment.createdAt <= :endDate', { endDate })
      .getRawOne();

    return parseFloat(result.total) || 0;
  }

  async getPaymentStatistics(): Promise<{
    total: number;
    successful: number;
    failed: number;
    pending: number;
    canceled: number;
    totalRevenue: number;
  }> {
    const [total, successful, failed, pending, canceled] = await Promise.all([
      this.paymentRepository.count(),
      this.paymentRepository.count({
        where: { status: PaymentStatus.SUCCESS },
      }),
      this.paymentRepository.count({ where: { status: PaymentStatus.FAILED } }),
      this.paymentRepository.count({
        where: { status: PaymentStatus.PENDING },
      }),
      this.paymentRepository.count({
        where: { status: PaymentStatus.CANCELLED },
      }),
    ]);

    const revenueResult = await this.paymentRepository
      .createQueryBuilder('payment')
      .select('SUM(payment.amount)', 'total')
      .where('payment.status = :status', { status: PaymentStatus.SUCCESS })
      .getRawOne();

    const totalRevenue = parseFloat(revenueResult.total) || 0;

    return {
      total,
      successful,
      failed,
      pending,
      canceled,
      totalRevenue,
    };
  }

  async createPaymentIntent(
    body: CreatePaymentIntentDto,
    userId: number,
    req: any,
  ): Promise<any> {
    try {
      const platform = this.commonService.detectPlatform(req);
      console.log(platform);

      // Check subscription eligibility
      const eligibility = await this.checkSubscriptionEligibility(
        userId,
        body.planId,
      );

      if (!eligibility.canSubscribe) {
        throw new BadRequestException(
          eligibility.action === 'block_same_plan'
            ? MESSAGE_CONFIG.USER_ALREADY_HAS_ACTIVE_SUBSCRIPTION
            : 'Cannot subscribe to this plan',
        );
      }

      return await this.subscriptionRepository.manager.transaction(
        async (transaction) => {
          const { planId, currency, paymentMethod } = body;

          const plan = await transaction.findOne(SubscriptionPlanEntity, {
            where: { id: planId, status: CommonStatus.ACTIVE },
          });

          if (!plan) {
            throw new NotFoundException('Subscription plan not found');
          }

          if (plan.currency !== currency) {
            throw new BadRequestException('Currency mismatch');
          }

          if (plan.price === 0) {
            return {
              clientSecret: 'free_plan',
              plan,
            };
          }

          // Handle existing subscription based on eligibility action
          if (
            eligibility.action === 'upgrade' &&
            eligibility.currentSubscription
          ) {
            // Deactivate current subscription for upgrade/downgrade
            eligibility.currentSubscription.status = CommonStatus.INACTIVE;
            await transaction.save(eligibility.currentSubscription);
          } else if (
            eligibility.action === 'renew' &&
            eligibility.currentSubscription
          ) {
            // Update expired subscription status
            eligibility.currentSubscription.status = CommonStatus.INACTIVE;
            await transaction.save(eligibility.currentSubscription);
          }

          const idempotencyKey = uuidv4();

          const subscription = transaction.create(SubscriptionEntity, {
            subscriptionPlan: { id: plan.id },
            user: { id: userId },
            status: CommonStatus.ACTIVE,
            startedAt: moment().valueOf(),
            expiresAt: moment().add(plan.durationDays, 'days').valueOf(),
            currency,
            autoRenew: true,
            isTrial: false,
            trialDays: 0,
          });

          const savedSubscription = await transaction.save(subscription);

          let ip = req?.ip?.replace('::ffff:', '');
          ip = ip?.split(':').shift();

          const paymentIntent = transaction.create(PaymentEntity, {
            amount: plan.price,
            currency,
            languageId: body.languageId,
            method: paymentMethod,
            idempotencyKey,
            status: PaymentStatus.SUCCESS,
            subscription: savedSubscription,
            user: { id: userId },
            ip,
            platform:
              platform.platform === 'mobile' ? Platform.MOBILE : Platform.PC,
          });

          await transaction.save(paymentIntent);

          return plainToInstance(PaymentEntity, paymentIntent);
        },
      );
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.message);
    }
  }

  async getPaymentHistory(
    query: PaymentHistoryQueryDto,
  ): Promise<PaymentHistoryResponseDto> {
    const {
      page = 1,
      limit = 20,
      startDate,
      endDate,
      purchaseType,
      productType,
      platform,
      paymentType,
      language,
      paymentStatus,
      email,
      searchType = SearchType.ID,
      search,
    } = query;

    const offset = (page - 1) * limit;

    // Build query with joins
    const queryBuilder = this.paymentRepository
      .createQueryBuilder('payment')
      .leftJoinAndSelect('payment.user', 'user')
      .leftJoinAndSelect('payment.subscription', 'subscription')
      .leftJoinAndSelect('subscription.subscriptionPlan', 'subscriptionPlan')
      .where('payment.deletedAt IS NULL');

    // Date range filters
    if (startDate) {
      queryBuilder.andWhere('DATE(payment.createdAt) >= :startDate', {
        startDate,
      });
    }
    if (endDate) {
      queryBuilder.andWhere('DATE(payment.createdAt) <= :endDate', { endDate });
    }

    // Purchase type filter (First Purchase vs Re-Purchase)
    if (purchaseType && purchaseType !== PurchaseType.ALL) {
      if (purchaseType === PurchaseType.FIRST_PURCHASE) {
        queryBuilder.andWhere('user.totalPurchases = 1');
      } else if (purchaseType === PurchaseType.RE_PURCHASE) {
        queryBuilder.andWhere('user.totalPurchases > 1');
      }
    }

    // Product type filter (Subscription vs Coin)
    if (productType && productType !== ProductType.ALL) {
      if (productType === ProductType.SUBSCRIPTION) {
        queryBuilder.andWhere('subscription.id IS NOT NULL');
      } else if (productType === ProductType.COIN) {
        queryBuilder.andWhere('subscription.id IS NULL');
      }
    }

    // Platform filter
    if (platform && platform !== PaymentPlatform.ALL) {
      const platformValue =
        platform === PaymentPlatform.MOBILE ? Platform.MOBILE : Platform.PC;
      queryBuilder.andWhere('payment.platform = :platform', {
        platform: platformValue.toLowerCase(),
      });
    }

    // Payment method filter
    if (paymentType && paymentType !== PaymentMethodType.ALL) {
      const methodValue =
        paymentType === PaymentMethodType.CREDIT_CARD ? 'stripe' : 'paypal';
      queryBuilder.andWhere('LOWER(payment.method) LIKE :method', {
        method: `%${methodValue.toLowerCase()}%`,
      });
    }

    // Language filter (based on user settings - simplified for now)
    if (language) {
      queryBuilder.andWhere('payment.languageId = :language', {
        language,
      });
    }

    // Payment status filter
    if (paymentStatus && paymentStatus !== PaymentHistoryStatus.ALL) {
      if (paymentStatus === PaymentHistoryStatus.NORMAL) {
        queryBuilder.andWhere('payment.status = :status', {
          status: PaymentStatus.SUCCESS,
        });
      } else if (paymentStatus === PaymentHistoryStatus.CANCELLED) {
        queryBuilder.andWhere('payment.status IN (:...statuses)', {
          statuses: [PaymentStatus.CANCELLED, PaymentStatus.FAILED],
        });
      }
    }

    // Email filter (separate from search)
    if (email) {
      queryBuilder.andWhere('LOWER(user.email) = :email', {
        email: email.toLowerCase(),
      });
    }

    // Enhanced search filter based on search type
    if (search && searchType) {
      const searchValue = search.toLowerCase();
      switch (searchType) {
        case SearchType.ID:
          queryBuilder.andWhere('payment.id = :searchId', {
            searchId: parseInt(search) || 0,
          });
          break;
        case SearchType.REF:
          queryBuilder.andWhere('LOWER(user.ref) LIKE :searchRef', {
            searchRef: `%${searchValue}%`,
          });
          break;
        case SearchType.PURCHASE_IP:
          queryBuilder.andWhere('LOWER(payment.ip) LIKE :searchIp', {
            searchIp: `%${searchValue}%`,
          });
          break;
        case SearchType.TRANSACTION_ID:
          queryBuilder.andWhere(
            'LOWER(payment.providerTransactionId) LIKE :searchTransactionId',
            {
              searchTransactionId: `%${searchValue}%`,
            },
          );
          break;
        default:
          // Fallback to ID search if searchType is not recognized
          queryBuilder.andWhere('payment.id = :searchId', {
            searchId: parseInt(search) || 0,
          });
          break;
      }
    }

    // Clone query builder to get count and total amount separately
    const countQuery = queryBuilder.clone();
    const totalCount = await countQuery.getCount();

    // Get total amount for all filtered records using a separate query
    const amountQuery = queryBuilder.clone();
    const totalAmountResult = await amountQuery
      .select('SUM(payment.amount)', 'totalAmount')
      .getRawOne();
    const totalAmount = parseFloat(totalAmountResult.totalAmount) || 0;

    // Apply pagination and ordering for main data query
    const payments = await queryBuilder
      .orderBy('payment.id', 'DESC')
      .skip(offset)
      .take(limit)
      .getMany();

    // Transform data to response format
    const paymentItems: PaymentHistoryItemDto[] = payments.map(
      (payment, index) => {
        const sequenceNumber = totalCount - offset - index;
        const isPurchaseFirst = payment.user?.totalPurchases === 1;
        const isSubscription = !!payment.subscription;
        const userPlatform = payment.user?.platform || Platform.PC;
        const paymentMethod = payment.method || 'Unknown';

        // Map payment method to display format
        let displayPaymentMethod = 'Unknown';
        if (
          paymentMethod.toLowerCase().includes('stripe') ||
          paymentMethod.toLowerCase().includes('card')
        ) {
          displayPaymentMethod = 'Credit Card';
        } else if (paymentMethod.toLowerCase().includes('paypal')) {
          displayPaymentMethod = 'Paypal';
        }

        // Map payment status to display format
        let displayStatus = 'Normal';
        if (
          payment.status === PaymentStatus.CANCELLED ||
          payment.status === PaymentStatus.FAILED
        ) {
          displayStatus = 'Cancelled';
        }

        // Get payment IP from payment record
        const paymentIp = payment.ip || 'Unknown';

        return {
          no: sequenceNumber,
          purchaseType: isPurchaseFirst ? 'First Purchase' : 'Re-Purchase',
          productType: isSubscription ? 'Subscription' : 'Coin',
          ref: payment.user?.ref || '',
          paymentId: payment.user?.email || '',
          paymentAmount: parseFloat(payment.amount.toString()),
          signUpDate: payment.user?.createdAt?.toISOString() || '',
          purchaseDate: payment.createdAt.toISOString(),
          platform: userPlatform === Platform.MOBILE ? 'Mobile' : 'PC',
          paymentType: displayPaymentMethod,
          language: 'English', // Placeholder - would need to implement language detection
          paymentIp,
          transactionId: payment.providerTransactionId || payment.id.toString(),
          paymentStatus: displayStatus,
          userId: payment.user?.id,
        };
      },
    );

    const totalPages = Math.ceil(totalCount / limit);

    return {
      payments: paymentItems,
      total: totalCount,
      page,
      limit,
      totalPages,
      totalAmount,
    };
  }
}
