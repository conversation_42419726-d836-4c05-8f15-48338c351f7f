import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { EAiApiGenStateManagementType } from 'src/common/ai-webtoon.enum';
import { MESSAGE_CONFIG } from 'src/common/message.config';
import { AiApiGenStateManagementEntity } from 'src/database/entities/ai-api-gen-state-management.entity';
import { AiApiGenStateManagementRepository } from 'src/database/repositories/ai-api-gen-state-management.repository';
import { In } from 'typeorm';

@Injectable()
export class AiApiGenStateManagementService {
  constructor(
    private aiApiGenStateManagementRepository: AiApiGenStateManagementRepository,
    // private cacheService: CacheService,
  ) {}
  private readonly logger = new Logger(AiApiGenStateManagementService.name);
  private readonly LOCKED_API_CACHE_KEY = 'ai-service:locked-apis';

  // private async getLockedApiNames(): Promise<Set<string>> {
  //   const lockedApis = await this.cacheService.get(this.LOCKED_API_CACHE_KEY);
  //   return lockedApis ? new Set(lockedApis) : new Set<string>();
  // }

  // private async addLockedApiName(apiName: string): Promise<void> {
  //   const lockedApis = await this.getLockedApiNames();
  //   lockedApis.add(apiName);
  //   await this.cacheService.set(
  //     this.LOCKED_API_CACHE_KEY,
  //     Array.from(lockedApis),
  //     300,
  //   );
  //   this.logger.debug(
  //     `🚀 Locked API: ${apiName}, Total locked: ${lockedApis.size}`,
  //   );
  // }

  // async removeLockedApiName(apiName: string): Promise<void> {
  //   const lockedApis = await this.getLockedApiNames();
  //   lockedApis.delete(apiName);
  //   if (lockedApis.size > 0) {
  //     await this.cacheService.set(
  //       this.LOCKED_API_CACHE_KEY,
  //       Array.from(lockedApis),
  //       300,
  //     );
  //   } else {
  //     await this.cacheService.del(this.LOCKED_API_CACHE_KEY);
  //   }
  //   this.logger.debug(
  //     `🚀 Unlocked API: ${apiName}, Total locked: ${lockedApis.size}`,
  //   );
  // }

  async findAllByType(
    type: EAiApiGenStateManagementType,
  ): Promise<AiApiGenStateManagementEntity[]> {
    return this.aiApiGenStateManagementRepository.find({
      where: {
        type,
        isMaintenance: false,
      },
    });
  }

  async findOneByName(name: string): Promise<AiApiGenStateManagementEntity> {
    const result = await this.aiApiGenStateManagementRepository.findOne({
      where: {
        name,
      },
    });

    if (!result) throw new NotFoundException(MESSAGE_CONFIG.SERVICE_NOT_FOUND);

    return result;
  }

  async findOneApiGenByType(
    type: EAiApiGenStateManagementType,
  ): Promise<AiApiGenStateManagementEntity> {
    const queryBuilder = this.aiApiGenStateManagementRepository
      .createQueryBuilder('apiGen')
      .where('apiGen.type = :type', { type })
      .andWhere('apiGen.isMaintenance = false')
      .andWhere('apiGen.status = false');

    // const lockedApiNames = await this.getLockedApiNames();
    // if (lockedApiNames.size > 0) {
    //   queryBuilder.andWhere('apiGen.name NOT IN (:...lockedNames)', {
    //     lockedNames: Array.from(lockedApiNames),
    //   });
    // }

    const result = await queryBuilder.orderBy('RAND()').getOne();

    if (!result) {
      // const lockedCount = lockedApiNames.size;
      throw new BadRequestException(MESSAGE_CONFIG.SERVICE_BUSY);
    }

    // await this.addLockedApiName(result.name);
    return result;
  }

  async updateItemsByNames(
    names: string[],
    status: boolean,
    startTime: Date | null,
  ) {
    await this.aiApiGenStateManagementRepository.update(
      {
        name: In(names),
      },
      { status, startTime },
    );
  }

  async updateIsMaintenanceById(id: number, isMaintenance: boolean) {
    await this.aiApiGenStateManagementRepository.update(
      {
        id,
      },
      { isMaintenance },
    );
  }

  async checkIsWorkingByName(name: string) {
    const result = await this.aiApiGenStateManagementRepository.find({
      where: {
        name,
        isMaintenance: false,
      },
    });

    if (!result)
      throw new BadRequestException(MESSAGE_CONFIG.SERVICE_MAINTENANCE);

    return result;
  }
}
